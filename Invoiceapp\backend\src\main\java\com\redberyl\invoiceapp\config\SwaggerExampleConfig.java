package com.redberyl.invoiceapp.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.examples.Example;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.parameters.RequestBody;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerExampleConfig {

    @Bean
    public OpenApiCustomizer taxRateExampleCustomizer() {
        return openApi -> {
            // Create a simple example for tax rate creation
            Example taxRateExample = new Example();
            taxRateExample.setValue("{\n" +
                    "  \"taxTypeId\": 1,\n" +
                    "  \"rate\": 18.0,\n" +
                    "  \"effectiveFrom\": \"2025-04-17\",\n" +
                    "  \"effectiveTo\": \"2025-06-22\"\n" +
                    "}");
            taxRateExample.setSummary("Simple Tax Rate Example");
            taxRateExample.setDescription("Example of creating a tax rate with only the required fields");

            // Find the POST operation for tax rates
            openApi.getPaths().forEach((path, pathItem) -> {
                if (path.contains("/tax-rates") && pathItem.getPost() != null) {
                    RequestBody requestBody = pathItem.getPost().getRequestBody();
                    if (requestBody != null && requestBody.getContent() != null) {
                        Content content = requestBody.getContent();
                        MediaType mediaType = content.get("application/json");
                        if (mediaType != null) {
                            // Add our example
                            mediaType.addExamples("simpleExample", taxRateExample);

                            // Set it as the default example
                            Schema schema = mediaType.getSchema();
                            if (schema != null) {
                                schema.setExample("{\n" +
                                        "  \"taxTypeId\": 1,\n" +
                                        "  \"rate\": 18.0,\n" +
                                        "  \"effectiveFrom\": \"2025-04-17\",\n" +
                                        "  \"effectiveTo\": \"2025-06-22\"\n" +
                                        "}");
                            }
                        }
                    }
                }
            });
        };
    }

    @Bean
    public OpenApiCustomizer clientExampleCustomizer() {
        return openApi -> {
            // Create a simple example for client creation
            Example clientExample = new Example();
            clientExample.setValue("{\n" +
                    "  \"name\": \"ABC Technologies Pvt Ltd\",\n" +
                    "  \"email\": \"<EMAIL>\",\n" +
                    "  \"phone\": \"+91-9876543210\",\n" +
                    "  \"contactPerson\": \"Rahul Sharma\",\n" +
                    "  \"website\": \"https://www.abctech.com\",\n" +
                    "  \"bdmId\": 1,\n" +
                    "  \"commissionPercentage\": 5,\n" +
                    "  \"billingAddress\": \"123, Tech Park, Sector 45, Bangalore, Karnataka, India - 560045\",\n" +
                    "  \"shippingAddress\": \"456, Industrial Area, Sector 67, Noida, Uttar Pradesh, India - 201301\",\n" +
                    "  \"gstNumber\": \"29**********1Z5\",\n" +
                    "  \"panNumber\": \"**********\",\n" +
                    "  \"cinNumber\": \"U12345KA2010PTC012345\",\n" +
                    "  \"notes\": \"Preferred vendor for software development and consulting services.\"\n" +
                    "}");
            clientExample.setSummary("Client Creation Example");
            clientExample.setDescription("Example of creating a client with the correct format for bdmId");

            // Create an example of what NOT to do (incorrect format)
            Example incorrectClientExample = new Example();
            incorrectClientExample.setValue("{\n" +
                    "  \"name\": \"ABC Technologies Pvt Ltd\",\n" +
                    "  \"email\": \"<EMAIL>\",\n" +
                    "  \"phone\": \"+91-9876543210\",\n" +
                    "  \"contactPerson\": \"Rahul Sharma\",\n" +
                    "  \"website\": \"https://www.abctech.com\",\n" +
                    "  \"bdmId\": {\n" +
                    "    \"id\": 1,\n" +
                    "    \"name\": \"John Doe\",\n" +
                    "    \"email\": \"<EMAIL>\",\n" +
                    "    \"phone\": \"1234567890\",\n" +
                    "    \"gstNumber\": \"22AAAAA0000A1Z5\",\n" +
                    "    \"billingAddress\": \"123 Main St, City\",\n" +
                    "    \"commissionRate\": 5,\n" +
                    "    \"notes\": \"Some notes about this BDM\",\n" +
                    "    \"clientCount\": 0,\n" +
                    "    \"projectCount\": 0\n" +
                    "  },\n" +
                    "  \"commissionPercentage\": 5,\n" +
                    "  \"billingAddress\": \"123, Tech Park, Sector 45, Bangalore, Karnataka, India - 560045\",\n" +
                    "  \"shippingAddress\": \"456, Industrial Area, Sector 67, Noida, Uttar Pradesh, India - 201301\",\n" +
                    "  \"gstNumber\": \"29**********1Z5\",\n" +
                    "  \"panNumber\": \"**********\",\n" +
                    "  \"cinNumber\": \"U12345KA2010PTC012345\",\n" +
                    "  \"notes\": \"Preferred vendor for software development and consulting services.\"\n" +
                    "}");
            incorrectClientExample.setSummary("INCORRECT Client Format (DO NOT USE)");
            incorrectClientExample.setDescription("This is an example of an INCORRECT format that will cause errors. DO NOT use this format.");

            // Find the POST operation for clients
            openApi.getPaths().forEach((path, pathItem) -> {
                if (path.contains("/clients") && pathItem.getPost() != null) {
                    RequestBody requestBody = pathItem.getPost().getRequestBody();
                    if (requestBody != null && requestBody.getContent() != null) {
                        Content content = requestBody.getContent();
                        MediaType mediaType = content.get("application/json");
                        if (mediaType != null) {
                            // Add our examples
                            mediaType.addExamples("correctExample", clientExample);
                            mediaType.addExamples("incorrectExample", incorrectClientExample);

                            // Set the correct example as the default
                            Schema schema = mediaType.getSchema();
                            if (schema != null) {
                                schema.setExample("{\n" +
                                        "  \"name\": \"ABC Technologies Pvt Ltd\",\n" +
                                        "  \"email\": \"<EMAIL>\",\n" +
                                        "  \"phone\": \"+91-9876543210\",\n" +
                                        "  \"contactPerson\": \"Rahul Sharma\",\n" +
                                        "  \"website\": \"https://www.abctech.com\",\n" +
                                        "  \"bdmId\": 1,\n" +
                                        "  \"commissionPercentage\": 5,\n" +
                                        "  \"billingAddress\": \"123, Tech Park, Sector 45, Bangalore, Karnataka, India - 560045\",\n" +
                                        "  \"shippingAddress\": \"456, Industrial Area, Sector 67, Noida, Uttar Pradesh, India - 201301\",\n" +
                                        "  \"gstNumber\": \"29**********1Z5\",\n" +
                                        "  \"panNumber\": \"**********\",\n" +
                                        "  \"cinNumber\": \"U12345KA2010PTC012345\",\n" +
                                        "  \"notes\": \"Preferred vendor for software development and consulting services.\"\n" +
                                        "}");
                            }
                        }
                    }
                }
            });
        };
    }
    @Bean
    public OpenApiCustomizer projectExampleCustomizer() {
        return openApi -> {
            // Create a correct example for project creation
            Example projectExample = new Example();
            projectExample.setValue("{\n" +
                    "  \"id\": null,\n" +
                    "  \"clientId\": 1,\n" +
                    "  \"name\": \"Website Development\",\n" +
                    "  \"hsnCodeId\": 1,\n" +
                    "  \"description\": \"Development of a responsive website with e-commerce functionality\",\n" +
                    "  \"email\": \"<EMAIL>\",\n" +
                    "  \"phone\": \"1234567890\",\n" +
                    "  \"gstNumber\": \"29**********1Z5\",\n" +
                    "  \"billingAddress\": \"123 Main St, Bangalore, Karnataka\",\n" +
                    "  \"shippingAddress\": \"456 Business Park, Mumbai, Maharashtra\",\n" +
                    "  \"engagementCode\": \"ENG-2023-001\",\n" +
                    "  \"clientPartnerName\": \"John Doe\",\n" +
                    "  \"clientPartnerEmail\": \"<EMAIL>\",\n" +
                    "  \"clientPartnerPhone\": \"9876543210\",\n" +
                    "  \"bdmId\": 1,\n" +
                    "  \"commissionPercentage\": 10.5,\n" +
                    "  \"commissionAmount\": 5000.00\n" +
                    "}");
            projectExample.setSummary("Project Creation Example");
            projectExample.setDescription("Example of creating a project with the correct format");

            // Create an example of what NOT to do (incorrect format)
            Example incorrectProjectExample = new Example();
            incorrectProjectExample.setValue("{\n" +
                    "  \"id\": 0,\n" +
                    "  \"clientId\": {},\n" +
                    "  \"client\": {\n" +
                    "    \"id\": 0,\n" +
                    "    \"name\": \"string\"\n" +
                    "  },\n" +
                    "  \"name\": \"Website Development\",\n" +
                    "  \"hsnCodeId\": {},\n" +
                    "  \"hsnCode\": {\n" +
                    "    \"id\": 0,\n" +
                    "    \"code\": \"string\",\n" +
                    "    \"description\": \"string\",\n" +
                    "    \"gstRate\": 0\n" +
                    "  },\n" +
                    "  \"description\": \"string\",\n" +
                    "  \"email\": \"string\",\n" +
                    "  \"phone\": \"string\",\n" +
                    "  \"gstNumber\": \"string\",\n" +
                    "  \"billingAddress\": \"string\",\n" +
                    "  \"shippingAddress\": \"string\",\n" +
                    "  \"engagementCode\": \"string\",\n" +
                    "  \"clientPartnerName\": \"string\",\n" +
                    "  \"clientPartnerEmail\": \"string\",\n" +
                    "  \"clientPartnerPhone\": \"string\",\n" +
                    "  \"bdmId\": {},\n" +
                    "  \"bdm\": {\n" +
                    "    \"id\": 0,\n" +
                    "    \"name\": \"string\",\n" +
                    "    \"email\": \"string\",\n" +
                    "    \"phone\": \"string\",\n" +
                    "    \"gstNumber\": \"string\",\n" +
                    "    \"billingAddress\": \"string\",\n" +
                    "    \"commissionRate\": 100,\n" +
                    "    \"notes\": \"string\",\n" +
                    "    \"clientCount\": 0,\n" +
                    "    \"projectCount\": 0\n" +
                    "  },\n" +
                    "  \"commissionPercentage\": 0,\n" +
                    "  \"commissionAmount\": 0\n" +
                    "}");
            incorrectProjectExample.setSummary("INCORRECT Project Format (DO NOT USE)");
            incorrectProjectExample.setDescription("This is an example of an INCORRECT format that will cause errors. DO NOT use this format.");

            // Find the POST operation for projects
            openApi.getPaths().forEach((path, pathItem) -> {
                if (path.contains("/projects") && pathItem.getPost() != null) {
                    RequestBody requestBody = pathItem.getPost().getRequestBody();
                    if (requestBody != null && requestBody.getContent() != null) {
                        Content content = requestBody.getContent();
                        MediaType mediaType = content.get("application/json");
                        if (mediaType != null) {
                            // Add our examples
                            mediaType.addExamples("correctExample", projectExample);
                            mediaType.addExamples("incorrectExample", incorrectProjectExample);

                            // Set the correct example as the default
                            Schema schema = mediaType.getSchema();
                            if (schema != null) {
                                schema.setExample("{\n" +
                                        "  \"clientId\": 1,\n" +
                                        "  \"name\": \"Website Development\",\n" +
                                        "  \"hsnCodeId\": 1,\n" +
                                        "  \"description\": \"Development of a responsive website with e-commerce functionality\",\n" +
                                        "  \"email\": \"<EMAIL>\",\n" +
                                        "  \"phone\": \"1234567890\",\n" +
                                        "  \"gstNumber\": \"29**********1Z5\",\n" +
                                        "  \"billingAddress\": \"123 Main St, Bangalore, Karnataka\",\n" +
                                        "  \"shippingAddress\": \"456 Business Park, Mumbai, Maharashtra\",\n" +
                                        "  \"engagementCode\": \"ENG-2023-001\",\n" +
                                        "  \"clientPartnerName\": \"John Doe\",\n" +
                                        "  \"clientPartnerEmail\": \"<EMAIL>\",\n" +
                                        "  \"clientPartnerPhone\": \"9876543210\",\n" +
                                        "  \"bdmId\": 1,\n" +
                                        "  \"commissionPercentage\": 10.5,\n" +
                                        "  \"commissionAmount\": 5000.00\n" +
                                        "}");
                            }
                        }
                    }
                }
            });
        };
    }
}