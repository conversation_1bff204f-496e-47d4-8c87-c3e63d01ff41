package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.DocumentTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController

@Tag(name = "Document Template", description = "Document Template management API")
public class DocumentTemplateController {

    @Autowired
    private DocumentTemplateService documentTemplateService;

    @GetMapping("/document-templates/getAll")
    @Operation(summary = "Get all document templates", description = "Get all document templates")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document templates found"),
            @ApiResponse(responseCode = "204", description = "No document templates found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DocumentTemplateDto>> getAllDocumentTemplates() {
        try {
            List<DocumentTemplateDto> documentTemplates = documentTemplateService.getAllDocumentTemplates();
            return new ResponseEntity<>(documentTemplates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/document-templates/getById/{id}")
    @Operation(summary = "Get document template by ID", description = "Get document template by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document template found"),
            @ApiResponse(responseCode = "404", description = "Document template not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentTemplateDto> getDocumentTemplateById(@PathVariable Long id) {
        DocumentTemplateDto documentTemplate = documentTemplateService.getDocumentTemplateById(id);
        return new ResponseEntity<>(documentTemplate, HttpStatus.OK);
    }

    @GetMapping("/document-templates/getByType/{templateType}")
    @Operation(summary = "Get document templates by type", description = "Get document templates by type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document templates found"),
            @ApiResponse(responseCode = "204", description = "No document templates found for this type"),
            @ApiResponse(responseCode = "400", description = "Invalid template type supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DocumentTemplateDto>> getDocumentTemplatesByType(@PathVariable String templateType) {
        try {
            List<DocumentTemplateDto> documentTemplates = documentTemplateService
                    .getDocumentTemplatesByType(templateType);
            return new ResponseEntity<>(documentTemplates, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/document-templates/create")
    @Operation(summary = "Create document template", description = "Create document template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Document template created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentTemplateDto> createDocumentTemplate(
            @Valid @RequestBody DocumentTemplateDto documentTemplateDto) {
        DocumentTemplateDto createdDocumentTemplate = documentTemplateService
                .createDocumentTemplate(documentTemplateDto);
        return new ResponseEntity<>(createdDocumentTemplate, HttpStatus.CREATED);
    }

    @PutMapping("/document-templates/update/{id}")
    @Operation(summary = "Update document template", description = "Update document template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document template updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Document template not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentTemplateDto> updateDocumentTemplate(@PathVariable Long id,
            @Valid @RequestBody DocumentTemplateDto documentTemplateDto) {
        DocumentTemplateDto updatedDocumentTemplate = documentTemplateService.updateDocumentTemplate(id,
                documentTemplateDto);
        return new ResponseEntity<>(updatedDocumentTemplate, HttpStatus.OK);
    }

    @DeleteMapping("/document-templates/deleteById/{id}")
    @Operation(summary = "Delete document template", description = "Delete document template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Document template deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or document template is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Document template not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteDocumentTemplate(@PathVariable Long id) {
        documentTemplateService.deleteDocumentTemplate(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
