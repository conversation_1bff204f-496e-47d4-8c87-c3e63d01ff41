package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Project;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.entity.HsnCode;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.ProjectRepository;
import com.redberyl.invoiceapp.repository.CandidateRepository;
import com.redberyl.invoiceapp.repository.HsnCodeRepository;
import com.redberyl.invoiceapp.service.InvoiceGenerationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Controller for generating invoices in various formats
 */
@RestController
@RequestMapping("/api/invoice-generation")
@CrossOrigin(origins = {"http://localhost:3000", "http://127.0.0.1:3000"})
@Tag(name = "Invoice Generation", description = "API for generating invoices in various formats")
public class InvoiceGenerationController {

    @Autowired
    private InvoiceGenerationService invoiceGenerationService;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    /**
     * Generate a PDF invoice from an existing invoice
     *
     * @param invoiceId The ID of the invoice to generate
     * @return The PDF invoice as a downloadable resource
     */
    @GetMapping("/pdf/{invoiceId}")
    @Operation(summary = "Generate PDF invoice", description = "Generate a PDF invoice from an existing invoice")
    public ResponseEntity<Resource> generatePdfInvoice(@PathVariable Long invoiceId) {
        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));

        Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice-" + invoice.getInvoiceNumber() + ".pdf\"")
                .body(pdfResource);
    }

    /**
     * Generate a PDF invoice from invoice data without saving it
     *
     * @param invoiceDto The invoice data to use for generation
     * @return The PDF invoice as a downloadable resource
     */
    @PostMapping("/pdf/preview")
    @Operation(summary = "Generate PDF invoice preview", description = "Generate a PDF invoice from invoice data without saving it")
    public ResponseEntity<Resource> generatePdfInvoicePreview(@RequestBody InvoiceDto invoiceDto) {
        try {
            System.out.println("=== PDF PREVIEW REQUEST ===");
            System.out.println("Received invoice data for PDF preview");

            // Log important fields for debugging
            System.out.println("Invoice Number: " + invoiceDto.getInvoiceNumber());
            System.out.println("Client ID: " + invoiceDto.getClientId());
            System.out.println("Project ID: " + invoiceDto.getProjectId());
            System.out.println("Invoice Type ID: " + invoiceDto.getInvoiceTypeId());
            System.out.println("Billing Amount: " + invoiceDto.getBillingAmount());
            System.out.println("Tax Amount: " + invoiceDto.getTaxAmount());
            System.out.println("Total Amount: " + invoiceDto.getTotalAmount());

            Resource pdfResource = invoiceGenerationService.generatePdfInvoiceFromDto(invoiceDto);
            System.out.println("✓ PDF resource generated successfully");

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"invoice-preview.pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("❌ Error generating PDF preview: " + e.getMessage());
            e.printStackTrace();

            // Return a more detailed error response
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.CONTENT_TYPE, "application/json")
                    .body(null);
        }
    }

    /**
     * Generate an HTML invoice from an existing invoice
     *
     * @param invoiceId The ID of the invoice to generate
     * @return The HTML invoice as a string
     */
    @GetMapping("/html/{invoiceId}")
    @Operation(summary = "Generate HTML invoice", description = "Generate an HTML invoice from an existing invoice")
    public ResponseEntity<String> generateHtmlInvoice(@PathVariable Long invoiceId) {
        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));

        String htmlContent = invoiceGenerationService.generateHtmlInvoice(invoice);

        return ResponseEntity.ok()
                .contentType(MediaType.TEXT_HTML)
                .body(htmlContent);
    }

    /**
     * Generate an HTML invoice from invoice data without saving it
     *
     * @param invoiceDto The invoice data to use for generation
     * @return The HTML invoice as a string
     */
    @PostMapping("/html/preview")
    @Operation(summary = "Generate HTML invoice preview", description = "Generate an HTML invoice from invoice data without saving it")
    public ResponseEntity<String> generateHtmlInvoicePreview(@RequestBody InvoiceDto invoiceDto) {
        try {
            String htmlContent = invoiceGenerationService.generateHtmlInvoiceFromDto(invoiceDto);

            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(htmlContent);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("Failed to generate HTML invoice preview: " + e.getMessage(), e);
        }
    }

    /**
     * Public endpoint for generating a PDF invoice from an existing invoice
     *
     * @param invoiceId The ID of the invoice to generate
     * @return The PDF invoice as a downloadable resource
     */
    @GetMapping("/public/pdf/{invoiceId}")
    @Operation(summary = "Generate PDF invoice (public)", description = "Public endpoint for generating a PDF invoice from an existing invoice")
    public ResponseEntity<Resource> generatePublicPdfInvoice(@PathVariable Long invoiceId) {
        System.out.println("=== PDF Generation Request ===");
        System.out.println("Requested Invoice ID: " + invoiceId);

        // Use the new method that eagerly loads all related entities
        Invoice invoice = invoiceRepository.findByIdWithAllRelations(invoiceId)
                .orElseThrow(() -> {
                    System.out.println("Invoice not found with ID: " + invoiceId);
                    return new ResourceNotFoundException("Invoice not found with id: " + invoiceId);
                });

        System.out.println("Found Invoice: " + invoice.getInvoiceNumber());
        System.out.println("Invoice Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "NULL"));
        System.out.println("Invoice Project: " + (invoice.getProject() != null ? invoice.getProject().getName() : "NULL"));
        System.out.println("Invoice Candidate: " + (invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL"));
        System.out.println("Invoice HSN Code: " + (invoice.getHsnCode() != null ? invoice.getHsnCode().getCode() : "NULL"));
        System.out.println("Invoice Redberyl Account: " + (invoice.getRedberylAccount() != null ? invoice.getRedberylAccount().getAccountName() : "NULL"));

        Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice-" + invoice.getInvoiceNumber() + ".pdf\"")
                .body(pdfResource);
    }

    /**
     * Simple test endpoint to verify the API is working
     *
     * @return A simple text message
     */
    @GetMapping("/test")
    @Operation(summary = "Test API", description = "Simple endpoint to test if the API is working")
    public ResponseEntity<String> testApi() {
        System.out.println("=== TEST API CALLED ===");
        return ResponseEntity.ok()
                .header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
                .header("Access-Control-Allow-Headers", "*")
                .body("Invoice Generation API is working!");
    }

    /**
     * Simple test endpoint to verify the PDF preview API is working
     *
     * @return A simple text message
     */
    @PostMapping("/pdf/preview/test")
    @Operation(summary = "Test PDF Preview API", description = "Simple endpoint to test if the PDF preview API is working")
    public ResponseEntity<String> testPdfPreviewApi(@RequestBody(required = false) Object requestBody) {
        System.out.println("Test PDF Preview API called with body: " + requestBody);
        return ResponseEntity.ok()
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                .body("PDF Preview API endpoint is working! Received: " + (requestBody != null ? requestBody.toString() : "null"));
    }

    /**
     * Generate a minimal PDF for testing
     */
    @PostMapping("/pdf/preview/minimal")
    @Operation(summary = "Generate minimal PDF", description = "Generate a minimal PDF for testing")
    public ResponseEntity<Resource> generateMinimalPdf(@RequestBody(required = false) Object requestBody) {
        try {
            System.out.println("=== MINIMAL PDF TEST ===");
            System.out.println("Request body: " + requestBody);

            // Create a minimal invoice DTO for testing
            InvoiceDto testDto = new InvoiceDto();
            testDto.setInvoiceNumber("TEST-MINIMAL");
            testDto.setBillingAmount(new BigDecimal("10000"));
            testDto.setTaxAmount(new BigDecimal("1800"));
            testDto.setTotalAmount(new BigDecimal("11800"));

            Resource pdfResource = invoiceGenerationService.generatePdfInvoiceFromDto(testDto);
            System.out.println("✓ Minimal PDF generated successfully");

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"minimal-test.pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("❌ Error generating minimal PDF: " + e.getMessage());
            e.printStackTrace();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.CONTENT_TYPE, "application/json")
                    .body(null);
        }
    }

    /**
     * Test endpoint to generate a sample invoice PDF
     *
     * @return A sample PDF invoice
     */
    @GetMapping("/test/sample-pdf")
    @Operation(summary = "Generate sample PDF", description = "Generate a sample PDF invoice for testing")
    public ResponseEntity<Resource> generateSamplePdf() {
        try {
            // Find the first invoice in the database
            List<Invoice> invoices = invoiceRepository.findAll();
            if (invoices.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            Invoice invoice = invoices.get(0);
            Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"sample-invoice.pdf\"")
                    .body(pdfResource);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Check if an invoice exists
     *
     * @param invoiceId The ID of the invoice to check
     * @return Invoice details if exists
     */
    @GetMapping("/check/{invoiceId}")
    @Operation(summary = "Check invoice existence", description = "Check if an invoice exists in the database")
    public ResponseEntity<String> checkInvoice(@PathVariable Long invoiceId) {
        try {
            Invoice invoice = invoiceRepository.findById(invoiceId).orElse(null);
            if (invoice == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok("Invoice found: " + invoice.getInvoiceNumber() +
                                   ", Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "N/A") +
                                   ", Total: " + invoice.getTotalAmount());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error checking invoice: " + e.getMessage());
        }
    }

    /**
     * List all invoices with their IDs for debugging
     *
     * @return List of all invoices with their database IDs
     */
    @GetMapping("/list-all")
    @Operation(summary = "List all invoices", description = "List all invoices with their database IDs for debugging")
    public ResponseEntity<String> listAllInvoices() {
        try {
            List<Invoice> invoices = invoiceRepository.findAll();
            if (invoices.isEmpty()) {
                return ResponseEntity.ok("No invoices found in database");
            }

            StringBuilder result = new StringBuilder("All invoices in database:\n");
            for (Invoice invoice : invoices) {
                result.append("Database ID: ").append(invoice.getId())
                      .append(", Invoice Number: ").append(invoice.getInvoiceNumber())
                      .append(", Client: ").append(invoice.getClient() != null ? invoice.getClient().getName() : "N/A")
                      .append(", Total: ").append(invoice.getTotalAmount())
                      .append("\n");
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error listing invoices: " + e.getMessage());
        }
    }

    /**
     * Generate PDF by invoice number instead of ID
     *
     * @param invoiceNumber The invoice number (e.g., INV-001)
     * @return The PDF invoice as a downloadable resource
     */
    @GetMapping("/public/pdf/by-number/{invoiceNumber}")
    @Operation(summary = "Generate PDF by invoice number", description = "Generate a PDF invoice using invoice number")
    public ResponseEntity<Resource> generatePdfByInvoiceNumber(@PathVariable String invoiceNumber) {
        System.out.println("=== PDF Generation by Number ===");
        System.out.println("Requested Invoice Number: " + invoiceNumber);

        // Use the new method that eagerly loads all related entities
        Invoice invoice = invoiceRepository.findByInvoiceNumberWithAllRelations(invoiceNumber)
                .orElseThrow(() -> {
                    System.out.println("Invoice not found with number: " + invoiceNumber);
                    return new ResourceNotFoundException("Invoice not found with number: " + invoiceNumber);
                });

        System.out.println("Found Invoice ID: " + invoice.getId());
        System.out.println("Invoice Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "NULL"));
        System.out.println("Invoice Project: " + (invoice.getProject() != null ? invoice.getProject().getName() : "NULL"));
        System.out.println("Invoice Candidate: " + (invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL"));
        System.out.println("Invoice HSN Code: " + (invoice.getHsnCode() != null ? invoice.getHsnCode().getCode() : "NULL"));
        System.out.println("Invoice Redberyl Account: " + (invoice.getRedberylAccount() != null ? invoice.getRedberylAccount().getAccountName() : "NULL"));

        Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice-" + invoice.getInvoiceNumber() + ".pdf\"")
                .body(pdfResource);
    }

    /**
     * Debug endpoint to test ID extraction logic
     *
     * @param invoiceId The invoice ID to test
     * @return Debug information
     */
    @GetMapping("/debug/test-id/{invoiceId}")
    @Operation(summary = "Test ID extraction", description = "Debug endpoint to test ID extraction logic")
    public ResponseEntity<String> testIdExtraction(@PathVariable String invoiceId) {
        StringBuilder result = new StringBuilder();
        result.append("Testing ID extraction for: ").append(invoiceId).append("\n");

        // Test different extraction patterns
        if (invoiceId.matches("INV-(\\d+)$")) {
            String extracted = invoiceId.replaceAll("INV-(\\d+)$", "$1");
            result.append("Pattern INV-(\\d+)$: ").append(extracted).append("\n");
        }

        if (invoiceId.matches("INV-\\d+-(\\d+)$")) {
            String extracted = invoiceId.replaceAll("INV-\\d+-(\\d+)$", "$1");
            result.append("Pattern INV-\\d+-(\\d+)$: ").append(extracted).append("\n");
        }

        if (invoiceId.matches(".*(\\d+)$")) {
            String extracted = invoiceId.replaceAll(".*(\\d+)$", "$1");
            result.append("Pattern .*(\\d+)$: ").append(extracted).append("\n");
        }

        // Test database lookup
        try {
            Long numericId = Long.parseLong(invoiceId.replaceAll(".*?(\\d+)$", "$1"));
            Invoice byId = invoiceRepository.findById(numericId).orElse(null);
            result.append("Database lookup by ID ").append(numericId).append(": ")
                  .append(byId != null ? byId.getInvoiceNumber() : "NOT FOUND").append("\n");
        } catch (Exception e) {
            result.append("Error parsing numeric ID: ").append(e.getMessage()).append("\n");
        }

        // Test by invoice number
        Invoice byNumber = invoiceRepository.findByInvoiceNumber(invoiceId).orElse(null);
        result.append("Database lookup by invoice number ").append(invoiceId).append(": ")
              .append(byNumber != null ? "FOUND (ID: " + byNumber.getId() + ")" : "NOT FOUND").append("\n");

        return ResponseEntity.ok(result.toString());
    }

    /**
     * Create sample invoices to match frontend data
     */
    @PostMapping("/debug/create-sample-invoices")
    @Operation(summary = "Create sample invoices", description = "Create sample invoices to match frontend data")
    public ResponseEntity<String> createSampleInvoices() {
        try {
            StringBuilder result = new StringBuilder("Creating sample invoices:\n");

            // Check if invoices already exist
            List<Invoice> existingInvoices = invoiceRepository.findAll();
            result.append("Existing invoices: ").append(existingInvoices.size()).append("\n");

            for (Invoice inv : existingInvoices) {
                result.append("- ID: ").append(inv.getId())
                      .append(", Number: ").append(inv.getInvoiceNumber())
                      .append(", Client: ").append(inv.getClient() != null ? inv.getClient().getName() : "NULL")
                      .append("\n");
            }

            // Create missing invoices if they don't exist
            String[] invoiceNumbers = {"INV-001", "INV-004", "INV-005"};

            for (String invoiceNumber : invoiceNumbers) {
                Invoice existing = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);
                if (existing == null) {
                    result.append("Creating invoice: ").append(invoiceNumber).append("\n");

                    Invoice newInvoice = new Invoice();
                    newInvoice.setInvoiceNumber(invoiceNumber);
                    newInvoice.setInvoiceDate(LocalDate.now());
                    newInvoice.setDueDate(LocalDate.now().plusDays(30));
                    newInvoice.setBillingAmount(new BigDecimal("30000.00"));
                    newInvoice.setTaxAmount(new BigDecimal("5400.00"));
                    newInvoice.setTotalAmount(new BigDecimal("35400.00"));
                    // Note: Invoice entity doesn't have status field

                    // Try to associate with existing clients, projects, candidates if available
                    try {
                        // This is just for testing - in real scenario, these would be properly linked
                        result.append("Note: Invoice created without relations. You'll need to link it to client/project/candidate manually.\n");
                    } catch (Exception e) {
                        result.append("Warning: Could not link relations: ").append(e.getMessage()).append("\n");
                    }

                    Invoice saved = invoiceRepository.save(newInvoice);
                    result.append("Created invoice with ID: ").append(saved.getId()).append("\n");
                } else {
                    result.append("Invoice ").append(invoiceNumber).append(" already exists with ID: ").append(existing.getId()).append("\n");
                }
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating sample invoices: " + e.getMessage());
        }
    }

    /**
     * Create complete sample data with all relations
     */
    @PostMapping("/debug/create-complete-sample-data")
    @Operation(summary = "Create complete sample data", description = "Create complete sample data with all relations")
    public ResponseEntity<String> createCompleteSampleData() {
        try {
            StringBuilder result = new StringBuilder("Creating complete sample data:\n");

            // 1. Create RedBeryl Account if it doesn't exist
            RedberylAccount redberylAccount = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
            if (redberylAccount == null) {
                redberylAccount = new RedberylAccount();
                redberylAccount.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
                redberylAccount.setAccountNo("**************");
                redberylAccount.setBankName("HDFC Bank Ltd.");
                redberylAccount.setIfscCode("HDFC0000486");
                redberylAccount.setBranchName("Destination Centre, Magarpatta, Pune");
                redberylAccount.setAccountType("Current Account");
                redberylAccount.setGstn("27**********1Z5");
                redberylAccount.setCin("U72900PN2022PTC213381");
                redberylAccount.setPanNo("**********");
                redberylAccount = redberylAccountRepository.save(redberylAccount);
                result.append("Created RedBeryl Account with ID: ").append(redberylAccount.getId()).append("\n");
            } else {
                result.append("RedBeryl Account already exists with ID: ").append(redberylAccount.getId()).append("\n");
            }

            // 2. Create HSN Code if it doesn't exist
            HsnCode hsnCode = hsnCodeRepository.findAll().stream().findFirst().orElse(null);
            if (hsnCode == null) {
                hsnCode = new HsnCode();
                hsnCode.setCode("998313");
                hsnCode.setDescription("IT consulting services");
                hsnCode = hsnCodeRepository.save(hsnCode);
                result.append("Created HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            } else {
                result.append("HSN Code already exists with ID: ").append(hsnCode.getId()).append("\n");
            }

            // 3. Get existing clients, projects, candidates
            List<Client> clients = clientRepository.findAll();
            List<Project> projects = projectRepository.findAll();
            List<Candidate> candidates = candidateRepository.findAll();

            result.append("Found ").append(clients.size()).append(" clients, ")
                  .append(projects.size()).append(" projects, ")
                  .append(candidates.size()).append(" candidates\n");

            // 4. Update existing invoices with proper relations
            String[] invoiceNumbers = {"INV-001", "INV-004", "INV-005"};

            for (int i = 0; i < invoiceNumbers.length; i++) {
                String invoiceNumber = invoiceNumbers[i];
                Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);

                if (invoice == null) {
                    // Create new invoice
                    invoice = new Invoice();
                    invoice.setInvoiceNumber(invoiceNumber);
                    invoice.setInvoiceDate(LocalDate.now());
                    invoice.setDueDate(LocalDate.now().plusDays(30));
                    invoice.setBillingAmount(new BigDecimal("30000.00"));
                    invoice.setTaxAmount(new BigDecimal("5400.00"));
                    invoice.setTotalAmount(new BigDecimal("35400.00"));
                    result.append("Created new invoice: ").append(invoiceNumber).append("\n");
                } else {
                    result.append("Updating existing invoice: ").append(invoiceNumber).append("\n");
                }

                // Link to RedBeryl Account
                invoice.setRedberylAccount(redberylAccount);

                // Link to HSN Code
                invoice.setHsnCode(hsnCode);

                // Link to client if available
                if (!clients.isEmpty() && i < clients.size()) {
                    invoice.setClient(clients.get(i));
                    result.append("  - Linked to client: ").append(clients.get(i).getName()).append("\n");
                }

                // Link to project if available
                if (!projects.isEmpty() && i < projects.size()) {
                    invoice.setProject(projects.get(i));
                    result.append("  - Linked to project: ").append(projects.get(i).getName()).append("\n");
                }

                // Link to candidate if available
                if (!candidates.isEmpty() && i < candidates.size()) {
                    invoice.setCandidate(candidates.get(i));
                    result.append("  - Linked to candidate: ").append(candidates.get(i).getName()).append("\n");
                }

                invoice = invoiceRepository.save(invoice);
                result.append("  - Saved invoice with ID: ").append(invoice.getId()).append("\n");
            }

            result.append("\nSample data creation completed successfully!\n");
            result.append("All invoices now have proper relations to RedBeryl Account, HSN Code, and other entities.\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating complete sample data: " + e.getMessage());
        }
    }

    /**
     * Debug specific invoice relations
     */
    @GetMapping("/debug/invoice/{invoiceNumber}")
    @Operation(summary = "Debug invoice relations", description = "Check specific invoice and its relations")
    public ResponseEntity<String> debugInvoiceRelations(@PathVariable String invoiceNumber) {
        try {
            StringBuilder result = new StringBuilder("=== DEBUGGING INVOICE: " + invoiceNumber + " ===\n\n");

            Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);
            if (invoice == null) {
                return ResponseEntity.notFound().build();
            }

            result.append("Invoice ID: ").append(invoice.getId()).append("\n");
            result.append("Invoice Number: ").append(invoice.getInvoiceNumber()).append("\n");
            result.append("Invoice Date: ").append(invoice.getInvoiceDate()).append("\n");
            result.append("Total Amount: ").append(invoice.getTotalAmount()).append("\n\n");

            // Check RedBeryl Account
            result.append("=== REDBERYL ACCOUNT ===\n");
            if (invoice.getRedberylAccount() != null) {
                RedberylAccount account = invoice.getRedberylAccount();
                result.append("✓ RedBeryl Account is linked!\n");
                result.append("Account ID: ").append(account.getId()).append("\n");
                result.append("Account Name: ").append(account.getAccountName()).append("\n");
                result.append("Account No: ").append(account.getAccountNo()).append("\n");
                result.append("Bank Name: ").append(account.getBankName()).append("\n");
                result.append("IFSC Code: ").append(account.getIfscCode()).append("\n");
                result.append("Branch Name: ").append(account.getBranchName()).append("\n");
                result.append("Account Type: ").append(account.getAccountType()).append("\n");
                result.append("GSTN: ").append(account.getGstn()).append("\n");
                result.append("CIN: ").append(account.getCin()).append("\n");
                result.append("PAN No: ").append(account.getPanNo()).append("\n");
            } else {
                result.append("✗ NO RedBeryl Account linked!\n");
            }

            // Check HSN Code
            result.append("\n=== HSN CODE ===\n");
            if (invoice.getHsnCode() != null) {
                HsnCode hsn = invoice.getHsnCode();
                result.append("✓ HSN Code is linked!\n");
                result.append("HSN ID: ").append(hsn.getId()).append("\n");
                result.append("HSN Code: ").append(hsn.getCode()).append("\n");
                result.append("HSN Description: ").append(hsn.getDescription()).append("\n");
            } else {
                result.append("✗ NO HSN Code linked!\n");
            }

            // Check Client
            result.append("\n=== CLIENT ===\n");
            if (invoice.getClient() != null) {
                Client client = invoice.getClient();
                result.append("✓ Client is linked!\n");
                result.append("Client ID: ").append(client.getId()).append("\n");
                result.append("Client Name: ").append(client.getName()).append("\n");
            } else {
                result.append("✗ NO Client linked!\n");
            }

            // Check Project
            result.append("\n=== PROJECT ===\n");
            if (invoice.getProject() != null) {
                Project project = invoice.getProject();
                result.append("✓ Project is linked!\n");
                result.append("Project ID: ").append(project.getId()).append("\n");
                result.append("Project Name: ").append(project.getName()).append("\n");
            } else {
                result.append("✗ NO Project linked!\n");
            }

            // Check Candidate
            result.append("\n=== CANDIDATE ===\n");
            if (invoice.getCandidate() != null) {
                Candidate candidate = invoice.getCandidate();
                result.append("✓ Candidate is linked!\n");
                result.append("Candidate ID: ").append(candidate.getId()).append("\n");
                result.append("Candidate Name: ").append(candidate.getName()).append("\n");
            } else {
                result.append("✗ NO Candidate linked!\n");
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error debugging invoice: " + e.getMessage());
        }
    }

    /**
     * Force link invoice to RedBeryl account
     */
    @PostMapping("/debug/force-link/{invoiceNumber}")
    @Operation(summary = "Force link invoice to RedBeryl account", description = "Force link specific invoice to RedBeryl account")
    public ResponseEntity<String> forceLinkInvoice(@PathVariable String invoiceNumber) {
        try {
            StringBuilder result = new StringBuilder("=== FORCE LINKING INVOICE: " + invoiceNumber + " ===\n\n");

            // Find the invoice
            Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);
            if (invoice == null) {
                return ResponseEntity.notFound().build();
            }

            // Find or create RedBeryl account
            RedberylAccount redberylAccount = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
            if (redberylAccount == null) {
                redberylAccount = new RedberylAccount();
                redberylAccount.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
                redberylAccount.setAccountNo("**************");
                redberylAccount.setBankName("HDFC Bank Ltd.");
                redberylAccount.setIfscCode("HDFC0000486");
                redberylAccount.setBranchName("Destination Centre, Magarpatta, Pune");
                redberylAccount.setAccountType("Current Account");
                redberylAccount.setGstn("27**********1Z5");
                redberylAccount.setCin("U72900PN2022PTC213381");
                redberylAccount.setPanNo("**********");
                redberylAccount = redberylAccountRepository.save(redberylAccount);
                result.append("Created new RedBeryl Account with ID: ").append(redberylAccount.getId()).append("\n");
            } else {
                result.append("Found existing RedBeryl Account with ID: ").append(redberylAccount.getId()).append("\n");
            }

            // Find or create HSN code
            HsnCode hsnCode = hsnCodeRepository.findAll().stream().findFirst().orElse(null);
            if (hsnCode == null) {
                hsnCode = new HsnCode();
                hsnCode.setCode("998313");
                hsnCode.setDescription("IT consulting services");
                hsnCode = hsnCodeRepository.save(hsnCode);
                result.append("Created new HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            } else {
                result.append("Found existing HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            }

            // Link the invoice to RedBeryl account and HSN code
            invoice.setRedberylAccount(redberylAccount);
            invoice.setHsnCode(hsnCode);

            // Save the invoice
            invoice = invoiceRepository.save(invoice);

            result.append("\n=== LINKING COMPLETED ===\n");
            result.append("Invoice ").append(invoiceNumber).append(" has been linked to:\n");
            result.append("- RedBeryl Account: ").append(redberylAccount.getAccountName()).append("\n");
            result.append("- HSN Code: ").append(hsnCode.getCode()).append("\n");
            result.append("\nNow try generating the PDF again!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error force linking invoice: " + e.getMessage());
        }
    }

    /**
     * Simple fix for INV-005 RedBeryl account issue
     */
    @GetMapping("/debug/fix-inv-005")
    @Operation(summary = "Fix INV-005 RedBeryl account", description = "Simple fix for INV-005 RedBeryl account issue")
    public ResponseEntity<String> fixInv005() {
        try {
            StringBuilder result = new StringBuilder("=== FIXING INV-005 REDBERYL ACCOUNT ISSUE ===\n\n");

            // 1. Find INV-005
            Invoice invoice = invoiceRepository.findByInvoiceNumber("INV-005").orElse(null);
            if (invoice == null) {
                return ResponseEntity.badRequest().body("INV-005 not found!");
            }
            result.append("✓ Found INV-005 with ID: ").append(invoice.getId()).append("\n");

            // 2. Create RedBeryl Account
            RedberylAccount account = new RedberylAccount();
            account.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
            account.setAccountNo("**************");
            account.setBankName("HDFC Bank Ltd.");
            account.setIfscCode("HDFC0000486");
            account.setBranchName("Destination Centre, Magarpatta, Pune");
            account.setAccountType("Current Account");
            account.setGstn("27**********1Z5");
            account.setCin("U72900PN2022PTC213381");
            account.setPanNo("**********");

            account = redberylAccountRepository.save(account);
            result.append("✓ Created RedBeryl Account with ID: ").append(account.getId()).append("\n");

            // 3. Create HSN Code
            HsnCode hsnCode = new HsnCode();
            hsnCode.setCode("998313");
            hsnCode.setDescription("IT consulting services");
            hsnCode = hsnCodeRepository.save(hsnCode);
            result.append("✓ Created HSN Code with ID: ").append(hsnCode.getId()).append("\n");

            // 4. Link to invoice
            invoice.setRedberylAccount(account);
            invoice.setHsnCode(hsnCode);
            invoice = invoiceRepository.save(invoice);
            result.append("✓ Linked INV-005 to RedBeryl Account and HSN Code\n");

            result.append("\n=== SUCCESS! ===\n");
            result.append("INV-005 is now properly linked to:\n");
            result.append("- RedBeryl Account: ").append(account.getAccountName()).append("\n");
            result.append("- Account Number: ").append(account.getAccountNo()).append("\n");
            result.append("- Bank: ").append(account.getBankName()).append("\n");
            result.append("- HSN Code: ").append(hsnCode.getCode()).append("\n");
            result.append("\nNow generate the PDF again - it should show real data!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error fixing INV-005: " + e.getMessage() + "\nStack trace: " + java.util.Arrays.toString(e.getStackTrace()));
        }
    }
}
