package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceMilestoneDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.InvoiceMilestoneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@Tag(name = "Invoice Milestone", description = "Invoice Milestone management API")
public class InvoiceMilestoneController {

    @Autowired
    private InvoiceMilestoneService invoiceMilestoneService;

    @GetMapping("/invoice-milestones/getAll")
    @Operation(summary = "Get all invoice milestones", description = "Get all invoice milestones")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice milestones found"),
            @ApiResponse(responseCode = "204", description = "No invoice milestones found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceMilestoneDto>> getAllInvoiceMilestones() {
        try {
            List<InvoiceMilestoneDto> invoiceMilestones = invoiceMilestoneService.getAllInvoiceMilestones();
            return new ResponseEntity<>(invoiceMilestones, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoice-milestones/getById/{id}")
    @Operation(summary = "Get invoice milestone by ID", description = "Get invoice milestone by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice milestone found"),
            @ApiResponse(responseCode = "404", description = "Invoice milestone not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceMilestoneDto> getInvoiceMilestoneById(@PathVariable Long id) {
        InvoiceMilestoneDto invoiceMilestone = invoiceMilestoneService.getInvoiceMilestoneById(id);
        return new ResponseEntity<>(invoiceMilestone, HttpStatus.OK);
    }

    @GetMapping("/invoice-milestones/getByInvoiceId/{invoiceId}")
    @Operation(summary = "Get invoice milestones by invoice ID", description = "Get invoice milestones by invoice ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice milestones found"),
            @ApiResponse(responseCode = "204", description = "No invoice milestones found for this invoice"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "400", description = "Invalid invoice ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceMilestoneDto>> getInvoiceMilestonesByInvoiceId(@PathVariable Long invoiceId) {
        try {
            List<InvoiceMilestoneDto> invoiceMilestones = invoiceMilestoneService
                    .getInvoiceMilestonesByInvoiceId(invoiceId);
            return new ResponseEntity<>(invoiceMilestones, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoice-milestones/getByDueBefore")
    @Operation(summary = "Get invoice milestones due before a date", description = "Get invoice milestones due before a date")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice milestones found"),
            @ApiResponse(responseCode = "204", description = "No invoice milestones found due before the specified date"),
            @ApiResponse(responseCode = "400", description = "Invalid date format")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceMilestoneDto>> getInvoiceMilestonesByDateBefore(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            List<InvoiceMilestoneDto> invoiceMilestones = invoiceMilestoneService
                    .getInvoiceMilestonesByDateBefore(date);
            return new ResponseEntity<>(invoiceMilestones, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/invoice-milestones/create")
    @Operation(summary = "Create invoice milestone", description = "Create invoice milestone")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Invoice milestone created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceMilestoneDto> createInvoiceMilestone(
            @Valid @RequestBody InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestoneDto createdInvoiceMilestone = invoiceMilestoneService
                .createInvoiceMilestone(invoiceMilestoneDto);
        return new ResponseEntity<>(createdInvoiceMilestone, HttpStatus.CREATED);
    }

    @PutMapping("/invoice-milestones/update/{id}")
    @Operation(summary = "Update invoice milestone", description = "Update invoice milestone")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice milestone updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Invoice milestone not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceMilestoneDto> updateInvoiceMilestone(@PathVariable Long id,
            @Valid @RequestBody InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestoneDto updatedInvoiceMilestone = invoiceMilestoneService.updateInvoiceMilestone(id,
                invoiceMilestoneDto);
        return new ResponseEntity<>(updatedInvoiceMilestone, HttpStatus.OK);
    }

    @DeleteMapping("/invoice-milestones/deleteById/{id}")
    @Operation(summary = "Delete invoice milestone", description = "Delete invoice milestone")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Invoice milestone deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or invoice milestone is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Invoice milestone not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceMilestone(@PathVariable Long id) {
        invoiceMilestoneService.deleteInvoiceMilestone(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
