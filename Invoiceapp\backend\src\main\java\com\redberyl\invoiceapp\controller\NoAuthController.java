package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.service.ProjectService;
import com.redberyl.invoiceapp.service.TaxRateService;
import com.redberyl.invoiceapp.service.TaxTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = "*", allowCredentials = "false")
@Tag(name = "No Auth", description = "API endpoints that don't require authentication")
public class NoAuthController {

    @Autowired
    private TaxRateService taxRateService;

    @Autowired
    private TaxTypeService taxTypeService;

    @Autowired
    private ProjectService projectService;

    @GetMapping("/noauth/getTaxRates")
    @Operation(summary = "Get all tax rates without authentication", description = "Get all tax rates without authentication")
    public ResponseEntity<List<TaxRateDto>> getAllTaxRates() {
        List<TaxRateDto> taxRates = taxRateService.getAllTaxRates();
        return new ResponseEntity<>(taxRates, HttpStatus.OK);
    }

    @PostMapping("/noauth/createTaxRate")
    @Operation(summary = "Create tax rate without authentication", description = "Create tax rate without authentication")
    public ResponseEntity<TaxRateDto> createTaxRate(@RequestBody TaxRateDto taxRateDto) {
        TaxRateDto createdTaxRate = taxRateService.createTaxRate(taxRateDto);
        return new ResponseEntity<>(createdTaxRate, HttpStatus.CREATED);
    }

    @GetMapping("/noauth/getTaxTypes")
    @Operation(summary = "Get all tax types without authentication", description = "Get all tax types without authentication")
    public ResponseEntity<List<TaxTypeDto>> getAllTaxTypes() {
        List<TaxTypeDto> taxTypes = taxTypeService.getAllTaxTypes();
        return new ResponseEntity<>(taxTypes, HttpStatus.OK);
    }

    @PostMapping("/noauth/createTaxType")
    @Operation(summary = "Create tax type without authentication", description = "Create tax type without authentication")
    public ResponseEntity<TaxTypeDto> createTaxType(@RequestBody TaxTypeDto taxTypeDto) {
        TaxTypeDto createdTaxType = taxTypeService.createTaxType(taxTypeDto);
        return new ResponseEntity<>(createdTaxType, HttpStatus.CREATED);
    }

    @GetMapping("/noauth/getProjects")
    @Operation(summary = "Get all projects without authentication", description = "Get all projects without authentication")
    public ResponseEntity<List<ProjectDto>> getAllProjects() {
        List<ProjectDto> projects = projectService.getAllProjects();
        return new ResponseEntity<>(projects, HttpStatus.OK);
    }

    @GetMapping("/api/noauth/projects")
    @Operation(summary = "Get all projects without authentication (alternative endpoint)", description = "Get all projects without authentication (alternative endpoint)")
    public ResponseEntity<List<ProjectDto>> getAllProjectsAlt() {
        List<ProjectDto> projects = projectService.getAllProjects();
        return new ResponseEntity<>(projects, HttpStatus.OK);
    }

    @GetMapping("/noauth/getProjectById/{id}")
    @Operation(summary = "Get project by ID without authentication", description = "Get project by ID without authentication")
    public ResponseEntity<ProjectDto> getProjectById(@PathVariable Long id) {
        ProjectDto project = projectService.getProjectById(id);
        return new ResponseEntity<>(project, HttpStatus.OK);
    }

    @PostMapping("/noauth/createProject")
    @Operation(summary = "Create project without authentication", description = "Create project without authentication")
    public ResponseEntity<ProjectDto> createProject(@Valid @RequestBody ProjectDto projectDto) {
        ProjectDto createdProject = projectService.createProject(projectDto);
        return new ResponseEntity<>(createdProject, HttpStatus.CREATED);
    }
}
