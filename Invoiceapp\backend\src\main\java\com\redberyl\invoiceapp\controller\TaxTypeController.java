package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.TaxTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Tax Type", description = "Tax Type management API")
public class TaxTypeController {

    @Autowired
    private TaxTypeService taxTypeService;

    @GetMapping("/tax-types/getAll")
    @Operation(summary = "Get all tax types", description = "Get all tax types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax types found"),
            @ApiResponse(responseCode = "204", description = "No tax types found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<TaxTypeDto>> getAllTaxTypes() {
        try {
            List<TaxTypeDto> taxTypes = taxTypeService.getAllTaxTypes();
            return new ResponseEntity<>(taxTypes, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/tax-types/getById/{id}")
    @Operation(summary = "Get tax type by ID", description = "Get tax type by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax type found"),
            @ApiResponse(responseCode = "404", description = "Tax type not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<TaxTypeDto> getTaxTypeById(@PathVariable Long id) {
        TaxTypeDto taxType = taxTypeService.getTaxTypeById(id);
        return new ResponseEntity<>(taxType, HttpStatus.OK);
    }

    @GetMapping("/tax-types/getByType/{taxType}")
    @Operation(summary = "Get tax type by type", description = "Get tax type by type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax type found"),
            @ApiResponse(responseCode = "404", description = "Tax type not found"),
            @ApiResponse(responseCode = "400", description = "Invalid type supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<TaxTypeDto> getTaxTypeByType(@PathVariable String taxType) {
        TaxTypeDto type = taxTypeService.getTaxTypeByType(taxType);
        return new ResponseEntity<>(type, HttpStatus.OK);
    }

    @PostMapping("/tax-types/create")
    @Operation(summary = "Create tax type", description = "Create tax type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Tax type created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<TaxTypeDto> createTaxType(@Valid @RequestBody TaxTypeDto taxTypeDto) {
        TaxTypeDto createdTaxType = taxTypeService.createTaxType(taxTypeDto);
        return new ResponseEntity<>(createdTaxType, HttpStatus.CREATED);
    }

    @PutMapping("/tax-types/update/{id}")
    @Operation(summary = "Update tax type", description = "Update tax type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Tax type updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Tax type not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<TaxTypeDto> updateTaxType(@PathVariable Long id, @Valid @RequestBody TaxTypeDto taxTypeDto) {
        TaxTypeDto updatedTaxType = taxTypeService.updateTaxType(id, taxTypeDto);
        return new ResponseEntity<>(updatedTaxType, HttpStatus.OK);
    }

    @DeleteMapping("/tax-types/deleteById/{id}")
    @Operation(summary = "Delete tax type", description = "Delete tax type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Tax type deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or tax type is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Tax type not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteTaxType(@PathVariable Long id) {
        taxTypeService.deleteTaxType(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
