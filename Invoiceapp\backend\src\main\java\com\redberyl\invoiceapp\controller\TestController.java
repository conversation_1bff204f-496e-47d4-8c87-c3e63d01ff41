package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.auth.MessageResponseDto;
import com.redberyl.invoiceapp.entity.auth.ERole;
import com.redberyl.invoiceapp.entity.auth.Role;
import com.redberyl.invoiceapp.repository.RoleRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@CrossOrigin(origins = { "http://localhost:3000", "http://127.0.0.1:3000" }, allowedHeaders = "*", methods = {
        RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
        RequestMethod.DELETE, RequestMethod.OPTIONS }, allowCredentials = "true", maxAge = 3600)
@RestController
@RequestMapping("/test")
@Tag(name = "Test", description = "Test API endpoints")
public class TestController {

    @Autowired
    private RoleRepository roleRepository;

    @GetMapping("/roles")
    @Operation(summary = "Test roles", description = "Test if roles are properly set up in the database")
    public ResponseEntity<?> testRoles() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Roles test");

            // Check if ROLE_USER exists
            roleRepository.findByName(ERole.ROLE_USER)
                    .ifPresentOrElse(
                            role -> response.put("ROLE_USER", Map.of("id", role.getId(), "exists", true)),
                            () -> response.put("ROLE_USER", Map.of("exists", false)));

            // Check if ROLE_ADMIN exists
            roleRepository.findByName(ERole.ROLE_ADMIN)
                    .ifPresentOrElse(
                            role -> response.put("ROLE_ADMIN", Map.of("id", role.getId(), "exists", true)),
                            () -> response.put("ROLE_ADMIN", Map.of("exists", false)));

            // Check if ROLE_MODERATOR exists
            roleRepository.findByName(ERole.ROLE_MODERATOR)
                    .ifPresentOrElse(
                            role -> response.put("ROLE_MODERATOR", Map.of("id", role.getId(), "exists", true)),
                            () -> response.put("ROLE_MODERATOR", Map.of("exists", false)));

            // Get all roles
            response.put("allRoles", roleRepository.findAll().stream()
                    .map(role -> Map.of(
                            "id", role.getId(),
                            "name", role.getName().toString()))
                    .toList());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponseDto("Error checking roles: " + e.getMessage()));
        }
    }

    @GetMapping("/create-roles")
    @Operation(summary = "Create roles", description = "Create default roles if they don't exist")
    public ResponseEntity<?> createRoles() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Creating roles");

            // Create ROLE_USER if it doesn't exist
            if (roleRepository.findByName(ERole.ROLE_USER).isEmpty()) {
                Role userRole = new Role();
                userRole.setName(ERole.ROLE_USER);
                roleRepository.save(userRole);
                response.put("ROLE_USER", "created");
            } else {
                response.put("ROLE_USER", "already exists");
            }

            // Create ROLE_ADMIN if it doesn't exist
            if (roleRepository.findByName(ERole.ROLE_ADMIN).isEmpty()) {
                Role adminRole = new Role();
                adminRole.setName(ERole.ROLE_ADMIN);
                roleRepository.save(adminRole);
                response.put("ROLE_ADMIN", "created");
            } else {
                response.put("ROLE_ADMIN", "already exists");
            }

            // Create ROLE_MODERATOR if it doesn't exist
            if (roleRepository.findByName(ERole.ROLE_MODERATOR).isEmpty()) {
                Role modRole = new Role();
                modRole.setName(ERole.ROLE_MODERATOR);
                roleRepository.save(modRole);
                response.put("ROLE_MODERATOR", "created");
            } else {
                response.put("ROLE_MODERATOR", "already exists");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponseDto("Error creating roles: " + e.getMessage()));
        }
    }

    @GetMapping("/ping")
    @Operation(summary = "Ping", description = "Simple ping endpoint to test if the API is working")
    public ResponseEntity<?> ping() {
        return ResponseEntity.ok(new MessageResponseDto("Pong! API is working."));
    }
}
