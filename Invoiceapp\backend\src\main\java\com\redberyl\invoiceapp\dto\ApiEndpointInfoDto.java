package com.redberyl.invoiceapp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for API endpoint information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "API endpoint information")
public class ApiEndpointInfoDto {
    
    @Schema(description = "API path")
    private String path;
    
    @Schema(description = "HTTP methods")
    private List<String> httpMethods;
    
    @Schema(description = "Controller name")
    private String controllerName;
    
    @Schema(description = "Method name")
    private String methodName;
    
    @Schema(description = "Summary")
    private String summary;
    
    @Schema(description = "Description")
    private String description;
}
