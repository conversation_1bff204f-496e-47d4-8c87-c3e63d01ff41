package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.redberyl.invoiceapp.util.IdConverter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
public class DocumentTemplateVersionDto extends BaseDto {
    private Long id;

    @NotNull(message = "Template ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object templateId;

    // Include the full template object in the response
    @JsonProperty("template")
    private DocumentTemplateDto template;

    public Long getTemplateId() {
        return templateId != null ? IdConverter.extractId(templateId) : null;
    }

    public void setTemplateId(Object templateId) {
        this.templateId = templateId;
    }

    @NotNull(message = "Version number is required")
    private Integer versionNumber;

    @NotBlank(message = "Content is required")
    private String content;

    private String createdBy;
    private Boolean isActive;
}
