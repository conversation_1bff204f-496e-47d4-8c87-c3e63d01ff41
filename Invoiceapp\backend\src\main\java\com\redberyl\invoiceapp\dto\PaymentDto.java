package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class PaymentDto extends BaseDto {
    private Long id;

    @NotNull(message = "Invoice ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long invoiceId;

    // Remove circular reference to avoid compilation issues
    // private InvoiceDto invoice;

    @NotNull(message = "Amount received is required")
    @Positive(message = "Amount received must be positive")
    private BigDecimal amountReceived;

    @NotNull(message = "Received date is required")
    private LocalDate receivedOn;

    private String paymentMode;
    private String referenceNumber;
}
