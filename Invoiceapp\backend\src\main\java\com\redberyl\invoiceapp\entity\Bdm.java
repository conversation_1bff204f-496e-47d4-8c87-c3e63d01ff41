package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "bdms")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Bdm extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "email")
    private String email;

    @Column(name = "phone")
    private String phone;

    @Column(name = "gst_number")
    private String gstNumber;

    @Column(name = "billing_address")
    private String billingAddress;

    @Column(name = "commission_rate", precision = 5, scale = 2)
    @Builder.Default
    private BigDecimal commissionRate = BigDecimal.ZERO;

    @Column(name = "notes")
    private String notes;

    @OneToMany(mappedBy = "bdm", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<Project> projects = new HashSet<>();

    @OneToMany(mappedBy = "bdm", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<BdmPayment> payments = new HashSet<>();

    // Removed clients collection as Client entity no longer has bdm field
    // @OneToMany(mappedBy = "bdm", cascade = CascadeType.ALL, orphanRemoval = true)
    // private Set<Client> clients = new HashSet<>();
}
