package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "candidate_attendance", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"candidate_id", "month", "year"}))
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CandidateAttendance extends BaseEntity {

    @Id
    @SequenceGenerator(name = "candidate_attendance_seq", sequenceName = "candidate_attendance_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "candidate_attendance_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "candidate_id", nullable = false)
    private Candidate candidate;

    @Column(name = "month", nullable = false)
    private Integer month;

    @Column(name = "year", nullable = false)
    private Integer year;

    @Column(name = "days_worked", nullable = false)
    @Builder.Default
    private Integer daysWorked = 0;

    @Column(name = "daily_rate", nullable = false, precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal dailyRate = BigDecimal.ZERO;

    @Column(name = "calculated_salary", nullable = false, precision = 12, scale = 2)
    @Builder.Default
    private BigDecimal calculatedSalary = BigDecimal.ZERO;

    @Column(name = "notes")
    private String notes;

    @PrePersist
    @PreUpdate
    private void calculateSalary() {
        if (daysWorked != null && dailyRate != null) {
            this.calculatedSalary = dailyRate.multiply(BigDecimal.valueOf(daysWorked));
        }
    }
}
