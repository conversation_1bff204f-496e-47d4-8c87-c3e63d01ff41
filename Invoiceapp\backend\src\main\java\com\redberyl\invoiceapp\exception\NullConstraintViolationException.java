package com.redberyl.invoiceapp.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class NullConstraintViolationException extends RuntimeException {

    private final String fieldName;

    public NullConstraintViolationException(String fieldName) {
        super(String.format("Field '%s' cannot be null", fieldName));
        this.fieldName = fieldName;
    }

    public NullConstraintViolationException(String fieldName, String message) {
        super(message);
        this.fieldName = fieldName;
    }

    public String getFieldName() {
        return fieldName;
    }
}
