package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.GeneratedDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DocumentRepository extends JpaRepository<GeneratedDocument, Long> {
    List<GeneratedDocument> findByStatus(String status);
    List<GeneratedDocument> findByClientId(Long clientId);
    long countByStatus(String status);
}
