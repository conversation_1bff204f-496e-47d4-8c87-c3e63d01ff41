package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.GeneratedDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GeneratedDocumentRepository extends JpaRepository<GeneratedDocument, Long> {
    List<GeneratedDocument> findByClientId(Long clientId);
    List<GeneratedDocument> findByDealId(Long dealId);
    List<GeneratedDocument> findByTemplateId(Long templateId);
    List<GeneratedDocument> findByVersionId(Long versionId);
    List<GeneratedDocument> findByStatus(String status);
}
