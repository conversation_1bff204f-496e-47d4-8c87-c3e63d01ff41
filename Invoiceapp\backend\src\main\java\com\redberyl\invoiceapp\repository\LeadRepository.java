package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Lead;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LeadRepository extends JpaRepository<Lead, Long> {
    List<Lead> findByStatus(String status);

    List<Lead> findBySource(String source);

    Optional<Lead> findByEmail(String email);
}
