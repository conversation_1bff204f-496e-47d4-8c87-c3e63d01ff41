package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.CommunicationDto;

import java.util.List;

public interface CommunicationService {
    List<CommunicationDto> getAllCommunications();
    CommunicationDto getCommunicationById(Long id);
    List<CommunicationDto> getCommunicationsByClientId(Long clientId);
    List<CommunicationDto> getCommunicationsByLeadId(Long leadId);
    List<CommunicationDto> getCommunicationsByDealId(Long dealId);
    CommunicationDto createCommunication(CommunicationDto communicationDto);
    CommunicationDto updateCommunication(Long id, CommunicationDto communicationDto);
    void deleteCommunication(Long id);
}
