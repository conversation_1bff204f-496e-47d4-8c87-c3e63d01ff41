package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.HsnCodeDto;

import java.util.List;

public interface HsnCodeService {
    List<HsnCodeDto> getAllHsnCodes();
    HsnCodeDto getHsnCodeById(Long id);
    HsnCodeDto getHsnCodeByCode(String code);
    HsnCodeDto createHsnCode(HsnCodeDto hsnCodeDto);
    HsnCodeDto updateHsnCode(Long id, HsnCodeDto hsnCodeDto);
    void deleteHsnCode(Long id);
}
