package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.entity.Invoice;
import org.springframework.core.io.Resource;

/**
 * Service for generating invoice PDFs and other formats
 */
public interface InvoiceGenerationService {
    
    /**
     * Generate a PDF invoice from an invoice entity
     * 
     * @param invoice The invoice entity
     * @return The PDF as a resource
     */
    Resource generatePdfInvoice(Invoice invoice);
    
    /**
     * Generate a PDF invoice from an invoice DTO
     * 
     * @param invoiceDto The invoice DTO
     * @return The PDF as a resource
     */
    Resource generatePdfInvoiceFromDto(InvoiceDto invoiceDto);
    
    /**
     * Generate an HTML invoice from an invoice entity
     * 
     * @param invoice The invoice entity
     * @return The HTML content as a string
     */
    String generateHtmlInvoice(Invoice invoice);
    
    /**
     * Generate an HTML invoice from an invoice DTO
     * 
     * @param invoiceDto The invoice DTO
     * @return The HTML content as a string
     */
    String generateHtmlInvoiceFromDto(InvoiceDto invoiceDto);
}
