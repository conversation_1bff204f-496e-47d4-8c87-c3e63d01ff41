package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.InvoiceDto;

import java.time.LocalDate;
import java.util.List;

public interface InvoiceService {
    List<InvoiceDto> getAllInvoices();
    InvoiceDto getInvoiceById(Long id);
    InvoiceDto getInvoiceByNumber(String invoiceNumber);
    List<InvoiceDto> getInvoicesByClientId(Long clientId);
    List<InvoiceDto> getInvoicesByProjectId(Long projectId);
    List<InvoiceDto> getInvoicesByCandidateId(Long candidateId);
    List<InvoiceDto> getInvoicesByDateRange(LocalDate startDate, LocalDate endDate);
    List<InvoiceDto> getOverdueInvoices(LocalDate currentDate);
    List<InvoiceDto> getRecurringInvoices(Boolean isRecurring);
    InvoiceDto createInvoice(InvoiceDto invoiceDto);
    InvoiceDto updateInvoice(Long id, InvoiceDto invoiceDto);
    InvoiceDto publishInvoice(Long id);
    void deleteInvoice(Long id);
}
