package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.entity.Lead;

import java.util.List;
import java.util.Optional;

public interface LeadService {
    List<LeadDto> getAllLeads();
    LeadDto getLeadById(Long id);
    List<LeadDto> getLeadsByStatus(String status);
    List<LeadDto> getLeadsBySource(String source);
    LeadDto createLead(LeadDto leadDto);
    LeadDto updateLead(Long id, LeadDto leadDto);
    void deleteLead(Long id);
    Optional<Lead> findByEmail(String email);
}
