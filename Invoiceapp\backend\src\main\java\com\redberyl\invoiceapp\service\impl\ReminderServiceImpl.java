package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ReminderDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.Reminder;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.ReminderRepository;
import com.redberyl.invoiceapp.service.ReminderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ReminderServiceImpl implements ReminderService {

    @Autowired
    private ReminderRepository reminderRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<ReminderDto> getAllReminders() {
        List<Reminder> reminders = reminderRepository.findAll();
        if (reminders.isEmpty()) {
            throw new NoContentException("No reminders found");
        }
        return reminders.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ReminderDto getReminderById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id");
        }

        Reminder reminder = reminderRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Reminder not found with id: " + id));
        return convertToDto(reminder);
    }

    @Override
    public List<ReminderDto> getRemindersByInvoiceId(Long invoiceId) {
        if (invoiceId == null) {
            throw new NullConstraintViolationException("invoiceId");
        }

        // Check if invoice exists
        if (!invoiceRepository.existsById(invoiceId)) {
            throw new ResourceNotFoundException("Invoice not found with id: " + invoiceId);
        }

        List<Reminder> reminders = reminderRepository.findByInvoiceId(invoiceId);
        if (reminders.isEmpty()) {
            throw new NoContentException("No reminders found for invoice with id: " + invoiceId);
        }

        return reminders.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ReminderDto> getRemindersByMethod(String method) {
        if (!StringUtils.hasText(method)) {
            throw new NullConstraintViolationException("method");
        }

        List<Reminder> reminders = reminderRepository.findByMethod(method);
        if (reminders.isEmpty()) {
            throw new NoContentException("No reminders found with method: " + method);
        }

        return reminders.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ReminderDto> getRemindersByStatus(String status) {
        if (!StringUtils.hasText(status)) {
            throw new NullConstraintViolationException("status");
        }

        List<Reminder> reminders = reminderRepository.findByStatus(status);
        if (reminders.isEmpty()) {
            throw new NoContentException("No reminders found with status: " + status);
        }

        return reminders.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateReminderDto(ReminderDto reminderDto) {
        if (reminderDto == null) {
            throw new NullConstraintViolationException("reminderDto");
        }

        if (reminderDto.getInvoiceId() == null) {
            throw new NullConstraintViolationException("invoiceId");
        }

        if (!invoiceRepository.existsById(reminderDto.getInvoiceId())) {
            throw new ForeignKeyViolationException("invoiceId",
                    "Invoice not found with id: " + reminderDto.getInvoiceId());
        }

        if (!StringUtils.hasText(reminderDto.getMethod())) {
            throw new NullConstraintViolationException("method");
        }

        if (!StringUtils.hasText(reminderDto.getStatus())) {
            throw new NullConstraintViolationException("status");
        }
    }

    @Override
    @Transactional
    public ReminderDto createReminder(ReminderDto reminderDto) {
        validateReminderDto(reminderDto);

        try {
            Reminder reminder = convertToEntity(reminderDto);
            Reminder savedReminder = reminderRepository.save(reminder);
            return convertToDto(savedReminder);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating reminder: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating reminder", e);
        }
    }

    @Override
    @Transactional
    public ReminderDto updateReminder(Long id, ReminderDto reminderDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id");
        }

        if (reminderDto == null) {
            throw new NullConstraintViolationException("reminderDto");
        }

        Reminder existingReminder = reminderRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Reminder not found with id: " + id));

        try {
            if (StringUtils.hasText(reminderDto.getMethod())) {
                existingReminder.setMethod(reminderDto.getMethod());
            }

            if (StringUtils.hasText(reminderDto.getStatus())) {
                existingReminder.setStatus(reminderDto.getStatus());
            }

            if (reminderDto.getNote() != null) {
                existingReminder.setNote(reminderDto.getNote());
            }

            Reminder updatedReminder = reminderRepository.save(existingReminder);
            return convertToDto(updatedReminder);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating reminder: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | ForeignKeyViolationException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating reminder", e);
        }
    }

    @Override
    @Transactional
    public void deleteReminder(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id");
        }

        if (!reminderRepository.existsById(id)) {
            throw new ResourceNotFoundException("Reminder not found with id: " + id);
        }

        try {
            reminderRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete reminder because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting reminder: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting reminder", e);
        }
    }

    private ReminderDto convertToDto(Reminder reminder) {
        return ReminderDto.builder()
                .id(reminder.getId())
                .invoiceId(reminder.getInvoice().getId())
                .method(reminder.getMethod())
                .status(reminder.getStatus())
                .note(reminder.getNote())
                .build();
    }

    private Reminder convertToEntity(ReminderDto reminderDto) {
        Reminder reminder = new Reminder();
        reminder.setId(reminderDto.getId());

        if (reminderDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(reminderDto.getInvoiceId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Invoice not found with id: " + reminderDto.getInvoiceId()));
            reminder.setInvoice(invoice);
        }

        reminder.setMethod(reminderDto.getMethod());
        reminder.setStatus(reminderDto.getStatus());
        reminder.setNote(reminderDto.getNote());

        return reminder;
    }
}
