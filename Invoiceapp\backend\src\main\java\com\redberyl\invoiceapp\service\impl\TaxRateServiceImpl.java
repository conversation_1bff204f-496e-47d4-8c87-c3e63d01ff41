package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.entity.TaxRate;
import com.redberyl.invoiceapp.entity.TaxType;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.TaxRateRepository;
import com.redberyl.invoiceapp.repository.TaxTypeRepository;
import com.redberyl.invoiceapp.service.TaxRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TaxRateServiceImpl implements TaxRateService {

    @Autowired
    private TaxRateRepository taxRateRepository;

    @Autowired
    private TaxTypeRepository taxTypeRepository;

    @Override
    public List<TaxRateDto> getAllTaxRates() {
        List<TaxRate> taxRates = taxRateRepository.findAll();
        if (taxRates.isEmpty()) {
            throw new NoContentException("No tax rates found");
        }
        return taxRates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public TaxRateDto getTaxRateById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Tax Rate ID cannot be null");
        }

        TaxRate taxRate = taxRateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tax Rate not found with id: " + id));
        return convertToDto(taxRate);
    }

    @Override
    public List<TaxRateDto> getTaxRatesByTaxTypeId(Long taxTypeId) {
        if (taxTypeId == null) {
            throw new NullConstraintViolationException("taxTypeId", "Tax Type ID cannot be null");
        }

        // Check if tax type exists
        if (!taxTypeRepository.existsById(taxTypeId)) {
            throw new ResourceNotFoundException("Tax Type not found with id: " + taxTypeId);
        }

        List<TaxRate> taxRates = taxRateRepository.findByTaxTypeId(taxTypeId);
        if (taxRates.isEmpty()) {
            throw new NoContentException("No tax rates found for tax type with id: " + taxTypeId);
        }

        return taxRates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<TaxRateDto> getTaxRatesEffectiveOnDate(LocalDate date) {
        if (date == null) {
            throw new NullConstraintViolationException("date", "Date cannot be null");
        }

        List<TaxRate> taxRates = taxRateRepository.findByEffectiveFromLessThanEqualAndEffectiveToGreaterThanEqual(date,
                date);
        if (taxRates.isEmpty()) {
            throw new NoContentException("No tax rates found effective on date: " + date);
        }

        return taxRates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateTaxRateDto(TaxRateDto taxRateDto) {
        if (taxRateDto == null) {
            throw new NullConstraintViolationException("taxRateDto", "Tax Rate data cannot be null");
        }

        Long taxTypeId;
        try {
            taxTypeId = taxRateDto.getTaxTypeId();
            if (taxTypeId == null) {
                throw new NullConstraintViolationException("taxTypeId", "Tax Type ID cannot be null");
            }
        } catch (IllegalArgumentException e) {
            throw new CustomException("Invalid tax type ID format", e);
        }

        if (!taxTypeRepository.existsById(taxTypeId)) {
            throw new ForeignKeyViolationException("taxTypeId",
                    "Tax Type not found with id: " + taxTypeId);
        }

        if (taxRateDto.getRate() == null) {
            throw new NullConstraintViolationException("rate", "Rate cannot be null");
        }

        if (taxRateDto.getEffectiveFrom() == null) {
            throw new NullConstraintViolationException("effectiveFrom", "Effective From date cannot be null");
        }

        if (taxRateDto.getEffectiveTo() == null) {
            throw new NullConstraintViolationException("effectiveTo", "Effective To date cannot be null");
        }

        if (taxRateDto.getEffectiveFrom().isAfter(taxRateDto.getEffectiveTo())) {
            throw new CustomException("Effective From date cannot be after Effective To date", null);
        }
    }

    @Override
    @Transactional
    public TaxRateDto createTaxRate(TaxRateDto taxRateDto) {
        validateTaxRateDto(taxRateDto);

        try {
            TaxRate taxRate = convertToEntity(taxRateDto);
            TaxRate savedTaxRate = taxRateRepository.save(taxRate);
            return convertToDto(savedTaxRate);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("taxTypeId", "Referenced Tax Type does not exist");
            } else {
                throw new CustomException("Error creating Tax Rate: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating Tax Rate", e);
        }
    }

    @Override
    @Transactional
    public TaxRateDto updateTaxRate(Long id, TaxRateDto taxRateDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Tax Rate ID cannot be null");
        }

        if (taxRateDto == null) {
            throw new NullConstraintViolationException("taxRateDto", "Tax Rate data cannot be null");
        }

        TaxRate existingTaxRate = taxRateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tax Rate not found with id: " + id));

        try {
            // Handle tax type change if needed
            final Long taxTypeId;
            try {
                taxTypeId = taxRateDto.getTaxTypeId();
            } catch (IllegalArgumentException e) {
                throw new CustomException("Invalid tax type ID format", e);
            }

            if (taxTypeId != null &&
                    (existingTaxRate.getTaxType() == null ||
                            !existingTaxRate.getTaxType().getId().equals(taxTypeId))) {

                // Check if new tax type exists
                if (!taxTypeRepository.existsById(taxTypeId)) {
                    throw new ForeignKeyViolationException("taxTypeId",
                            "Tax Type not found with id: " + taxTypeId);
                }

                // Remove from old tax type if exists
                if (existingTaxRate.getTaxType() != null) {
                    existingTaxRate.getTaxType().removeTaxRate(existingTaxRate);
                }

                // Add to new tax type
                TaxType newTaxType = taxTypeRepository.findById(taxTypeId)
                        .orElseThrow(() -> new ResourceNotFoundException(
                                "Tax Type not found with id: " + taxTypeId));
                newTaxType.addTaxRate(existingTaxRate);
            }

            // Update rate if provided
            if (taxRateDto.getRate() != null) {
                existingTaxRate.setRate(taxRateDto.getRate());
            }

            // Update effective dates if provided
            if (taxRateDto.getEffectiveFrom() != null) {
                existingTaxRate.setEffectiveFrom(taxRateDto.getEffectiveFrom());
            }

            if (taxRateDto.getEffectiveTo() != null) {
                existingTaxRate.setEffectiveTo(taxRateDto.getEffectiveTo());
            }

            // Validate effective dates
            if (existingTaxRate.getEffectiveFrom() != null && existingTaxRate.getEffectiveTo() != null &&
                    existingTaxRate.getEffectiveFrom().isAfter(existingTaxRate.getEffectiveTo())) {
                throw new CustomException("Effective From date cannot be after Effective To date", null);
            }

            TaxRate updatedTaxRate = taxRateRepository.save(existingTaxRate);
            return convertToDto(updatedTaxRate);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("taxTypeId", "Referenced Tax Type does not exist");
            } else {
                throw new CustomException("Error updating Tax Rate: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | ForeignKeyViolationException
                | CustomException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating Tax Rate", e);
        }
    }

    @Override
    @Transactional
    public void deleteTaxRate(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Tax Rate ID cannot be null");
        }

        TaxRate taxRate = taxRateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tax Rate not found with id: " + id));

        try {
            // Properly remove from the parent entity to maintain the relationship
            if (taxRate.getTaxType() != null) {
                taxRate.getTaxType().removeTaxRate(taxRate);
            }

            taxRateRepository.delete(taxRate);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete Tax Rate because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting Tax Rate: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting Tax Rate", e);
        }
    }

    private TaxRateDto convertToDto(TaxRate taxRate) {
        // Start building the DTO with common fields
        TaxRateDto.TaxRateDtoBuilder builder = TaxRateDto.builder()
                .id(taxRate.getId())
                .rate(taxRate.getRate())
                .effectiveFrom(taxRate.getEffectiveFrom())
                .effectiveTo(taxRate.getEffectiveTo());

        // Set tax type if available
        if (taxRate.getTaxType() != null) {
            // Set the taxTypeId as a string
            builder.taxTypeId(taxRate.getTaxType().getId().toString());

            // Create and set the tax type DTO
            TaxTypeDto taxTypeDto = TaxTypeDto.builder()
                    .id(taxRate.getTaxType().getId())
                    .taxType(taxRate.getTaxType().getTaxType())
                    .taxTypeDescription(taxRate.getTaxType().getTaxTypeDescription())
                    .build();

            // Set audit fields for tax type
            taxTypeDto.setCreatedAt(taxRate.getTaxType().getCreatedAt());
            taxTypeDto.setUpdatedAt(taxRate.getTaxType().getModifiedAt());

            builder.taxType(taxTypeDto);
        }

        // Build the DTO
        TaxRateDto dto = builder.build();

        // Set the audit fields
        dto.setCreatedAt(taxRate.getCreatedAt());
        dto.setUpdatedAt(taxRate.getModifiedAt());

        return dto;
    }

    private TaxRate convertToEntity(TaxRateDto taxRateDto) {
        TaxRate taxRate = new TaxRate();
        taxRate.setId(taxRateDto.getId());

        final Long taxTypeId;
        try {
            taxTypeId = taxRateDto.getTaxTypeId();
        } catch (IllegalArgumentException e) {
            throw new CustomException("Invalid tax type ID format", e);
        }

        if (taxTypeId != null) {
            TaxType taxType = taxTypeRepository.findById(taxTypeId)
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Tax Type not found with id: " + taxTypeId));

            // Use the helper method to maintain the bidirectional relationship
            taxType.addTaxRate(taxRate);
        }

        taxRate.setRate(taxRateDto.getRate());
        taxRate.setEffectiveFrom(taxRateDto.getEffectiveFrom());
        taxRate.setEffectiveTo(taxRateDto.getEffectiveTo());

        return taxRate;
    }
}
