package com.redberyl.invoiceapp.util;

import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.entity.HsnCode;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.repository.HsnCodeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Utility to fix the RedBeryl account issue for INV-005
 */
@Component
public class FixInvoiceRedberylAccount implements CommandLineRunner {

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Override
    public void run(String... args) throws Exception {
        // Only run if specific argument is passed
        if (args.length > 0 && "fix-redberyl".equals(args[0])) {
            fixRedberylAccountIssue();
        }
    }

    public void fixRedberylAccountIssue() {
        try {
            System.out.println("=== FIXING REDBERYL ACCOUNT ISSUE ===");

            // 1. Create or find RedBeryl Account
            RedberylAccount account = redberylAccountRepository.findByAccountNo("**************").orElse(null);
            if (account == null) {
                account = new RedberylAccount();
                account.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
                account.setAccountNo("**************");
                account.setBankName("HDFC Bank Ltd.");
                account.setIfscCode("HDFC0000486");
                account.setBranchName("Destination Centre, Magarpatta, Pune");
                account.setAccountType("Current Account");
                account.setGstn("27**********1Z5");
                account.setCin("U72900PN2022PTC213381");
                account.setPanNo("**********");
                account = redberylAccountRepository.save(account);
                System.out.println("✓ Created RedBeryl Account with ID: " + account.getId());
            } else {
                System.out.println("✓ Found existing RedBeryl Account with ID: " + account.getId());
            }

            // 2. Create or find HSN Code
            HsnCode hsnCode = hsnCodeRepository.findByCode("998313").orElse(null);
            if (hsnCode == null) {
                hsnCode = new HsnCode();
                hsnCode.setCode("998313");
                hsnCode.setDescription("IT consulting services");
                hsnCode = hsnCodeRepository.save(hsnCode);
                System.out.println("✓ Created HSN Code with ID: " + hsnCode.getId());
            } else {
                System.out.println("✓ Found existing HSN Code with ID: " + hsnCode.getId());
            }

            // 3. Fix all invoices
            String[] invoiceNumbers = {"INV-001", "INV-004", "INV-005"};
            for (String invoiceNumber : invoiceNumbers) {
                Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);
                if (invoice != null) {
                    invoice.setRedberylAccount(account);
                    invoice.setHsnCode(hsnCode);
                    invoice = invoiceRepository.save(invoice);
                    System.out.println("✓ Fixed " + invoiceNumber + " - linked to RedBeryl Account and HSN Code");
                } else {
                    System.out.println("✗ Invoice " + invoiceNumber + " not found");
                }
            }

            System.out.println("=== FIX COMPLETED ===");
            System.out.println("All invoices should now show real RedBeryl account data in PDF generation!");

        } catch (Exception e) {
            System.err.println("Error fixing RedBeryl account issue: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
