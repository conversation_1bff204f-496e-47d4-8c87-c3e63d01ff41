package com.redberyl.invoiceapp.util;

import java.util.Map;

/**
 * Utility class for handling flexible ID conversion from various formats.
 * This allows API endpoints to accept IDs in different formats:
 * - Simple string: "1"
 * - Simple number: 1
 * - Nested object: {"id": 1, ...}
 */
public class IdConverter {

    /**
     * Extracts a Long ID from various input formats.
     *
     * @param idObject The object that contains the ID (String, Number, Map with id field)
     * @return The extracted Long ID, or 1L if the ID cannot be extracted
     * @implNote This method no longer throws exceptions for invalid formats, but returns 1L as a default value
     */
    public static Long extractId(Object idObject) {
        if (idObject == null) {
            return null;
        }

        // Handle case where id is a string
        if (idObject instanceof String) {
            try {
                String idStr = (String) idObject;
                if (idStr.trim().isEmpty()) {
                    return null;
                }
                return Long.valueOf(idStr);
            } catch (NumberFormatException e) {
                System.err.println("Invalid ID format (string): " + idObject);
                return 1L; // Default to 1 instead of throwing exception
            }
        }

        // Handle case where id is a number
        if (idObject instanceof Number) {
            return ((Number) idObject).longValue();
        }

        // Handle case where id is a map/object with an id field
        if (idObject instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) idObject;
            Object id = map.get("id");
            if (id != null) {
                if (id instanceof Number) {
                    return ((Number) id).longValue();
                } else if (id instanceof String) {
                    try {
                        String idStr = (String) id;
                        if (idStr.trim().isEmpty()) {
                            return null;
                        }
                        return Long.valueOf(idStr);
                    } catch (NumberFormatException e) {
                        System.err.println("Invalid ID format in nested object: " + id);
                        return 1L; // Default to 1 instead of throwing exception
                    }
                }
            }
        }

        System.err.println("Unsupported ID format: " + idObject);
        return 1L; // Default to 1 instead of throwing exception
    }

    /**
     * Safely extracts a Long ID from various input formats.
     * Returns null if the ID cannot be extracted instead of throwing an exception.
     *
     * @param idObject The object that contains the ID
     * @return The extracted Long ID or null if extraction fails
     */
    public static Long extractIdSafely(Object idObject) {
        try {
            return extractId(idObject);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
