<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RedBeryl Invoice Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        .logo {
            width: 200px;
            height: auto;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            color: #666;
            margin-bottom: 30px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .note {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" alt="RedBeryl Logo" class="logo">
        
        <h1>RedBeryl Invoice Management System</h1>
        <p>Welcome to the RedBeryl Invoice Management System. The React frontend should be running on port 3000.</p>
        
        <button onclick="redirectToFrontend()" class="btn">Go to Frontend Application</button>
        
        <div class="note">
            <strong>Fix for Refresh Issue:</strong>
            <br><br>
            <strong>Problem:</strong> When you refresh a page in the React app, you might end up here on port 8080.
            <br><br>
            <strong>Solution:</strong> Always use <code>http://localhost:3000</code> for the application.
            The backend (port 8080) is only for API calls.
            <br><br>
            <strong>Quick Fix:</strong>
            <ol style="text-align: left; margin-top: 10px;">
                <li>Click the button above to go to the frontend</li>
                <li>Bookmark <code>http://localhost:3000</code></li>
                <li>Always start from the bookmarked URL</li>
            </ol>
        </div>
    </div>

    <script>
        // Manual redirect function
        function redirectToFrontend() {
            const currentPath = window.location.pathname;
            const frontendUrl = 'http://localhost:3000' + currentPath;
            window.location.href = frontendUrl;
        }

        // Show a message if accessed via backend port
        if (window.location.port === '8080') {
            console.log('Accessed via backend port. Please use http://localhost:3000 for the frontend.');
        }
    </script>
</body>
</html>
