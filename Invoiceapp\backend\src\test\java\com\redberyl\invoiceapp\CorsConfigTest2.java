package com.redberyl.invoiceapp;

import org.junit.jupiter.api.Test;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

public class CorsConfigTest2 {

    @Test
    public void testCorsConfigurationWithAllowCredentialsAndWildcardOrigins() {
        CorsConfiguration config = new CorsConfiguration();
        
        // This should throw an exception because allowCredentials=true and origins="*" is not allowed
        config.setAllowCredentials(true);
        
        // This will throw an exception
        assertThrows(IllegalArgumentException.class, () -> {
            config.setAllowedOrigins(Arrays.asList("*"));
            config.validateAllowCredentials();
        });
    }
    
    @Test
    public void testCorsConfigurationWithAllowCredentialsAndSpecificOrigins() {
        CorsConfiguration config = new CorsConfiguration();
        
        // This should be valid
        config.setAllowCredentials(true);
        config.setAllowedOrigins(Arrays.asList("http://localhost:3000", "http://127.0.0.1:3000"));
        
        // This should not throw an exception
        assertDoesNotThrow(() -> {
            config.validateAllowCredentials();
        });
    }
    
    @Test
    public void testCorsConfigurationWithAllowCredentialsAndOriginPatterns() {
        CorsConfiguration config = new CorsConfiguration();
        
        // This should be valid
        config.setAllowCredentials(true);
        config.setAllowedOriginPatterns(Arrays.asList("*"));
        
        // This should not throw an exception
        assertDoesNotThrow(() -> {
            config.validateAllowCredentials();
        });
    }
}
