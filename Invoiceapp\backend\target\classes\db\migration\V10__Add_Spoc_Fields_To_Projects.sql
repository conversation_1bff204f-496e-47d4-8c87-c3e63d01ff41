-- Add SPOC reference columns to projects table
ALTER TABLE projects ADD COLUMN IF NOT EXISTS manager_spoc_id INT REFERENCES spocs(id);
ALTER TABLE projects ADD COLUMN IF NOT EXISTS account_head_spoc_id INT REFERENCES spocs(id);
ALTER TABLE projects ADD COLUMN IF NOT EXISTS business_head_spoc_id INT REFERENCES spocs(id);
ALTER TABLE projects ADD COLUMN IF NOT EXISTS hr_spoc_id INT REFERENCES spocs(id);
ALTER TABLE projects ADD COLUMN IF NOT EXISTS finance_spoc_id INT REFERENCES spocs(id);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_projects_manager_spoc_id ON projects(manager_spoc_id);
CREATE INDEX IF NOT EXISTS idx_projects_account_head_spoc_id ON projects(account_head_spoc_id);
CREATE INDEX IF NOT EXISTS idx_projects_business_head_spoc_id ON projects(business_head_spoc_id);
CREATE INDEX IF NOT EXISTS idx_projects_hr_spoc_id ON projects(hr_spoc_id);
CREATE INDEX IF NOT EXISTS idx_projects_finance_spoc_id ON projects(finance_spoc_id);
