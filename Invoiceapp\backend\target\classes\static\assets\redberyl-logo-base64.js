// RedBeryl Logo as base64 encoded data URL
// This can be used in PDFs and other contexts where external images might not load

export const REDBERYL_LOGO_BASE64 = `data:image/svg+xml;base64,${btoa(`
<svg width="600" height="240" viewBox="0 0 600 240" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="600" height="240" fill="white"/>
  
  <!-- Cloud-like interconnected shapes representing the RedBeryl logo -->
  <g transform="translate(30, 60)">
    <!-- Left blue cloud shape -->
    <path 
      d="M0 60 C0 27, 27 0, 60 0 C93 0, 120 27, 120 60 C120 93, 93 120, 60 120 C27 120, 0 93, 0 60 Z" 
      fill="#4A90E2"
      opacity="0.9"
    />
    
    <!-- Right pink/magenta cloud shape -->
    <path 
      d="M80 40 C80 18, 98 0, 120 0 C142 0, 160 18, 160 40 C160 62, 142 80, 120 80 C98 80, 80 62, 80 40 Z" 
      fill="#E91E63"
      opacity="0.9"
    />
    
    <!-- Bottom connecting cloud -->
    <path 
      d="M40 80 C40 62, 54 48, 72 48 C90 48, 104 62, 104 80 C104 98, 90 112, 72 112 C54 112, 40 98, 40 80 Z" 
      fill="#8E44AD"
      opacity="0.8"
    />
    
    <!-- Small connecting dots/elements -->
    <circle cx="90" cy="60" r="6" fill="#3498DB"/>
    <circle cx="70" cy="70" r="4" fill="#E74C3C"/>
    <circle cx="110" cy="50" r="3" fill="#F39C12"/>
  </g>
  
  <!-- RedBeryl Text -->
  <g transform="translate(220, 60)">
    <!-- Red text -->
    <text x="0" y="45" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#E91E63">Red</text>
    
    <!-- Beryl text -->
    <text x="105" y="45" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#1565C0">Beryl</text>
    
    <!-- TECH SOLUTIONS -->
    <text x="0" y="75" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#666" letter-spacing="3px">TECH SOLUTIONS</text>
    
    <!-- Tagline -->
    <text x="0" y="100" font-family="Arial, sans-serif" font-size="14" fill="#888" font-style="italic">Integrates Business With Technology</text>
  </g>
</svg>
`)}`;
