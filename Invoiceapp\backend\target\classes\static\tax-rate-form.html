<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Tax Rate</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .result {
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>Create Tax Rate</h1>
    
    <div class="form-group">
        <label for="taxTypeId">Tax Type ID:</label>
        <input type="number" id="taxTypeId" value="1">
    </div>
    
    <div class="form-group">
        <label for="rate">Rate (%):</label>
        <input type="number" id="rate" step="0.01" value="18.0">
    </div>
    
    <div class="form-group">
        <label for="effectiveFrom">Effective From:</label>
        <input type="date" id="effectiveFrom" value="2025-04-17">
    </div>
    
    <div class="form-group">
        <label for="effectiveTo">Effective To:</label>
        <input type="date" id="effectiveTo" value="2025-06-22">
    </div>
    
    <button onclick="generateJson()">Generate JSON</button>
    <button onclick="createTaxRate()">Create Tax Rate</button>
    
    <div class="result" id="jsonResult">
        <h2>JSON Payload:</h2>
        <pre id="jsonPayload"></pre>
    </div>
    
    <div class="result" id="apiResult">
        <h2>API Response:</h2>
        <pre id="apiResponse"></pre>
    </div>
    
    <script>
        function generateJson() {
            const taxTypeId = parseInt(document.getElementById('taxTypeId').value);
            const rate = parseFloat(document.getElementById('rate').value);
            const effectiveFrom = document.getElementById('effectiveFrom').value;
            const effectiveTo = document.getElementById('effectiveTo').value;
            
            const payload = {
                taxTypeId: taxTypeId,
                rate: rate,
                effectiveFrom: effectiveFrom,
                effectiveTo: effectiveTo
            };
            
            const jsonPayload = JSON.stringify(payload, null, 2);
            document.getElementById('jsonPayload').textContent = jsonPayload;
            document.getElementById('jsonResult').style.display = 'block';
        }
        
        async function createTaxRate() {
            const taxTypeId = parseInt(document.getElementById('taxTypeId').value);
            const rate = parseFloat(document.getElementById('rate').value);
            const effectiveFrom = document.getElementById('effectiveFrom').value;
            const effectiveTo = document.getElementById('effectiveTo').value;
            
            const payload = {
                taxTypeId: taxTypeId,
                rate: rate,
                effectiveFrom: effectiveFrom,
                effectiveTo: effectiveTo
            };
            
            try {
                const response = await fetch('/api/tax-rates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                document.getElementById('apiResult').style.display = 'block';
            } catch (error) {
                document.getElementById('apiResponse').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
            }
        }
    </script>
</body>
</html>
