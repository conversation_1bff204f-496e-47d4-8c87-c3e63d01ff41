C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\DocumentTemplateDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\RedberylAccountServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\TaxType.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\LeadDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\SpocService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\InvoiceMilestone.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\InvoiceMilestoneService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\DashboardServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\CandidateController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\DocumentTemplateVersionService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\CandidateServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\CommunicationController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DashboardController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\ApiEndpointInfoDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\InvoiceGenerationServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\TaxRate.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\BdmPayment.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\ReminderRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\InvoiceApplication.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\PublicController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\ForeignKeyViolationException.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\BdmServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\InvoiceMilestoneController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\SampleInvoiceDataInitializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\ProjectServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\ClientController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\CustomLeadSerializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\HsnCodeRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\SwaggerTestController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\PublicDealController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\filter\CustomCorsFilter.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\ClientDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\StaffingTypeController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\auth\ERole.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\RedberylAccountController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\SwaggerRedirectController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\util\FixInvoiceRedberylAccount.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\DocumentRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\CommunicationService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\InvoiceAuditLogController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DocumentTemplateController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\InvoiceTax.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\LeadServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\auth\LoginRequestDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\InvoiceTypeDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Payment.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\SwaggerExampleController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\StaffingTypeRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\PublicInvoiceTypeController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\security\jwt\AuthEntryPointJwt.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\util\IdConverter.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\CandidateRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\HsnCodeServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\PaymentService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\HsnCode.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\SpocController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\BdmRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\CandidateDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\StaffingType.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\TaxRateRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\InvoiceAuditLog.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\StaffingTypeServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\BdmController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\ProjectDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\WebConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Communication.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\DocumentTemplate.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\EntityRelationshipController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\BdmPaymentServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\auth\User.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\DealDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\CandidateService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\GeneratedDocumentService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\StaffingTypeDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\InvoiceTypeServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\DashboardService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\ResourceNotFoundException.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\TaxRateNestedController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\DocumentTemplateServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Candidate.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\GeneratedDocumentController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\InvoiceTypeService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Deal.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\ReminderController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\CommunicationServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\CommunicationDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\HsnCodeService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\BdmPaymentService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\dashboard\DashboardMetricsDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\CustomClientSerializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DealController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\StaffingTypeService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DocumentVariableController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\SwaggerUiConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\ProjectController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\RootController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\BdmService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\CustomCandidateSerializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\EntityTableController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\InvoiceServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\FixController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\InvoiceMilestoneRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\DocumentVariableService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\TaxTypeServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Project.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\security\jwt\JwtUtils.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\ReminderService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\InvoiceTypeController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\DocumentTemplateRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\CustomException.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\TaxRateService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\InvoiceTaxService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Client.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\InvoiceGenerationService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\ReminderDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\CommunicationRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\LeadController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\InvoiceMilestoneDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\PublicClientController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\auth\SignupRequestDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\LeadService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\CorsConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\NoAuthController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\auth\MessageResponseDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\TaxTypeDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\DocumentTemplateVersionRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\InvoiceAuditLogDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Invoice.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\InvoiceTaxController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\auth\Role.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DirectAccessInvoiceTypeController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\InvoiceService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Bdm.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\InvoiceType.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\NullConstraintViolationException.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\security\services\UserDetailsServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\TaxRateController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\SpocServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\SampleDataInitializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\RedberylAccount.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\FixedTaxRateController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\security\jwt\AuthTokenFilter.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\BdmCompatController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\BdmPaymentController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\ProjectRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\ErrorDetails.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\GeneratedDocumentRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\HtmlController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\PaymentDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Lead.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\GeneratedDocumentServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\SwaggerPathCustomizer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\ClientServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\GlobalExceptionHandler.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\RedberylAccountDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\TaxRateCreateRequestDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\TestController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\TaxRateDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\TaxRateServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\CustomTaxRateSerializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\SwaggerConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\RoleRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\DealRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\DocumentTemplateService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\DocumentVariableServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\InvoiceTaxRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\ApiResponseDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\AuditingConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\ReminderServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\PaymentServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DocumentTemplateVersionController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\InvoiceDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\DocumentVariableRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\ComponentFieldConfigController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\EntityTableInfoDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\BdmPaymentRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\TaxTypeService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\RedberylAccountRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\JacksonConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\FlexibleIdDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\LeadRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\SwaggerExampleConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\security\WebSecurityConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\util\InvoiceValidator.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\DealService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\NewDocumentTemplateVersionController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DebugController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\TestDocumentTemplateVersionController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\DocumentTemplateVersionDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\BaseDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\InvoiceTypeDataInitializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\CollectionFieldConfigController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\InvoiceAuditLogService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\ExampleController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\ClientService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\DataInitializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\ClientExampleController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\DocumentVariableDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\DirectAccessController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\GeneratedDocument.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\InvoiceGenerationController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\DocumentVariable.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\MessageResponseDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\EntityNotFoundException.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\auth\LoginResponseDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\PagedResponse.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\DocumentTemplateVersion.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\UserRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\InvoiceTypeRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\BaseEntity.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\RedberylAccountService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\RoleController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\EntityRelationshipDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Spoc.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\BdmDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\TaxTypeRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\HsnCodeController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\auth\RoleDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\NoAuthInvoiceTypeController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\InvoiceAuditLogRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\PaymentRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\BdmPaymentDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\SpocRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\InvoiceTaxServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\InvoiceAuditLogServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\CustomDealSerializer.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\DealServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\HsnCodeDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\UniqueConstraintViolationException.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\SimplifiedCollectionController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\InvoiceTaxDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\DocumentTemplateVersionServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\InvoiceRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\ProjectService.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\NoContentException.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\TaxTypeController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\config\SimplifiedSwaggerConfig.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\repository\ClientRepository.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\security\services\UserDetailsImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\GeneratedDocumentDto.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\AuthController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\CorsTestController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\exception\ValidationErrorDetails.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\entity\Reminder.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\service\impl\InvoiceMilestoneServiceImpl.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\PaymentController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\controller\InvoiceController.java
C:\Users\<USER>\Desktop\Invoiceapp\Invoiceapp\backend\src\main\java\com\redberyl\invoiceapp\dto\SpocDto.java
