-- Fix INV-005 RedBeryl Account Issue
-- This script creates a RedBeryl account and links it to INV-005

-- 1. Create RedBeryl Account if it doesn't exist
INSERT INTO redberyl_accounts (
    account_name, 
    account_no, 
    bank_name, 
    ifsc_code, 
    branch_name, 
    account_type, 
    gstn, 
    cin, 
    pan_no,
    created_at,
    updated_at
) VALUES (
    'RedBeryl Tech Solutions Pvt Ltd.',
    '**************',
    'HDFC Bank Ltd.',
    'HDFC0000486',
    'Destination Centre, Magarpatta, Pune',
    'Current Account',
    '27**********1Z5',
    'U72900PN2022PTC213381',
    '**********',
    NOW(),
    NOW()
) ON CONFLICT (account_no) DO NOTHING;

-- 2. Create HSN Code if it doesn't exist
INSERT INTO hsn_codes (
    code,
    description,
    created_at,
    updated_at
) VALUES (
    '998313',
    'IT consulting services',
    NOW(),
    NOW()
) ON CONFLICT (code) DO NOTHING;

-- 3. Update INV-005 to link to RedBeryl account and HSN code
UPDATE invoices 
SET 
    redberyl_account_id = (SELECT id FROM redberyl_accounts WHERE account_no = '**************' LIMIT 1),
    hsn_id = (SELECT id FROM hsn_codes WHERE code = '998313' LIMIT 1),
    updated_at = NOW()
WHERE invoice_number = 'INV-005';

-- 4. Verify the update
SELECT 
    i.id,
    i.invoice_number,
    i.redberyl_account_id,
    i.hsn_id,
    ra.account_name,
    ra.account_no,
    ra.bank_name,
    hc.code as hsn_code
FROM invoices i
LEFT JOIN redberyl_accounts ra ON i.redberyl_account_id = ra.id
LEFT JOIN hsn_codes hc ON i.hsn_id = hc.id
WHERE i.invoice_number = 'INV-005';
