Stack trace:
Frame         Function      Args
0007FFFFC020  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAF20) msys-2.0.dll+0x2118E
0007FFFFC020  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFC020  0002100469F2 (00021028DF99, 0007FFFFBED8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFC020  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFC020  00021006A545 (0007FFFFC030, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFC030, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE86AD0000 ntdll.dll
7FFE86080000 KERNEL32.DLL
7FFE83E40000 KERNELBASE.dll
7FFE847D0000 USER32.dll
7FFE84580000 win32u.dll
7FFE86770000 GDI32.dll
7FFE845B0000 gdi32full.dll
7FFE83BC0000 msvcp_win.dll
7FFE83D20000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE867A0000 advapi32.dll
7FFE865E0000 msvcrt.dll
7FFE86480000 sechost.dll
7FFE84220000 bcrypt.dll
7FFE86880000 RPCRT4.dll
7FFE83230000 CRYPTBASE.DLL
7FFE846E0000 bcryptPrimitives.dll
7FFE85400000 IMM32.DLL
