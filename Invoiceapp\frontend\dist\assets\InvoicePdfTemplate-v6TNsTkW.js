import{j as t,f as d}from"./index-wZf-EOI5.js";const s=({invoice:o})=>{const e={...o,client:o.client||"Unknown Client",project:o.project||"Unknown Project",candidate:o.candidate||"-",invoiceType:o.invoiceType||"Standard",staffingType:o.staffingType||"Full-time",amount:o.amount||"₹0.00",tax:o.tax||"₹0.00",total:o.total||"₹0.00",issueDate:o.issueDate||new Date().toISOString().split("T")[0],dueDate:o.dueDate||new Date().toISOString().split("T")[0],status:o.status||"Draft",recurring:o.recurring||!1,notes:o.notes||""};console.log("InvoicePdfTemplate: Rendering with data:",e);const i=(n=>{switch(n.toLowerCase()){case"paid":return{bg:"#d1fae5",text:"#065f46"};case"pending":return{bg:"#fef3c7",text:"#92400e"};case"overdue":return{bg:"#fee2e2",text:"#b91c1c"};case"draft":return{bg:"#f3f4f6",text:"#374151"};default:return{bg:"#f3f4f6",text:"#374151"}}})(e.status);return t.jsxs("div",{id:"invoice-pdf-content",style:{fontFamily:"Arial, sans-serif",padding:"20px",maxWidth:"800px",margin:"0 auto",backgroundColor:"white",color:"#333"},children:[t.jsxs("div",{style:{textAlign:"center",marginBottom:"30px"},children:[t.jsx("h1",{style:{fontSize:"24px",fontWeight:"bold",marginBottom:"5px",color:"#1f2937"},children:"INVOICE"}),t.jsx("div",{style:{fontSize:"16px",color:"#666",marginBottom:"10px"},children:e.id}),t.jsx("div",{style:{display:"inline-block",padding:"5px 10px",borderRadius:"4px",fontSize:"14px",backgroundColor:i.bg,color:i.text},children:e.status}),e.recurring&&t.jsx("div",{style:{display:"inline-block",padding:"5px 10px",borderRadius:"4px",fontSize:"14px",backgroundColor:"#dbeafe",color:"#1e40af",marginLeft:"10px"},children:"Recurring"})]}),t.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px",marginBottom:"20px"},children:[t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Client"}),t.jsx("div",{style:{fontSize:"16px",fontWeight:"bold"},children:e.client})]}),t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Project"}),t.jsx("div",{style:{fontSize:"16px",fontWeight:"bold"},children:e.project})]}),e.candidate&&e.candidate!=="-"&&t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Candidate"}),t.jsx("div",{style:{fontSize:"16px",fontWeight:"bold"},children:e.candidate})]}),e.invoiceType&&t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Invoice Type"}),t.jsx("div",{children:e.invoiceType})]}),e.staffingType&&t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Staffing Type"}),t.jsx("div",{children:e.staffingType})]}),t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Issue Date"}),t.jsx("div",{children:d(new Date(e.issueDate),"MMMM d, yyyy")})]}),t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Due Date"}),t.jsx("div",{children:d(new Date(e.dueDate),"MMMM d, yyyy")})]}),e.hsnCode&&t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"HSN Code"}),t.jsx("div",{children:e.hsnCode})]}),e.redberylAccount&&t.jsxs("div",{children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Redberyl Account"}),t.jsx("div",{children:e.redberylAccount})]})]}),t.jsxs("table",{style:{width:"100%",borderCollapse:"collapse",marginTop:"20px",marginBottom:"20px"},children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{style:{backgroundColor:"#f3f4f6",textAlign:"left",padding:"10px",borderBottom:"1px solid #e5e7eb"},children:"Description"}),t.jsx("th",{style:{backgroundColor:"#f3f4f6",textAlign:"right",padding:"10px",borderBottom:"1px solid #e5e7eb"},children:"Billing Amount"}),t.jsx("th",{style:{backgroundColor:"#f3f4f6",textAlign:"right",padding:"10px",borderBottom:"1px solid #e5e7eb"},children:"Tax Amount"}),t.jsx("th",{style:{backgroundColor:"#f3f4f6",textAlign:"right",padding:"10px",borderBottom:"1px solid #e5e7eb"},children:"Total Amount"})]})}),t.jsx("tbody",{children:t.jsxs("tr",{children:[t.jsxs("td",{style:{padding:"10px",borderBottom:"1px solid #e5e7eb"},children:[e.project,e.candidate&&e.candidate!=="-"?` - ${e.candidate}`:""]}),t.jsx("td",{style:{padding:"10px",borderBottom:"1px solid #e5e7eb",textAlign:"right"},children:e.amount}),t.jsx("td",{style:{padding:"10px",borderBottom:"1px solid #e5e7eb",textAlign:"right"},children:e.tax}),t.jsx("td",{style:{padding:"10px",borderBottom:"1px solid #e5e7eb",textAlign:"right"},children:e.total})]})})]}),t.jsxs("div",{style:{fontSize:"18px",fontWeight:"bold",textAlign:"right",marginTop:"20px",marginBottom:"20px"},children:["Total Amount: ",e.total]}),e.notes&&t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"5px",color:"#6b7280"},children:"Notes"}),t.jsx("div",{children:e.notes})]}),t.jsx("div",{style:{marginTop:"50px",textAlign:"center",color:"#6b7280",fontSize:"14px",borderTop:"1px solid #e5e7eb",paddingTop:"20px"},children:"Thank you for your business!"})]})};export{s as default};
