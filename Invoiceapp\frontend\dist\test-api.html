<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div>
        <button onclick="testDirectBackend()">Test Direct Backend</button>
        <button onclick="testProxiedBackend()">Test Proxied Backend</button>
        <button onclick="testSignup()">Test Signup</button>
    </div>
    
    <div id="result">Results will appear here...</div>
    
    <script>
        // Function to display results
        function displayResult(title, data) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<h3>${title}</h3>`;
            
            if (typeof data === 'object') {
                resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                resultDiv.innerHTML += `<pre>${data}</pre>`;
            }
        }
        
        // Test direct backend connection
        async function testDirectBackend() {
            try {
                displayResult('Testing direct backend connection...', 'Sending request to http://localhost:8080/api/test/ping');
                
                const response = await fetch('http://localhost:8080/api/test/ping', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                displayResult('Direct Backend Response', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                });
            } catch (error) {
                displayResult('Direct Backend Error', error.message);
            }
        }
        
        // Test proxied backend connection
        async function testProxiedBackend() {
            try {
                displayResult('Testing proxied backend connection...', 'Sending request to /api/test/ping');
                
                const response = await fetch('/api/test/ping', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                displayResult('Proxied Backend Response', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                });
            } catch (error) {
                displayResult('Proxied Backend Error', error.message);
            }
        }
        
        // Test signup
        async function testSignup() {
            try {
                // Generate random username and email to avoid duplicates
                const random = Math.floor(Math.random() * 10000);
                const username = `testuser${random}`;
                const email = `test${random}@example.com`;
                const password = 'password123';
                
                displayResult('Testing signup...', `Sending signup request for ${username} (${email})`);
                
                // First try direct backend
                try {
                    const directResponse = await fetch('http://localhost:8080/api/auth/signup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username + '_direct',
                            email: 'direct_' + email,
                            password: password,
                            roles: ['user']
                        })
                    });
                    
                    let directResult;
                    try {
                        directResult = await directResponse.json();
                    } catch (e) {
                        directResult = await directResponse.text();
                    }
                    
                    displayResult('Direct Signup Response', {
                        status: directResponse.status,
                        statusText: directResponse.statusText,
                        data: directResult
                    });
                } catch (directError) {
                    displayResult('Direct Signup Error', directError.message);
                }
                
                // Then try proxied backend
                try {
                    const proxiedResponse = await fetch('/api/auth/signup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username + '_proxied',
                            email: 'proxied_' + email,
                            password: password,
                            roles: ['user']
                        })
                    });
                    
                    let proxiedResult;
                    try {
                        proxiedResult = await proxiedResponse.json();
                    } catch (e) {
                        proxiedResult = await proxiedResponse.text();
                    }
                    
                    displayResult('Proxied Signup Response', {
                        status: proxiedResponse.status,
                        statusText: proxiedResponse.statusText,
                        data: proxiedResult
                    });
                } catch (proxiedError) {
                    displayResult('Proxied Signup Error', proxiedError.message);
                }
            } catch (error) {
                displayResult('Signup Test Error', error.message);
            }
        }
    </script>
</body>
</html>
