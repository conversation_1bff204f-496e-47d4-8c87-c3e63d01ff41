/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
import*as e from"react";import{UNSAFE_invariant as t,joinPaths as r,matchPath as n,UNSAFE_decodePath as a,UNSAFE_getResolveToMatches as o,resolveTo as i,parsePath as l,matchRoutes as u,Action as s,UNSAFE_convertRouteMatchToUiMatch as c,stripBasename as d,IDLE_BLOCKER as p,isRouteErrorResponse as m,createMemoryHistory as h,AbortedDeferredError as f,createRouter as v}from"@remix-run/router";export{AbortedDeferredError,Action as NavigationType,createPath,defer,generatePath,isRouteErrorResponse,json,matchPath,matchRoutes,parsePath,redirect,redirectDocument,replace,resolvePath}from"@remix-run/router";const E=e.createContext(null),g=e.createContext(null),y=e.createContext(null),x=e.createContext(null),C=e.createContext(null),b=e.createContext({outlet:null,matches:[],isDataRoute:!1}),R=e.createContext(null);function S(n,{relative:a}={}){_()||t(!1);let{basename:o,navigator:i}=e.useContext(x),{hash:l,pathname:u,search:s}=A(n,{relative:a}),c=u;return"/"!==o&&(c="/"===u?o:r([o,u])),i.createHref({pathname:c,search:s,hash:l})}function _(){return null!=e.useContext(C)}function P(){return _()||t(!1),e.useContext(C).location}function U(){return e.useContext(C).navigationType}function k(r){_()||t(!1);let{pathname:o}=P();return e.useMemo((()=>n(r,a(o))),[o,r])}function D(t){e.useContext(x).static||e.useLayoutEffect(t)}function N(){let{isDataRoute:n}=e.useContext(b);return n?function(){let{router:t}=V(J.UseNavigateStable),r=Y($.UseNavigateStable),n=e.useRef(!1);return D((()=>{n.current=!0})),e.useCallback(((e,a={})=>{n.current&&("number"==typeof e?t.navigate(e):t.navigate(e,{fromRouteId:r,...a}))}),[t,r])}():function(){_()||t(!1);let n=e.useContext(E),{basename:a,future:l,navigator:u}=e.useContext(x),{matches:s}=e.useContext(b),{pathname:c}=P(),d=JSON.stringify(o(s,l.v7_relativeSplatPath)),p=e.useRef(!1);return D((()=>{p.current=!0})),e.useCallback(((e,t={})=>{if(!p.current)return;if("number"==typeof e)return void u.go(e);let o=i(e,JSON.parse(d),c,"path"===t.relative);null==n&&"/"!==a&&(o.pathname="/"===o.pathname?a:r([a,o.pathname])),(t.replace?u.replace:u.push)(o,t.state,t)}),[a,u,d,c,n])}()}const B=e.createContext(null);function F(){return e.useContext(B)}function L(t){let r=e.useContext(b).outlet;return r?e.createElement(B.Provider,{value:t},r):r}function O(){let{matches:t}=e.useContext(b),r=t[t.length-1];return r?r.params:{}}function A(t,{relative:r}={}){let{future:n}=e.useContext(x),{matches:a}=e.useContext(b),{pathname:l}=P(),u=JSON.stringify(o(a,n.v7_relativeSplatPath));return e.useMemo((()=>i(t,JSON.parse(u),l,"path"===r)),[t,u,l,r])}function j(e,t){return I(e,t)}function I(n,a,o,i){_()||t(!1);let{navigator:c}=e.useContext(x),{matches:d}=e.useContext(b),p=d[d.length-1],m=p?p.params:{};!p||p.pathname;let h=p?p.pathnameBase:"/";p&&p.route;let f,v=P();if(a){let e="string"==typeof a?l(a):a;"/"===h||e.pathname?.startsWith(h)||t(!1),f=e}else f=v;let E=f.pathname||"/",g=E;if("/"!==h){let e=h.replace(/^\//,"").split("/");g="/"+E.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=u(n,{pathname:g}),R=z(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},m,e.params),pathname:r([h,c.encodeLocation?c.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:r([h,c.encodeLocation?c.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),d,o,i);return a&&R?e.createElement(C.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...f},navigationType:s.Pop}},R):R}function M(){let t=te(),r=m(t)?`${t.status} ${t.statusText}`:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},r),n?e.createElement("pre",{style:a},n):null,null)}const T=e.createElement(M,null);class H extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?e.createElement(b.Provider,{value:this.props.routeContext},e.createElement(R.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function w({routeContext:t,match:r,children:n}){let a=e.useContext(E);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),e.createElement(b.Provider,{value:t},n)}function z(r,n=[],a=null,o=null){if(null==r){if(!a)return null;if(a.errors)r=a.matches;else{if(!(o?.v7_partialHydration&&0===n.length&&!a.initialized&&a.matches.length>0))return null;r=a.matches}}let i=r,l=a?.errors;if(null!=l){let e=i.findIndex((e=>e.route.id&&void 0!==l?.[e.route.id]));e>=0||t(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,s=-1;if(a&&o&&o.v7_partialHydration)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(s=e),t.route.id){let{loaderData:e,errors:r}=a,n=t.route.loader&&void 0===e[t.route.id]&&(!r||void 0===r[t.route.id]);if(t.route.lazy||n){u=!0,i=s>=0?i.slice(0,s+1):[i[0]];break}}}return i.reduceRight(((t,r,o)=>{let c,d=!1,p=null,m=null;var h;a&&(c=l&&r.route.id?l[r.route.id]:void 0,p=r.route.errorElement||T,u&&(s<0&&0===o?(h="route-fallback",!1||ie[h]||(ie[h]=!0),d=!0,m=null):s===o&&(d=!0,m=r.route.hydrateFallbackElement||null)));let f=n.concat(i.slice(0,o+1)),v=()=>{let n;return n=c?p:d?m:r.route.Component?e.createElement(r.route.Component,null):r.route.element?r.route.element:t,e.createElement(w,{match:r,routeContext:{outlet:t,matches:f,isDataRoute:null!=a},children:n})};return a&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?e.createElement(H,{location:a.location,revalidation:a.revalidation,component:p,error:c,children:v(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):v()}),null)}var J=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(J||{}),$=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}($||{});function V(r){let n=e.useContext(E);return n||t(!1),n}function W(r){let n=e.useContext(g);return n||t(!1),n}function Y(r){let n=function(r){let n=e.useContext(b);return n||t(!1),n}(),a=n.matches[n.matches.length-1];return a.route.id||t(!1),a.route.id}function q(){return Y($.UseRouteId)}function G(){return W($.UseNavigation).navigation}function K(){let t=V(J.UseRevalidator),r=W($.UseRevalidator);return e.useMemo((()=>({revalidate:t.router.revalidate,state:r.revalidation})),[t.router.revalidate,r.revalidation])}function Q(){let{matches:t,loaderData:r}=W($.UseMatches);return e.useMemo((()=>t.map((e=>c(e,r)))),[t,r])}function X(){let e=W($.UseLoaderData),t=Y($.UseLoaderData);if(!e.errors||null==e.errors[t])return e.loaderData[t];console.error(`You cannot \`useLoaderData\` in an errorElement (routeId: ${t})`)}function Z(e){return W($.UseRouteLoaderData).loaderData[e]}function ee(){let e=W($.UseActionData),t=Y($.UseLoaderData);return e.actionData?e.actionData[t]:void 0}function te(){let t=e.useContext(R),r=W($.UseRouteError),n=Y($.UseRouteError);return void 0!==t?t:r.errors?.[n]}function re(){return e.useContext(y)?._data}function ne(){return e.useContext(y)?._error}let ae=0;function oe(t){let{router:r,basename:n}=V(J.UseBlocker),a=W($.UseBlocker),[o,i]=e.useState(""),l=e.useCallback((e=>{if("function"!=typeof t)return!!t;if("/"===n)return t(e);let{currentLocation:r,nextLocation:a,historyAction:o}=e;return t({currentLocation:{...r,pathname:d(r.pathname,n)||r.pathname},nextLocation:{...a,pathname:d(a.pathname,n)||a.pathname},historyAction:o})}),[n,t]);return e.useEffect((()=>{let e=String(++ae);return i(e),()=>r.deleteBlocker(e)}),[r]),e.useEffect((()=>{""!==o&&r.getBlocker(o,l)}),[r,o,l]),o&&a.blockers.has(o)?a.blockers.get(o):p}const ie={};function le(e,t){e?.v7_startTransition,void 0===e?.v7_relativeSplatPath&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}const ue=e.startTransition;function se({fallbackElement:t,router:r,future:n}){let[a,o]=e.useState(r.state),{v7_startTransition:i}=n||{},l=e.useCallback((e=>{i&&ue?ue((()=>o(e))):o(e)}),[o,i]);e.useLayoutEffect((()=>r.subscribe(l)),[r,l]),e.useEffect((()=>{}),[]);let u=e.useMemo((()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:e=>r.navigate(e),push:(e,t,n)=>r.navigate(e,{state:t,preventScrollReset:n?.preventScrollReset}),replace:(e,t,n)=>r.navigate(e,{replace:!0,state:t,preventScrollReset:n?.preventScrollReset})})),[r]),s=r.basename||"/",c=e.useMemo((()=>({router:r,navigator:u,static:!1,basename:s})),[r,u,s]);return e.useEffect((()=>le(n,r.future)),[r,n]),e.createElement(e.Fragment,null,e.createElement(E.Provider,{value:c},e.createElement(g.Provider,{value:a},e.createElement(fe,{basename:s,location:a.location,navigationType:a.historyAction,navigator:u,future:{v7_relativeSplatPath:r.future.v7_relativeSplatPath}},a.initialized||r.future.v7_partialHydration?e.createElement(ce,{routes:r.routes,future:r.future,state:a}):t))),null)}function ce({routes:e,future:t,state:r}){return I(e,void 0,r,t)}function de({basename:t,children:r,initialEntries:n,initialIndex:a,future:o}){let i=e.useRef();null==i.current&&(i.current=h({initialEntries:n,initialIndex:a,v5Compat:!0}));let l=i.current,[u,s]=e.useState({action:l.action,location:l.location}),{v7_startTransition:c}=o||{},d=e.useCallback((e=>{c&&ue?ue((()=>s(e))):s(e)}),[s,c]);return e.useLayoutEffect((()=>l.listen(d)),[l,d]),e.useEffect((()=>le(o)),[o]),e.createElement(fe,{basename:t,children:r,location:u.location,navigationType:u.action,navigator:l,future:o})}function pe({to:r,replace:n,state:a,relative:l}){_()||t(!1);let{future:u,static:s}=e.useContext(x),{matches:c}=e.useContext(b),{pathname:d}=P(),p=N(),m=i(r,o(c,u.v7_relativeSplatPath),d,"path"===l),h=JSON.stringify(m);return e.useEffect((()=>p(JSON.parse(h),{replace:n,state:a,relative:l})),[p,h,l,n,a]),null}function me(e){return L(e.context)}function he(e){t(!1)}function fe({basename:r="/",children:n=null,location:a,navigationType:o=s.Pop,navigator:i,static:u=!1,future:c}){_()&&t(!1);let p=r.replace(/^\/*/,"/"),m=e.useMemo((()=>({basename:p,navigator:i,static:u,future:{v7_relativeSplatPath:!1,...c}})),[p,c,i,u]);"string"==typeof a&&(a=l(a));let{pathname:h="/",search:f="",hash:v="",state:E=null,key:g="default"}=a,y=e.useMemo((()=>{let e=d(h,p);return null==e?null:{location:{pathname:e,search:f,hash:v,state:E,key:g},navigationType:o}}),[p,h,f,v,E,g,o]);return null==y?null:e.createElement(x.Provider,{value:m},e.createElement(C.Provider,{children:n,value:y}))}function ve({children:e,location:t}){return j(be(e),t)}function Ee({children:t,errorElement:r,resolve:n}){return e.createElement(xe,{resolve:n,errorElement:r},e.createElement(Ce,null,t))}var ge=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(ge||{});const ye=new Promise((()=>{}));class xe extends e.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:t,errorElement:r,resolve:n}=this.props,a=null,o=ge.pending;if(n instanceof Promise)if(this.state.error){o=ge.error;let e=this.state.error;a=Promise.reject().catch((()=>{})),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_error",{get:()=>e})}else n._tracked?(a=n,o="_error"in a?ge.error:"_data"in a?ge.success:ge.pending):(o=ge.pending,Object.defineProperty(n,"_tracked",{get:()=>!0}),a=n.then((e=>Object.defineProperty(n,"_data",{get:()=>e})),(e=>Object.defineProperty(n,"_error",{get:()=>e}))));else o=ge.success,a=Promise.resolve(),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_data",{get:()=>n});if(o===ge.error&&a._error instanceof f)throw ye;if(o===ge.error&&!r)throw a._error;if(o===ge.error)return e.createElement(y.Provider,{value:a,children:r});if(o===ge.success)return e.createElement(y.Provider,{value:a,children:t});throw a}}function Ce({children:t}){let r=re(),n="function"==typeof t?t(r):t;return e.createElement(e.Fragment,null,n)}function be(r,n=[]){let a=[];return e.Children.forEach(r,((r,o)=>{if(!e.isValidElement(r))return;let i=[...n,o];if(r.type===e.Fragment)return void a.push.apply(a,be(r.props.children,i));r.type!==he&&t(!1),r.props.index&&r.props.children&&t(!1);let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:null!=r.props.ErrorBoundary||null!=r.props.errorElement,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=be(r.props.children,i)),a.push(l)})),a}function Re(e){return z(e)}function Se(t){let r={hasErrorBoundary:null!=t.ErrorBoundary||null!=t.errorElement};return t.Component&&Object.assign(r,{element:e.createElement(t.Component),Component:void 0}),t.HydrateFallback&&Object.assign(r,{hydrateFallbackElement:e.createElement(t.HydrateFallback),HydrateFallback:void 0}),t.ErrorBoundary&&Object.assign(r,{errorElement:e.createElement(t.ErrorBoundary),ErrorBoundary:void 0}),r}function _e(e,t){return v({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:h({initialEntries:t?.initialEntries,initialIndex:t?.initialIndex}),hydrationData:t?.hydrationData,routes:e,mapRouteProperties:Se,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation}).initialize()}export{Ee as Await,de as MemoryRouter,pe as Navigate,me as Outlet,he as Route,fe as Router,se as RouterProvider,ve as Routes,E as UNSAFE_DataRouterContext,g as UNSAFE_DataRouterStateContext,C as UNSAFE_LocationContext,x as UNSAFE_NavigationContext,b as UNSAFE_RouteContext,le as UNSAFE_logV6DeprecationWarnings,Se as UNSAFE_mapRouteProperties,q as UNSAFE_useRouteId,I as UNSAFE_useRoutesImpl,_e as createMemoryRouter,be as createRoutesFromChildren,be as createRoutesFromElements,Re as renderMatches,ee as useActionData,ne as useAsyncError,re as useAsyncValue,oe as useBlocker,S as useHref,_ as useInRouterContext,X as useLoaderData,P as useLocation,k as useMatch,Q as useMatches,N as useNavigate,G as useNavigation,U as useNavigationType,L as useOutlet,F as useOutletContext,O as useParams,A as useResolvedPath,K as useRevalidator,te as useRouteError,Z as useRouteLoaderData,j as useRoutes};
//# sourceMappingURL=react-router.production.min.js.map
