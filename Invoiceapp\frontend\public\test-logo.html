<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RedBeryl Logo Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo-test {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background: #fafafa;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            text-decoration: underline;
            letter-spacing: 2px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RedBeryl Logo Test for Invoice Generation</h1>
        
        <div class="logo-test">
            <h3>Logo Test 1: Base64 SVG (Used in PDF)</h3>
            <img
                src="data:image/svg+xml;base64,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"
                alt="RedBeryl Tech Solutions"
                style="width: 280px; height: 90px; object-fit: contain; border: 1px solid #ddd;"
            />
            <p><strong>Status:</strong> <span id="logo1-status">Loading...</span></p>
        </div>

        <div class="logo-test">
            <h3>Logo Test 2: External Image (if available)</h3>
            <img 
                id="external-logo"
                src="/assets/redberyl-logo.png"
                alt="RedBeryl Tech Solutions"
                style="width: 250px; height: 80px; object-fit: contain; border: 1px solid #ddd;"
                onerror="this.style.display='none'; document.getElementById('logo2-status').innerHTML='❌ External image not found';"
                onload="document.getElementById('logo2-status').innerHTML='✅ External image loaded successfully';"
            />
            <p><strong>Status:</strong> <span id="logo2-status">Loading...</span></p>
        </div>

        <div class="logo-test">
            <h3>Invoice Header Preview</h3>
            <div class="invoice-header">
                <div style="flex: 1;">
                    <img
                        src="data:image/svg+xml;base64,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"
                        alt="RedBeryl Tech Solutions"
                        style="width: 280px; height: 90px; object-fit: contain;"
                    />
                </div>
                <div style="text-align: center; flex: 1;">
                    <h1 class="invoice-title">INVOICE</h1>
                </div>
                <div style="flex: 1;"></div>
            </div>
            <p>This is how the logo appears in the actual invoice header.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="window.open('/invoice-pdf-demo', '_blank')">
                🧪 Test PDF Generation
            </button>
            <button class="test-button" onclick="window.open('/invoices', '_blank')">
                📄 Go to Invoices
            </button>
        </div>

        <div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4>🔧 Troubleshooting:</h4>
            <ul>
                <li><strong>Logo not showing in PDF?</strong> The base64 SVG should always work</li>
                <li><strong>Want to use your own logo?</strong> Place it at <code>/public/assets/redberyl-logo.png</code></li>
                <li><strong>PDF generation failing?</strong> Check browser console for errors</li>
                <li><strong>Logo quality issues?</strong> Use high-resolution images (600x240px recommended)</li>
            </ul>
        </div>
    </div>

    <script>
        // Check if base64 logo loaded
        setTimeout(() => {
            const logo1Status = document.getElementById('logo1-status');
            if (logo1Status.textContent === 'Loading...') {
                logo1Status.innerHTML = '✅ Base64 SVG loaded successfully';
            }
        }, 1000);

        // Set default status for external logo if not changed
        setTimeout(() => {
            const logo2Status = document.getElementById('logo2-status');
            if (logo2Status.textContent === 'Loading...') {
                logo2Status.innerHTML = '⚠️ External image not found (this is normal if you haven\'t added your logo yet)';
            }
        }, 2000);
    </script>
</body>
</html>
