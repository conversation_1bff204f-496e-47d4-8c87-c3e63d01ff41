<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RedBeryl Logo Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo-test {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background: #fafafa;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            text-decoration: underline;
            letter-spacing: 2px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RedBeryl Logo Test for Invoice Generation</h1>
        
        <div class="logo-test">
            <h3>Logo Test 1: Base64 SVG (Used in PDF)</h3>
            <img 
                src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDYwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjYwMCIgaGVpZ2h0PSIyNDAiIGZpbGw9IndoaXRlIi8+CiAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMzAsIDYwKSI+CiAgICA8cGF0aCBkPSJNMCA2MCBDMCA0MSwgMTMgMjcsIDMwIDI3IEM0NyAyNywgNjAgNDEsIDYwIDYwIEM2MCA3OSwgNDcgOTMsIDMwIDkzIEMxMyA5MywgMCA3OSwgMCA2MCBaIiBmaWxsPSIjNEE5MEUyIiBvcGFjaXR5PSIwLjkiLz4KICAgIDxwYXRoIGQ9Ik00MCA0MCBDNDAGMTI4LCA1MiAyOCwgNjggMjggQzg0IDI4LCA5NiA0MCwgOTYgNTYgQzk2IDcyLCA4NCA4NCwgNjggODQgQzUyIDg0LCA0MCA3MiwgNDAgNTYgWiIgZmlsbD0iI0U5MUU2MyIgb3BhY2l0eT0iMC45Ii8+CiAgICA8cGF0aCBkPSJNMjAgODAgQzIwIDY4LCAzMCA1OCwgNDIgNTggQzU0IDU4LCA2NCA2OCwgNjQgODAgQzY0IDkyLCA1NCAxMDIsIDQyIDEwMiBDMzAgMTAyLCAyMCA5MiwgMjAgODAgWiIgZmlsbD0iIzhFNDRBRCIgb3BhY2l0eT0iMC44Ii8+CiAgICA8Y2lyY2xlIGN4PSI1MCIgY3k9IjYwIiByPSI0IiBmaWxsPSIjMzQ5OERCIi8+CiAgICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjcwIiByPSIzIiBmaWxsPSIjRTc0QzNDIi8+CiAgICA8Y2lyY2xlIGN4PSI2MCIgY3k9IjUwIiByPSIyIiBmaWxsPSIjRjM5QzEyIi8+CiAgPC9nPgogIDxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDE0MCwgNjApIj4KICAgIDx0ZXh0IHg9IjAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iNDgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjRTkxRTYzIj5SZWQ8L3RleHQ+CiAgICA8dGV4dCB4PSIxMDUiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iNDgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjMTU2NUMwIj5CZXJ5bDwvdGV4dD4KICAgIDx0ZXh0IHg9IjAiIHk9Ijc1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIGZvbnQtd2VpZ2h0PSI2MDAiIGZpbGw9IiM2NjYiIGxldHRlci1zcGFjaW5nPSIzcHgiPlRFQ0ggU09MVVRJT05TPC90ZXh0PgogICAgPHRleHQgeD0iMCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM4ODgiIGZvbnQtc3R5bGU9Iml0YWxpYyI+SW50ZWdyYXRlcyBCdXNpbmVzcyBXaXRoIFRlY2hub2xvZ3k8L3RleHQ+CiAgPC9nPgo8L3N2Zz4K"
                alt="RedBeryl Tech Solutions"
                style="width: 250px; height: 80px; object-fit: contain; border: 1px solid #ddd;"
            />
            <p><strong>Status:</strong> <span id="logo1-status">Loading...</span></p>
        </div>

        <div class="logo-test">
            <h3>Logo Test 2: External Image (if available)</h3>
            <img 
                id="external-logo"
                src="/assets/redberyl-logo.png"
                alt="RedBeryl Tech Solutions"
                style="width: 250px; height: 80px; object-fit: contain; border: 1px solid #ddd;"
                onerror="this.style.display='none'; document.getElementById('logo2-status').innerHTML='❌ External image not found';"
                onload="document.getElementById('logo2-status').innerHTML='✅ External image loaded successfully';"
            />
            <p><strong>Status:</strong> <span id="logo2-status">Loading...</span></p>
        </div>

        <div class="logo-test">
            <h3>Invoice Header Preview</h3>
            <div class="invoice-header">
                <div style="flex: 1;">
                    <img 
                        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDYwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjYwMCIgaGVpZ2h0PSIyNDAiIGZpbGw9IndoaXRlIi8+CiAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMzAsIDYwKSI+CiAgICA8cGF0aCBkPSJNMCA2MCBDMCA0MSwgMTMgMjcsIDMwIDI3IEM0NyAyNywgNjAgNDEsIDYwIDYwIEM2MCA3OSwgNDcgOTMsIDMwIDkzIEMxMyA5MywgMCA3OSwgMCA2MCBaIiBmaWxsPSIjNEE5MEUyIiBvcGFjaXR5PSIwLjkiLz4KICAgIDxwYXRoIGQ9Ik00MCA0MCBDNDAGMTI4LCA1MiAyOCwgNjggMjggQzg0IDI4LCA5NiA0MCwgOTYgNTYgQzk2IDcyLCA4NCA4NCwgNjggODQgQzUyIDg0LCA0MCA3MiwgNDAgNTYgWiIgZmlsbD0iI0U5MUU2MyIgb3BhY2l0eT0iMC45Ii8+CiAgICA8cGF0aCBkPSJNMjAgODAgQzIwIDY4LCAzMCA1OCwgNDIgNTggQzU0IDU4LCA2NCA2OCwgNjQgODAgQzY0IDkyLCA1NCAxMDIsIDQyIDEwMiBDMzAgMTAyLCAyMCA5MiwgMjAgODAgWiIgZmlsbD0iIzhFNDRBRCIgb3BhY2l0eT0iMC44Ii8+CiAgICA8Y2lyY2xlIGN4PSI1MCIgY3k9IjYwIiByPSI0IiBmaWxsPSIjMzQ5OERCIi8+CiAgICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjcwIiByPSIzIiBmaWxsPSIjRTc0QzNDIi8+CiAgICA8Y2lyY2xlIGN4PSI2MCIgY3k9IjUwIiByPSIyIiBmaWxsPSIjRjM5QzEyIi8+CiAgPC9nPgogIDxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDE0MCwgNjApIj4KICAgIDx0ZXh0IHg9IjAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iNDgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjRTkxRTYzIj5SZWQ8L3RleHQ+CiAgICA8dGV4dCB4PSIxMDUiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iNDgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjMTU2NUMwIj5CZXJ5bDwvdGV4dD4KICAgIDx0ZXh0IHg9IjAiIHk9Ijc1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIGZvbnQtd2VpZ2h0PSI2MDAiIGZpbGw9IiM2NjYiIGxldHRlci1zcGFjaW5nPSIzcHgiPlRFQ0ggU09MVVRJT05TPC90ZXh0PgogICAgPHRleHQgeD0iMCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM4ODgiIGZvbnQtc3R5bGU9Iml0YWxpYyI+SW50ZWdyYXRlcyBCdXNpbmVzcyBXaXRoIFRlY2hub2xvZ3k8L3RleHQ+CiAgPC9nPgo8L3N2Zz4K"
                        alt="RedBeryl Tech Solutions"
                        style="width: 250px; height: 80px; object-fit: contain;"
                    />
                </div>
                <div style="text-align: center; flex: 1;">
                    <h1 class="invoice-title">INVOICE</h1>
                </div>
                <div style="flex: 1;"></div>
            </div>
            <p>This is how the logo appears in the actual invoice header.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="window.open('/invoice-pdf-demo', '_blank')">
                🧪 Test PDF Generation
            </button>
            <button class="test-button" onclick="window.open('/invoices', '_blank')">
                📄 Go to Invoices
            </button>
        </div>

        <div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4>🔧 Troubleshooting:</h4>
            <ul>
                <li><strong>Logo not showing in PDF?</strong> The base64 SVG should always work</li>
                <li><strong>Want to use your own logo?</strong> Place it at <code>/public/assets/redberyl-logo.png</code></li>
                <li><strong>PDF generation failing?</strong> Check browser console for errors</li>
                <li><strong>Logo quality issues?</strong> Use high-resolution images (600x240px recommended)</li>
            </ul>
        </div>
    </div>

    <script>
        // Check if base64 logo loaded
        setTimeout(() => {
            const logo1Status = document.getElementById('logo1-status');
            if (logo1Status.textContent === 'Loading...') {
                logo1Status.innerHTML = '✅ Base64 SVG loaded successfully';
            }
        }, 1000);

        // Set default status for external logo if not changed
        setTimeout(() => {
            const logo2Status = document.getElementById('logo2-status');
            if (logo2Status.textContent === 'Loading...') {
                logo2Status.innerHTML = '⚠️ External image not found (this is normal if you haven\'t added your logo yet)';
            }
        }, 2000);
    </script>
</body>
</html>
