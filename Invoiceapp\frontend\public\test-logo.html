<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RedBeryl Logo Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo-test {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background: #fafafa;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            text-decoration: underline;
            letter-spacing: 2px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RedBeryl Logo Test for Invoice Generation</h1>
        
        <div class="logo-test">
            <h3>Logo Test 1: Exact RedBeryl Logo (Used in PDF)</h3>

            <img 
                id="external-logo"
                src="/assets/logo.png"
                alt="RedBeryl Tech Solutions"
                style="width: 250px; height: 80px; object-fit: contain; border: 1px solid #ddd;"
                onerror="this.style.display='none'; document.getElementById('logo2-status').innerHTML='❌ External image not found';"
                onload="document.getElementById('logo2-status').innerHTML='✅ External image loaded successfully';"
            />
        
            <p><strong>Status:</strong> <span id="logo1-status">✅ Exact RedBeryl logo loaded successfully</span></p>
        </div>

        <div class="logo-test">
            <h3>Logo Test 2: External Image (if available)</h3>
            <img 
                id="external-logo"
                src="/assets/redberyl-logo.png"
                alt="RedBeryl Tech Solutions"
                style="width: 250px; height: 80px; object-fit: contain; border: 1px solid #ddd;"
                onerror="this.style.display='none'; document.getElementById('logo2-status').innerHTML='❌ External image not found';"
                onload="document.getElementById('logo2-status').innerHTML='✅ External image loaded successfully';"
            />
            <p><strong>Status:</strong> <span id="logo2-status">Loading...</span></p>
        </div>

        <div class="logo-test">
            <h3>Invoice Header Preview</h3>
            <div class="invoice-header">
                <div style="flex: 1;">
                 <img 
                id="external-logo"
                src="/assets/logo.png"
                alt="RedBeryl Tech Solutions"
                style="width: 250px; height: 80px; object-fit: contain; border: 1px solid #ddd;"
                onerror="this.style.display='none'; document.getElementById('logo2-status').innerHTML='❌ External image not found';"
                onload="document.getElementById('logo2-status').innerHTML='✅ External image loaded successfully';"
            />
        
                </div>
                <div style="text-align: center; flex: 1;">
                    <!-- <h1 class="invoice-title">INVOICE</h1> -->
                </div>
                <div style="flex: 1;"></div>
            </div>
            <p>This is how the logo appears in the actual invoice header.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="window.open('/invoice-pdf-demo', '_blank')">
                🧪 Test PDF Generation
            </button>
            <button class="test-button" onclick="window.open('/invoices', '_blank')">
                📄 Go to Invoices
            </button>
        </div>

        <div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4>🔧 Troubleshooting:</h4>
            <ul>
                <li><strong>Logo not showing in PDF?</strong> The base64 SVG should always work</li>
                <li><strong>Want to use your own logo?</strong> Place it at <code>/public/assets/redberyl-logo.png</code></li>
                <li><strong>PDF generation failing?</strong> Check browser console for errors</li>
                <li><strong>Logo quality issues?</strong> Use high-resolution images (600x240px recommended)</li>
            </ul>
        </div>
    </div>

    <script>
        // Check if base64 logo loaded
        setTimeout(() => {
            const logo1Status = document.getElementById('logo1-status');
            if (logo1Status.textContent === 'Loading...') {
                logo1Status.innerHTML = '✅ Base64 SVG loaded successfully';
            }
        }, 1000);

        // Set default status for external logo if not changed
        setTimeout(() => {
            const logo2Status = document.getElementById('logo2-status');
            if (logo2Status.textContent === 'Loading...') {
                logo2Status.innerHTML = '⚠️ External image not found (this is normal if you haven\'t added your logo yet)';
            }
        }, 2000);
    </script>
</body>
</html>
