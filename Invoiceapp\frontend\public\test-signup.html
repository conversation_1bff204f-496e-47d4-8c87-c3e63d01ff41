<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4a65ff;
            color: white;
            border: none;
            cursor: pointer;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>Test Signup Form</h1>
    <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" value="testuser">
    </div>
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="password123">
    </div>
    <div class="form-group">
        <label for="backend-url">Backend URL:</label>
        <input type="text" id="backend-url" value="http://localhost:8080/api/auth/signup">
    </div>
    <button onclick="testSignup()">Test Signup</button>

    <div id="result">
        <p>Results will appear here...</p>
    </div>

    <script>
        async function testSignup() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const backendUrl = document.getElementById('backend-url').value;

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Sending request...</p>';

            try {
                const response = await fetch(backendUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        roles: ['user']
                    }),
                    credentials: 'include'
                });

                const statusText = `Status: ${response.status} ${response.statusText}`;

                // Log response headers
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });

                let responseData;
                try {
                    responseData = await response.json();
                } catch (e) {
                    responseData = { error: 'Could not parse JSON response' };
                }

                resultDiv.innerHTML = `
                    <p><strong>${statusText}</strong></p>
                    <p><strong>Headers:</strong></p>
                    <pre>${JSON.stringify(headers, null, 2)}</pre>
                    <p><strong>Response:</strong></p>
                    <pre>${JSON.stringify(responseData, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <p><strong>Error:</strong></p>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
