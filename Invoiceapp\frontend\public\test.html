<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .success {
            color: #28a745;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .info {
            color: #6c757d;
            margin-bottom: 10px;
        }
        .link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .link:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="success">✅ Frontend Server is Working!</h1>
        
        <div class="info">
            <strong>Server Status:</strong> Running on http://localhost:3000
        </div>
        
        <div class="info">
            <strong>Test Time:</strong> <span id="currentTime"></span>
        </div>
        
        <h3>Available Pages:</h3>
        <a href="/" class="link">Main Dashboard</a>
        <a href="/invoices" class="link">Invoice Form</a>
        <a href="/auto-generation-demo" class="link">Auto-Generation Demo</a>
        <a href="/auth/login" class="link">Login Page</a>
        
        <h3>Troubleshooting:</h3>
        <p>If the React app pages don't load:</p>
        <ul>
            <li>Try refreshing the page (F5)</li>
            <li>Clear browser cache (Ctrl+Shift+R)</li>
            <li>Check browser console for errors (F12)</li>
            <li>Make sure backend is running on port 8080</li>
        </ul>
        
        <h3>Auto-Generation Features:</h3>
        <ul>
            <li>✅ Candidate-based auto-population</li>
            <li>✅ Attendance-based billing calculation</li>
            <li>✅ Real-time GST calculations</li>
            <li>✅ Unique invoice number generation</li>
            <li>✅ Auto-generated descriptions</li>
        </ul>
    </div>
    
    <script>
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
