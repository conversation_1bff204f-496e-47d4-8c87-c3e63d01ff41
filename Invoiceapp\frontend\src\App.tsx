
import React from 'react';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import InvoiceForm<PERSON>rapper from "./components/invoices/InvoiceFormWrapper";

const App = () => {
  return (
    <BrowserRouter>
      <div style={{ padding: '20px' }}>
        <Routes>
          <Route path="/" element={<InvoiceFormWrapper />} />
          <Route path="/invoices" element={<InvoiceFormWrapper />} />
          <Route path="/auto-generation-demo" element={<InvoiceFormWrapper />} />
        </Routes>
      </div>
    </BrowserRouter>
  );
};

export default App;
