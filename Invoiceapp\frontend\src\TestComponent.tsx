import React from 'react';

const TestComponent = () => {
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f0f8ff', 
      border: '2px solid #007bff',
      borderRadius: '8px',
      margin: '20px',
      textAlign: 'center'
    }}>
      <h1 style={{ color: '#007bff', marginBottom: '20px' }}>
        🎉 React Frontend is Working!
      </h1>
      
      <p style={{ fontSize: '18px', marginBottom: '15px' }}>
        ✅ Vite server is running successfully
      </p>
      
      <p style={{ fontSize: '18px', marginBottom: '15px' }}>
        ✅ React components are rendering
      </p>
      
      <p style={{ fontSize: '18px', marginBottom: '15px' }}>
        ✅ TypeScript compilation is working
      </p>
      
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={() => alert('Button clicked! React events are working!')}
          style={{
            backgroundColor: '#28a745',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '16px',
            marginRight: '10px'
          }}
        >
          Test Button
        </button>
        
        <a 
          href="/invoices" 
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            padding: '10px 20px',
            textDecoration: 'none',
            borderRadius: '4px',
            fontSize: '16px'
          }}
        >
          Go to Invoices
        </a>
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <p>Current time: {new Date().toLocaleString()}</p>
      </div>
    </div>
  );
};

export default TestComponent;
