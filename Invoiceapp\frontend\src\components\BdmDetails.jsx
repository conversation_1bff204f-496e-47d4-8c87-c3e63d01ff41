import React from 'react';
import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Divider,
  SimpleGrid,
  Tooltip,
  Icon
} from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';

const BdmDetails = ({ bdm, showHeading = true, compact = false }) => {
  if (!bdm) {
    return (
      <Box p={3} bg="gray.100" borderRadius="md">
        <Text color="gray.500" fontStyle="italic">No BDM selected</Text>
      </Box>
    );
  }

  // For compact view, show only essential information
  if (compact) {
    return (
      <Box p={3} bg="blue.50" borderRadius="md">
        {showHeading && <Heading size="sm" mb={2}>BDM Information</Heading>}
        <HStack>
          <Text fontWeight="bold">Name:</Text>
          <Text>{bdm.name}</Text>
        </HStack>
        <HStack>
          <Text fontWeight="bold">Email:</Text>
          <Text>{bdm.email || 'N/A'}</Text>
        </HStack>
        <HStack>
          <Text fontWeight="bold">Phone:</Text>
          <Text>{bdm.phone || 'N/A'}</Text>
        </HStack>
      </Box>
    );
  }

  return (
    <Box p={4} bg="blue.50" borderRadius="md">
      {showHeading && (
        <>
          <Heading size="sm" mb={2}>BDM Information</Heading>
          <Divider mb={3} />
        </>
      )}

      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
        <VStack align="stretch" spacing={2}>
          <HStack>
            <Text fontWeight="bold" width="150px">ID:</Text>
            <Text>{bdm.id}</Text>
          </HStack>
          
          <HStack>
            <Text fontWeight="bold" width="150px">Name:</Text>
            <Text>{bdm.name}</Text>
          </HStack>
          
          <HStack>
            <Text fontWeight="bold" width="150px">Email:</Text>
            <Text>{bdm.email || 'N/A'}</Text>
          </HStack>
          
          <HStack>
            <Text fontWeight="bold" width="150px">Phone:</Text>
            <Text>{bdm.phone || 'N/A'}</Text>
          </HStack>
          
          <HStack>
            <Text fontWeight="bold" width="150px">GST Number:</Text>
            <Text>{bdm.gstNumber || 'N/A'}</Text>
          </HStack>
        </VStack>
        
        <VStack align="stretch" spacing={2}>
          <HStack>
            <Text fontWeight="bold" width="150px">Commission Rate:</Text>
            <Text>{bdm.commissionRate ? `${bdm.commissionRate}%` : '0%'}</Text>
          </HStack>
          
          <HStack alignItems="flex-start">
            <Text fontWeight="bold" width="150px">Billing Address:</Text>
            <Text>{bdm.billingAddress || 'N/A'}</Text>
          </HStack>
          
          <HStack>
            <Text fontWeight="bold" width="150px">Client Count:</Text>
            <Badge colorScheme="green">{bdm.clientCount || 0}</Badge>
          </HStack>
          
          <HStack>
            <Text fontWeight="bold" width="150px">Project Count:</Text>
            <Badge colorScheme="blue">{bdm.projectCount || 0}</Badge>
          </HStack>
          
          <HStack alignItems="flex-start">
            <Text fontWeight="bold" width="150px">Notes:</Text>
            <Text>{bdm.notes || 'N/A'}</Text>
          </HStack>
        </VStack>
      </SimpleGrid>
      
      <Divider my={3} />
      
      <HStack>
        <Tooltip label="This information is fetched from the BDM database">
          <Icon as={InfoIcon} color="blue.500" />
        </Tooltip>
        <Text fontSize="sm" color="gray.600">
          BDM data last updated: {bdm.updatedAt ? new Date(bdm.updatedAt).toLocaleString() : 'Unknown'}
        </Text>
      </HStack>
    </Box>
  );
};

export default BdmDetails;
