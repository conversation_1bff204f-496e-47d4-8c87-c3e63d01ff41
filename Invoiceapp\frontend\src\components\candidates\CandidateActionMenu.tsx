import React, { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Edit, FileText, Trash, MoreHorizontal } from "lucide-react";
import DeleteConfirmationDialog from "./DeleteConfirmationDialog";

interface CandidateActionMenuProps {
  candidateId: string;
  onEdit: (id: string) => void;
  onViewDocuments: (id: string) => void;
  onDelete: (id: string) => void;
}

const CandidateActionMenu: React.FC<CandidateActionMenuProps> = ({
  candidateId,
  onEdit,
  onViewDocuments,
  onDelete,
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDelete = () => {
    onDelete(candidateId);
    setIsDeleteDialogOpen(false);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8 ml-auto">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem onClick={() => onEdit(candidateId)} className="cursor-pointer">
            <Edit className="mr-2 h-4 w-4 text-amber-600" />
            <span>Edit</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onViewDocuments(candidateId)} className="cursor-pointer">
            <FileText className="mr-2 h-4 w-4 text-blue-600" />
            <span>View Documents</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="cursor-pointer text-red-600"
          >
            <Trash className="mr-2 h-4 w-4" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDelete}
        title="Delete Candidate"
        description="Are you sure you want to delete this candidate? This action cannot be undone and will remove all associated data."
      />
    </>
  );
};

export default CandidateActionMenu;
