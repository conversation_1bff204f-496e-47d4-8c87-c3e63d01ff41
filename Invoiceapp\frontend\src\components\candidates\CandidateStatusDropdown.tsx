import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

interface CandidateStatusDropdownProps {
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
}

const CandidateStatusDropdown: React.FC<CandidateStatusDropdownProps> = ({
  currentStatus,
  onStatusChange,
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "inactive":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Select defaultValue={currentStatus} onValueChange={onStatusChange}>
      <SelectTrigger className="w-[130px] h-8 border-0 p-0 bg-transparent">
        <SelectValue>
          <Badge className={`${getStatusColor(currentStatus)}`} variant="outline">
            {currentStatus}
          </Badge>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="Active">
          <Badge className="bg-green-100 text-green-800 border-green-200" variant="outline">
            Active
          </Badge>
        </SelectItem>
        <SelectItem value="Inactive">
          <Badge className="bg-red-100 text-red-800 border-red-200" variant="outline">
            Inactive
          </Badge>
        </SelectItem>
        <SelectItem value="Pending">
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200" variant="outline">
            Pending
          </Badge>
        </SelectItem>
      </SelectContent>
    </Select>
  );
};

export default CandidateStatusDropdown;
