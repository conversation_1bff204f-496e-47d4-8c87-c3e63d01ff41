
import { useState, useEffect } from "react";
import { DragDropContext, Droppable, Draggable, DropResult } from "react-beautiful-dnd";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, MoreHorizontal, Trash, Edit } from "lucide-react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

// Define the initial state for our kanban board with empty columns
const initialDeals = {
  lead: [],
  qualified: [],
  proposal: [],
  negotiation: [],
  closed: []
};

export interface Deal {
  id: string;
  title: string;
  client: string;
  value: string;
  dueDate: string;
  assignedTo: string;
  notes?: string;
  status?: string;
}

export interface ColumnData {
  [key: string]: Deal[];
}

export const columnTitles: Record<string, string> = {
  lead: "Leads",
  qualified: "Qualified",
  proposal: "Proposal",
  negotiation: "Negotiation",
  closed: "Closed",
};

const columnStyles: Record<string, string> = {
  lead: "bg-blue-100 border-blue-300",
  qualified: "bg-purple-100 border-purple-300",
  proposal: "bg-orange-100 border-orange-300",
  negotiation: "bg-yellow-100 border-yellow-300",
  closed: "bg-green-100 border-green-300",
};

interface KanbanBoardProps {
  onEditDeal?: (deal: Deal) => void;
  onDeleteDeal?: (dealId: string) => void;
  externalDeals?: ColumnData;
  onDealMoved?: (deal: Deal, source: string, destination: string) => void;
}

const KanbanBoard: React.FC<KanbanBoardProps> = ({
  onEditDeal,
  onDeleteDeal,
  externalDeals,
  onDealMoved
}) => {
  const [columns, setColumns] = useState<ColumnData>(initialDeals);

  // Update columns if external deals are provided
  useEffect(() => {
    if (externalDeals) {
      setColumns(externalDeals);
    }
  }, [externalDeals]);

  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    // Dropped outside the list
    if (!destination) return;

    // Dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // Find the source column
    const sourceColumn = columns[source.droppableId];
    // Get the dragged item
    const draggedItem = sourceColumn[source.index];
    // Create a copy of the columns state
    const newColumns = { ...columns };

    // Remove from source column
    newColumns[source.droppableId] = sourceColumn.filter((_, idx) => idx !== source.index);

    // Add to destination column
    const destColumn = [...newColumns[destination.droppableId]];
    destColumn.splice(destination.index, 0, draggedItem);
    newColumns[destination.droppableId] = destColumn;

    // Update the deal's status to match the new column
    const updatedDeal = { ...draggedItem, status: destination.droppableId };

    // Call the callback if provided
    if (onDealMoved) {
      onDealMoved(updatedDeal, source.droppableId, destination.droppableId);
    } else {
      // Show a toast notification
      toast.success(`Deal moved to ${columnTitles[destination.droppableId]}`, {
        description: draggedItem.title,
      });
    }

    setColumns(newColumns);
  };

  const handleEditDeal = (deal: Deal) => {
    try {
      console.log('Edit deal clicked:', deal);

      // Prevent editing if the deal is being dragged
      if (document.querySelector('.react-beautiful-dnd-dragging')) {
        console.log('Cannot edit while dragging');
        return;
      }

      if (onEditDeal) {
        // Call the parent component's edit handler
        onEditDeal(deal);

        // Show a brief toast notification for feedback
        toast.success(`Opening edit form for: ${deal.title}`, {
          duration: 2000
        });
      } else {
        // Fallback if no handler is provided
        toast.info(`Edit deal: ${deal.title}`, {
          description: "Edit functionality not implemented yet",
        });
      }
    } catch (error) {
      console.error('Error handling edit deal:', error);
      toast.error('Failed to open edit form', {
        description: 'Please try again or refresh the page',
        duration: 5000
      });
    }
  };

  const handleDeleteDeal = (dealId: string, columnId: string, dealTitle: string) => {
    if (onDeleteDeal) {
      // Call the parent component's delete handler
      onDeleteDeal(dealId);
    } else {
      // Handle delete locally if no parent handler is provided
      const newColumns = { ...columns };
      newColumns[columnId] = newColumns[columnId].filter((deal) => deal.id !== dealId);
      setColumns(newColumns);

      toast.success("Deal deleted", {
        description: `"${dealTitle}" has been removed`,
      });
    }
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex gap-4 overflow-x-auto pb-4">
        {Object.keys(columns).map((columnId) => (
          <div key={columnId} className="flex flex-col min-w-[300px]">
            <h3 className="mb-3 text-lg font-semibold">
              {columnTitles[columnId]}
              <Badge variant="outline" className="ml-2">
                {columns[columnId].length}
              </Badge>
            </h3>
            <Droppable droppableId={columnId}>
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className={`kanban-column rounded-md border p-2 min-h-[200px] transition-colors duration-200 ${columnStyles[columnId]} hover:shadow-md`}
                >
                  {columns[columnId].length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-[200px] text-center p-4 text-gray-500">
                      <p className="text-sm mb-2">No deals in this stage</p>
                      <p className="text-xs">Drag deals here or create a new deal</p>
                    </div>
                  ) : (
                    columns[columnId].map((deal, index) => (
                      <Draggable key={deal.id} draggableId={deal.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="kanban-card mb-3 last:mb-0 cursor-grab active:cursor-grabbing transition-transform duration-200 hover:-translate-y-1"
                            style={{
                              ...provided.draggableProps.style,
                              transform: provided.draggableProps.style?.transform,
                            }}
                          >
                            <Card className={`shadow-sm hover:shadow-md transition-shadow ${provided.isDragging ? 'border-2 border-primary shadow-lg' : ''}`}>
                              <CardHeader className="p-3 pb-2 flex flex-row justify-between items-start">
                                <div>
                                  <CardTitle className="text-base">{deal.title}</CardTitle>
                                  <CardDescription>{deal.client}</CardDescription>
                                </div>
                                <div className="flex items-center">
                                  {/* Actions Dropdown Menu */}
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="icon" className="h-8 w-8">
                                        <MoreHorizontal className="h-4 w-4" />
                                        <span className="sr-only">Actions</span>
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem
                                        onClick={() => handleEditDeal(deal)}
                                        className="cursor-pointer"
                                      >
                                        <Edit className="mr-2 h-4 w-4 text-amber-600" />
                                        <span>Edit</span>
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        onClick={() => handleDeleteDeal(deal.id, columnId, deal.title)}
                                        className="text-red-600 cursor-pointer"
                                      >
                                        <Trash className="mr-2 h-4 w-4" />
                                        <span>Delete</span>
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </CardHeader>
                              <CardContent className="p-3 pt-0">
                                <p className="text-lg font-semibold">{deal.value}</p>
                                {deal.notes && (
                                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                    {deal.notes}
                                  </p>
                                )}
                              </CardContent>
                              <CardFooter className="p-3 pt-0 flex justify-between text-xs text-muted-foreground">
                                <div className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {new Date(deal.dueDate).toLocaleDateString()}
                                </div>
                                <div className="flex items-center">
                                  <User className="h-3 w-3 mr-1" />
                                  {deal.assignedTo}
                                </div>
                              </CardFooter>
                            </Card>
                          </div>
                        )}
                      </Draggable>
                    ))
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>
        ))}
      </div>
    </DragDropContext>
  );
};

export default KanbanBoard;
