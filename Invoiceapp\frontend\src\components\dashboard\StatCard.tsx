
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
  linkTo?: string;
  onClick?: () => void;
}

const StatCard = ({
  title,
  value,
  description,
  icon,
  trend,
  className,
  linkTo,
  onClick
}: StatCardProps) => {
  const cardContent = (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-4 w-4 text-muted-foreground">{icon}</div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && (
          <p className="text-xs text-muted-foreground">
            <span className={cn(
              "mr-1",
              trend.isPositive ? "text-green-500" : "text-red-500"
            )}>
              {trend.isPositive ? "+" : "-"}{trend.value}%
            </span>
            from last month
          </p>
        )}
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
        {(linkTo || onClick) && (
          <div className="flex justify-end mt-2">
            <span className="text-xs text-primary/70 group-hover:text-primary transition-colors cursor-pointer">
              View details
            </span>
          </div>
        )}
      </CardContent>
    </>
  );

  // If linkTo is provided, wrap the card in a Link component
  if (linkTo) {
    return (
      <Link to={linkTo} className="block">
        <Card className={cn(
          "overflow-hidden cursor-pointer transition-all hover:shadow-md hover:border-primary/50",
          "relative group",
          className
        )}>
          {cardContent}
          <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"></div>
        </Card>
      </Link>
    );
  }

  // If onClick is provided, make the card clickable
  if (onClick) {
    return (
      <Card
        className={cn(
          "overflow-hidden cursor-pointer transition-all hover:shadow-md hover:border-primary/50",
          "relative group",
          className
        )}
        onClick={onClick}
      >
        {cardContent}
        <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"></div>
      </Card>
    );
  }

  // Default case: non-clickable card
  return (
    <Card className={cn("overflow-hidden", className)}>
      {cardContent}
    </Card>
  );
};

export default StatCard;
