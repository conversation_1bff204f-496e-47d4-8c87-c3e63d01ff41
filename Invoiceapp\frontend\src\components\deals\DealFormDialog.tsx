import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { toast } from "sonner";
import { clientService, Client } from "@/services/clientService";

export interface DealFormData {
  title: string;
  client_id: number;
  client_name?: string; // For display purposes
  value: string;
  dueDate: string;
  assignedTo?: string; // Made optional
  status?: string;
  notes?: string;
}

interface DealFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: DealFormData) => void;
  initialData?: DealFormData;
  title?: string;
}

const DealFormDialog: React.FC<DealFormDialogProps> = ({
  open,
  onOpenChange,
  onSave,
  initialData,
  title = "Add New Deal",
}) => {
  const [formData, setFormData] = useState<DealFormData>({
    title: "",
    client_id: 0,
    client_name: "",
    value: "",
    dueDate: format(new Date(), "yyyy-MM-dd"),
    status: "lead",
    notes: "",
  });

  const [date, setDate] = useState<Date | undefined>(new Date());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const dealStatuses = [
    { value: "lead", label: "Lead" },
    { value: "qualified", label: "Qualified" },
    { value: "proposal", label: "Proposal" },
    { value: "negotiation", label: "Negotiation" },
    { value: "closed", label: "Closed" },
  ];

  // Fetch clients when component mounts
  useEffect(() => {
    const fetchClients = async () => {
      if (open) {
        setIsLoading(true);
        try {
          // Try to fetch clients from multiple endpoints through the proxy with authentication
          const endpoints = [
            // Try authenticated endpoints first
            { url: '/api/clients', auth: true },
            { url: '/api/clients/getAll', auth: true },
            // Then try public endpoints
            { url: '/api/clients/public', auth: false },
            { url: '/api/v1/clients', auth: true }
          ];

          let clientsData: Client[] = [];
          let success = false;

          // Create basic auth header
          const authHeader = 'Basic ' + btoa('admin:admin123');

          // Try each endpoint until one succeeds
          for (const endpoint of endpoints) {
            try {
              console.log(`Trying to fetch clients from ${endpoint.url}`);

              const headers: HeadersInit = {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              };

              if (endpoint.auth) {
                headers['Authorization'] = authHeader;
              }

              const response = await fetch(endpoint.url, {
                method: 'GET',
                headers: headers,
                credentials: endpoint.auth ? 'include' : 'omit'
              });

              if (response.ok) {
                const data = await response.json();
                console.log(`Successfully fetched clients from ${endpoint.url}:`, data);

                // Handle different response formats
                if (Array.isArray(data)) {
                  clientsData = data;
                } else if (data && Array.isArray(data.content)) {
                  clientsData = data.content;
                } else if (data && Array.isArray(data.data)) {
                  clientsData = data.data;
                } else if (data && typeof data === 'object') {
                  // Try to find an array in the response
                  const possibleArrays = Object.values(data).filter(val => Array.isArray(val));
                  if (possibleArrays.length > 0) {
                    clientsData = possibleArrays[0] as Client[];
                  }
                }

                if (clientsData.length > 0) {
                  success = true;
                  break;
                }
              }
            } catch (endpointError) {
              console.error(`Error fetching clients from ${endpoint.url}:`, endpointError);
            }
          }

          if (success) {
            console.log('Setting clients:', clientsData);
            setClients(clientsData);
          } else {
            // If all endpoints fail, try the clientService as a fallback
            console.log('Trying clientService as fallback');
            try {
              const fallbackData = await clientService.getAllClients();
              if (Array.isArray(fallbackData) && fallbackData.length > 0) {
                setClients(fallbackData);
                success = true;
              }
            } catch (fallbackError) {
              console.error("Failed to fetch clients from clientService:", fallbackError);
            }

            // If we still don't have clients, create a default client
            if (!success) {
              console.log('Creating a default client');
              try {
                const createResponse = await fetch('http://localhost:8080/api/clients', {
                  method: 'POST',
                  headers: {
                    'Authorization': authHeader,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    name: 'Default Client'
                  }),
                  credentials: 'include'
                });

                if (createResponse.ok) {
                  const createdClient = await createResponse.json();
                  console.log('Created default client:', createdClient);
                  setClients([createdClient]);

                  // Auto-select the created client
                  setFormData(prev => ({
                    ...prev,
                    client_id: createdClient.id,
                    client_name: createdClient.name
                  }));
                }
              } catch (createError) {
                console.error("Failed to create default client:", createError);
              }
            }
          }
        } catch (error) {
          console.error("Failed to fetch clients:", error);
          // Don't show an error toast to avoid annoying the user

          // Create a default client list with at least one client
          setClients([
            { id: 1, name: 'Default Client', created_at: new Date().toISOString() }
          ]);

          // Auto-select the default client
          setFormData(prev => ({
            ...prev,
            client_id: 1,
            client_name: 'Default Client'
          }));
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchClients();
  }, [open]);

  // Set form data when initialData changes or dialog opens
  useEffect(() => {
    if (initialData) {
      // If we have initial data, use it
      setFormData(initialData);
      setDate(initialData.dueDate ? new Date(initialData.dueDate) : new Date());

      // If we have a client_name but no client_id, try to find the client in the clients list
      if (initialData.client_name && (!initialData.client_id || initialData.client_id === 0) && clients.length > 0) {
        const matchingClient = clients.find(c => c.name === initialData.client_name);
        if (matchingClient) {
          setFormData(prev => ({
            ...prev,
            client_id: matchingClient.id
          }));
        }
      }
    } else {
      // Reset form when dialog opens without initial data
      setFormData({
        title: "",
        client_id: 0,
        client_name: "",
        value: "",
        dueDate: format(new Date(), "yyyy-MM-dd"),
        status: "lead",
        notes: "",
      });
      setDate(new Date());
    }
  }, [initialData, open, clients]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (newDate: Date | undefined) => {
    if (newDate) {
      setDate(newDate);
      setFormData((prev) => ({
        ...prev,
        dueDate: format(newDate, "yyyy-MM-dd"),
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form
    if (!formData.title || !formData.client_id || !formData.value) {
      toast.error("Please fill in all required fields");
      setIsSubmitting(false);
      return;
    }

    try {
      // Format the value to ensure it has a rupee sign and is a valid number
      let formattedValue = formData.value;
      if (!formattedValue.startsWith("₹")) {
        formattedValue = `₹${formattedValue}`;
      }

      // Make sure the value is a valid number
      const numericValue = parseFloat(formattedValue.replace(/[₹$,]/g, ''));
      if (isNaN(numericValue)) {
        toast.error("Please enter a valid number for the value");
        setIsSubmitting(false);
        return;
      }

      // Get the client name if not already set
      let clientName = formData.client_name;
      if (!clientName && formData.client_id) {
        const selectedClient = clients.find(c => c.id === Number(formData.client_id));
        if (selectedClient) {
          clientName = selectedClient.name;
        }
      }

      const finalFormData = {
        ...formData,
        value: formattedValue,
        client_id: Number(formData.client_id), // Ensure client_id is a number
        client_name: clientName
      };

      console.log("Submitting deal form with data:", finalFormData);

      // Save the deal
      onSave(finalFormData);

      // Show success toast
      toast.success(initialData ? "Deal updated successfully" : "Deal created successfully");

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error submitting deal form:", error);
      toast.error("An error occurred while saving the deal");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="sm:max-w-[500px] overflow-visible" style={{ zIndex: 9999 }}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            Fill in the details below to create a new deal.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="overflow-visible">
          <div className="grid gap-4 py-4 overflow-visible">
            <div className="grid grid-cols-4 items-center gap-4 overflow-visible">
              <Label htmlFor="title" className="text-right">
                Deal Title *
              </Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4 overflow-visible">
              <Label htmlFor="client_id" className="text-right">
                Client *
              </Label>
              <Select
                value={formData.client_id ? formData.client_id.toString() : ""}
                onValueChange={(value) => {
                  const clientId = parseInt(value, 10);
                  const selectedClient = clients.find(c => c.id === clientId);
                  handleSelectChange("client_id", clientId.toString());
                  if (selectedClient) {
                    handleSelectChange("client_name", selectedClient.name);
                  }
                }}
                disabled={isLoading}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder={isLoading ? "Loading clients..." : "Select client"} />
                </SelectTrigger>
                <SelectContent position="popper" className="bg-white shadow-lg border border-gray-200 rounded-md">
                  {clients.length > 0 ? (
                    clients.map((client) => (
                      <SelectItem key={client.id} value={client.id.toString()} className="cursor-pointer hover:bg-gray-100">
                        {client.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-clients" disabled>
                      {isLoading ? "Loading clients..." : "No clients available"}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4 overflow-visible">
              <Label htmlFor="value" className="text-right">
                Value *
              </Label>
              <Input
                id="value"
                name="value"
                value={formData.value}
                onChange={handleChange}
                placeholder="$0.00"
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4 overflow-visible">
              <Label className="text-right">Due Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className="col-span-3 justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 z-[10000]">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={handleDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid grid-cols-4 items-center gap-4 overflow-visible">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange("status", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent position="popper" className="bg-white shadow-lg border border-gray-200 rounded-md">
                  {dealStatuses.map((status) => (
                    <SelectItem key={status.value} value={status.value} className="cursor-pointer hover:bg-gray-100">
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4 overflow-visible">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                className="col-span-3"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Deal"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DealFormDialog;
