import React from "react";
import { Download, Edit, History, Trash } from "lucide-react";
import { toast } from "sonner";

interface SimpleActionMenuProps {
  onDownload?: () => void;
  onEdit?: () => void;
  onViewHistory?: () => void;
  onDelete?: () => void;
  isVisible: boolean;
}

const SimpleActionMenu: React.FC<SimpleActionMenuProps> = ({
  onDownload,
  onEdit,
  onViewHistory,
  onDelete,
  isVisible
}) => {
  if (!isVisible) return null;

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDownload) {
      onDownload();
    } else {
      toast.success("Downloading document");
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit();
    } else {
      toast.info("Editing variables");
    }
  };

  const handleViewHistory = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onViewHistory) {
      onViewHistory();
    } else {
      toast.info("Viewing version history");
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete();
    } else {
      toast.success("Document deleted");
    }
  };

  return (
    <div
      className="bg-white rounded-md shadow-lg border p-0 z-50 w-48 action-menu"
      onClick={(e) => e.stopPropagation()}
      style={{ boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)" }}
    >
      <div className="text-sm font-medium p-2 border-b">Actions</div>
      <div className="flex flex-col py-1">
        <button
          className="flex items-center text-sm hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleDownload}
        >
          <Download className="h-4 w-4 mr-3" />
          Download
        </button>

        <button
          className="flex items-center text-sm hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleEdit}
        >
          <Edit className="h-4 w-4 mr-3" />
          Edit Variables
        </button>

        <button
          className="flex items-center text-sm hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleViewHistory}
        >
          <History className="h-4 w-4 mr-3" />
          Version History
        </button>

        <button
          className="flex items-center text-sm text-red-600 hover:bg-gray-50 px-4 py-2 w-full text-left"
          onClick={handleDelete}
        >
          <Trash className="h-4 w-4 mr-3 text-red-600" />
          Delete
        </button>
      </div>
    </div>
  );
};

export default SimpleActionMenu;
