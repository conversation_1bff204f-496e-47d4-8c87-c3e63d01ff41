import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { candidateService } from '@/services/candidateService';
import { clientService } from '@/services/clientService';
import { projectService } from '@/services/projectService';

interface Candidate {
  id: string;
  name: string;
  clientId?: string;
  projectId?: string;
  billingRate?: number;
  salaryOffered?: number;
  designation?: string;
  panNo?: string;
  address?: string;
}

interface AutoGeneratedData {
  invoiceNumber: string;
  clientId: string;
  projectId: string;
  billingAmount: number;
  taxAmount: number;
  totalAmount: number;
  attendanceDays: number;
  description: string;
}

export default function AutoGenerationDemo() {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [clients, setClients] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [selectedCandidateId, setSelectedCandidateId] = useState<string>('');
  const [attendanceDays, setAttendanceDays] = useState<number>(20);
  const [autoGeneratedData, setAutoGeneratedData] = useState<AutoGeneratedData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load candidates
      const candidatesData = await candidateService.getAllCandidates();
      setCandidates(candidatesData);

      // Load clients
      const clientsData = await clientService.getAllClients();
      setClients(clientsData);

      // Load projects
      const projectsData = await projectService.getAllProjects();
      setProjects(projectsData);

      console.log('Loaded data:', { candidatesData, clientsData, projectsData });
    } catch (error) {
      console.error('Error loading data:', error);
      // Use mock data if API fails
      setCandidates([
        { id: '1', name: 'John Doe', clientId: '1', projectId: '1', billingRate: 1200, designation: 'Senior Developer' },
        { id: '2', name: 'Jane Smith', clientId: '2', projectId: '2', billingRate: 1500, designation: 'Tech Lead' },
        { id: '3', name: 'Mike Johnson', clientId: '1', projectId: '3', salaryOffered: 1000, designation: 'Junior Developer' }
      ]);
      setClients([
        { id: '1', name: 'TechCorp Inc.' },
        { id: '2', name: 'InnovateSoft Ltd.' }
      ]);
      setProjects([
        { id: '1', name: 'E-commerce Platform' },
        { id: '2', name: 'Mobile App Development' },
        { id: '3', name: 'Data Analytics Dashboard' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const autoGenerateInvoiceData = (candidateId: string, days: number) => {
    const candidate = candidates.find(c => c.id === candidateId);
    if (!candidate) return;

    console.log('🚀 Auto-generating invoice data for candidate:', candidate);

    // Calculate billing amount based on billing rate and attendance days
    const billingRate = candidate.billingRate || candidate.salaryOffered || 1000;
    const billingAmount = billingRate * days;

    // Calculate tax amounts (18% GST = 9% CGST + 9% SGST)
    const cgstAmount = billingAmount * 0.09;
    const sgstAmount = billingAmount * 0.09;
    const totalTaxAmount = cgstAmount + sgstAmount;
    const totalAmount = billingAmount + totalTaxAmount;

    // Auto-generate invoice number
    const invoiceNumber = `INV-${candidate.name?.replace(/\s+/g, '').toUpperCase()}-${Date.now().toString().slice(-6)}`;

    // Auto-fill description
    const description = `Invoice for ${candidate.name} - ${days} days @ ₹${billingRate}/day`;

    const generatedData: AutoGeneratedData = {
      invoiceNumber,
      clientId: candidate.clientId || '',
      projectId: candidate.projectId || '',
      billingAmount,
      taxAmount: totalTaxAmount,
      totalAmount,
      attendanceDays: days,
      description
    };

    setAutoGeneratedData(generatedData);
    console.log('✅ Auto-generation completed:', generatedData);
  };

  const handleCandidateChange = (candidateId: string) => {
    setSelectedCandidateId(candidateId);
    if (candidateId) {
      autoGenerateInvoiceData(candidateId, attendanceDays);
    } else {
      setAutoGeneratedData(null);
    }
  };

  const handleAttendanceChange = (days: number) => {
    setAttendanceDays(days);
    if (selectedCandidateId) {
      autoGenerateInvoiceData(selectedCandidateId, days);
    }
  };

  const selectedCandidate = candidates.find(c => c.id === selectedCandidateId);
  const selectedClient = clients.find(c => c.id === autoGeneratedData?.clientId);
  const selectedProject = projects.find(p => p.id === autoGeneratedData?.projectId);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-pink-500 rounded"></div>
            Invoice Auto-Generation Demo
          </CardTitle>
          <p className="text-muted-foreground">
            Select a candidate to automatically generate all invoice data including amounts, taxes, and descriptions.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Controls */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="candidate">Select Candidate</Label>
              <Select value={selectedCandidateId} onValueChange={handleCandidateChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a candidate..." />
                </SelectTrigger>
                <SelectContent>
                  {candidates.map(candidate => (
                    <SelectItem key={candidate.id} value={candidate.id}>
                      {candidate.name} - {candidate.designation || 'No designation'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="attendance">Attendance Days</Label>
              <Input
                id="attendance"
                type="number"
                value={attendanceDays}
                onChange={(e) => handleAttendanceChange(parseInt(e.target.value) || 20)}
                min="1"
                max="31"
                placeholder="20"
              />
            </div>
          </div>

          {/* Selected Candidate Info */}
          {selectedCandidate && (
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-lg">Selected Candidate Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <strong>Name:</strong> {selectedCandidate.name}
                  </div>
                  <div>
                    <strong>Designation:</strong> {selectedCandidate.designation || 'N/A'}
                  </div>
                  <div>
                    <strong>Billing Rate:</strong> ₹{selectedCandidate.billingRate || selectedCandidate.salaryOffered || 1000}/day
                  </div>
                  <div>
                    <strong>PAN:</strong> {selectedCandidate.panNo || 'N/A'}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Separator />

          {/* Auto-Generated Invoice Data */}
          {autoGeneratedData && (
            <Card className="bg-green-50 border-green-200">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Auto-Generated
                  </Badge>
                  Invoice Data
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Invoice Number</Label>
                      <div className="p-2 bg-white rounded border">{autoGeneratedData.invoiceNumber}</div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Client</Label>
                      <div className="p-2 bg-white rounded border">{selectedClient?.name || 'Not found'}</div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Project</Label>
                      <div className="p-2 bg-white rounded border">{selectedProject?.name || 'Not found'}</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Billing Amount</Label>
                      <div className="p-2 bg-white rounded border">₹{autoGeneratedData.billingAmount.toLocaleString()}</div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Tax Amount (18% GST)</Label>
                      <div className="p-2 bg-white rounded border">₹{autoGeneratedData.taxAmount.toLocaleString()}</div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Total Amount</Label>
                      <div className="p-2 bg-white rounded border font-semibold">₹{autoGeneratedData.totalAmount.toLocaleString()}</div>
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Description</Label>
                  <div className="p-2 bg-white rounded border">{autoGeneratedData.description}</div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                  <h4 className="font-medium text-yellow-800 mb-2">Calculation Breakdown:</h4>
                  <div className="text-sm text-yellow-700 space-y-1">
                    <div>Base Amount: ₹{(autoGeneratedData.billingAmount / attendanceDays).toLocaleString()}/day × {attendanceDays} days = ₹{autoGeneratedData.billingAmount.toLocaleString()}</div>
                    <div>CGST (9%): ₹{(autoGeneratedData.taxAmount / 2).toLocaleString()}</div>
                    <div>SGST (9%): ₹{(autoGeneratedData.taxAmount / 2).toLocaleString()}</div>
                    <div className="font-medium">Total: ₹{autoGeneratedData.totalAmount.toLocaleString()}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {!selectedCandidateId && (
            <div className="text-center py-8 text-muted-foreground">
              Select a candidate above to see auto-generated invoice data
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
