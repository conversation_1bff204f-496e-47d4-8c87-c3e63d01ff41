
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CustomSelect, SelectOption } from "@/components/ui/custom-select";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import React, { useEffect, useState } from "react";
import { clientService, Client } from "@/services/clientService";
import { projectService } from "@/services/projectService";
import hsnCodeService from "@/services/hsnCodeService";
import { invoiceTypeService } from "@/services/invoiceTypeService";
import { directInvoiceTypeService, InvoiceType } from "@/services/directInvoiceTypeService";
import { simpleInvoiceTypeService, SimpleInvoiceType } from "@/services/simpleInvoiceTypeService";
import { publicInvoiceTypeService, PublicInvoiceType } from "@/services/publicInvoiceTypeService";
import { redberylAccountService, RedberylAccount } from "@/services/redberylAccountService";
import { staffingTypeService } from "@/services/staffingTypeService";
import candidateService from "@/services/candidateService";
import attendanceService, { CandidateDetailDto } from "@/services/attendanceService";
import { invoiceGenerationService } from "@/services/invoiceGenerationService";
import { Loader2 } from "lucide-react";

// Helper function to handle both string and number types
const flexibleId = z.union([
  z.string().min(1),
  z.number().int().positive()
]).transform(val => val.toString());

const formSchema = z.object({
  invoiceNumber: z.string().min(1, "Invoice number is required"),
  clientId: flexibleId.refine(val => !!val, "Client is required"),
  projectId: flexibleId.refine(val => !!val, "Project is required"),
  candidateId: z.union([flexibleId, z.string().optional()]).optional(),
  attendanceDays: z.union([z.string(), z.number()]).optional().transform(val => {
    if (val === undefined || val === null || val === '') return 0;
    const num = typeof val === 'string' ? parseInt(val) : val;
    return isNaN(num) || num < 0 ? 0 : num;
  }),
  invoiceTypeId: flexibleId.refine(val => !!val, "Invoice type is required"),
  staffingTypeId: z.union([flexibleId, z.string().optional()]).optional(),
  billingAmount: z.union([z.string(), z.number()]).transform(val => {
    // Ensure it's a valid positive number
    const num = typeof val === 'string' ? parseFloat(val) : val;
    return isNaN(num) || num <= 0 ? 0.01 : num;
  }),
  taxAmount: z.union([z.string(), z.number()]).transform(val => {
    // Ensure it's a valid positive number
    const num = typeof val === 'string' ? parseFloat(val) : val;
    return isNaN(num) || num <= 0 ? 0.01 : num;
  }),
  totalAmount: z.union([z.string(), z.number()]).transform(val => {
    // Ensure it's a valid positive number
    const num = typeof val === 'string' ? parseFloat(val) : val;
    return isNaN(num) || num <= 0 ? 0.01 : num;
  }),
  issueDate: z.date(),
  dueDate: z.date(),
  isRecurring: z.boolean().default(false),
  publishedToFinance: z.boolean().default(false),
  publishedAt: z.date().optional(),
  hsnId: flexibleId.refine(val => !!val, "HSN code is required"),
  redberylAccountId: flexibleId.refine(val => !!val, "Redberyl account is required"),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface InvoiceFormProps {
  invoice?: {
    id: string;
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
  };
  onCancel: () => void;
  onSuccess: () => void;
}

// Error boundary component to catch rendering errors
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean, error: Error | null }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error in InvoiceForm:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-lg font-semibold text-red-700 mb-2">Something went wrong</h3>
          <p className="text-red-600 mb-4">
            {this.state.error?.message || "An unknown error occurred while rendering the form."}
          </p>
          <p className="text-sm text-gray-600 mb-4">
            Please try refreshing the page or contact support if the problem persists.
          </p>
          <div className="flex space-x-2">
            <button
              className="px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </button>
            <button
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
              onClick={() => this.setState({ hasError: false, error: null })}
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({ invoice, onCancel, onSuccess }) => {
  const isEditing = !!invoice;

  console.log("InvoiceForm: Rendering with props:", {
    isEditing,
    invoice: invoice ? JSON.stringify(invoice) : null
  });

  // Validate invoice data if in edit mode
  if (isEditing && invoice) {
    console.log("Validating invoice data:", invoice);
    // Check for required fields
    if (!invoice.id) {
      console.error("Invoice is missing ID");
    }
    if (!invoice.client) {
      console.warn("Invoice is missing client");
    }
    if (!invoice.project) {
      console.warn("Invoice is missing project");
    }
  }

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      invoiceNumber: "",
      clientId: "",
      projectId: "",
      candidateId: "",
      attendanceDays: 0,
      invoiceTypeId: "",
      staffingTypeId: "",
      billingAmount: 0,
      taxAmount: 0,
      totalAmount: 0,
      issueDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      isRecurring: false,
      publishedToFinance: false,
      publishedAt: undefined,
      hsnId: "",
      redberylAccountId: "",
      description: "",
    },
  });

  // Watch billing amount to auto-calculate tax and total
  const billingAmount = form.watch("billingAmount");

  // Watch candidate selection for auto-population
  const selectedCandidateId = form.watch("candidateId");

  // Function to handle candidate selection and auto-populate fields
  const handleCandidateSelection = async (candidateId: string) => {
    if (!candidateId) {
      setSelectedCandidateDetails(null);
      setAttendanceDays(0);
      setCalculatedSalary(0);
      return;
    }

    try {
      console.log("Fetching candidate details for ID:", candidateId);
      const candidateDetails = await attendanceService.getCandidateDetailById(parseInt(candidateId));
      console.log("Candidate details received:", candidateDetails);

      setSelectedCandidateDetails(candidateDetails);

      // Auto-populate client and project if available
      if (candidateDetails.clientId) {
        form.setValue('clientId', candidateDetails.clientId.toString());
      }
      if (candidateDetails.projectId) {
        form.setValue('projectId', candidateDetails.projectId.toString());
      }

      // Set billing amount to monthly salary if available
      if (candidateDetails.salaryOffered) {
        form.setValue('billingAmount', candidateDetails.salaryOffered);
      }

      toast.success(`Auto-populated details for ${candidateDetails.name}`);
    } catch (error) {
      console.error("Error fetching candidate details:", error);
      toast.error("Failed to fetch candidate details");
    }
  };

  // Function to calculate salary based on attendance days
  const calculateAttendanceSalary = (days: number) => {
    if (!selectedCandidateDetails?.dailyRate || days <= 0) {
      setCalculatedSalary(0);
      return;
    }

    const salary = selectedCandidateDetails.dailyRate * days;
    setCalculatedSalary(salary);

    // Update billing amount with calculated salary
    form.setValue('billingAmount', salary);
  };

  // Auto-calculate tax and total when billing amount changes
  useEffect(() => {
    if (billingAmount) {
      try {
        const billing = parseFloat(billingAmount.toString());
        if (!isNaN(billing) && billing > 0) {
          // Calculate tax as 18% of billing amount
          const tax = parseFloat((billing * 0.18).toFixed(2));
          // Calculate total as billing + tax
          const total = parseFloat((billing + tax).toFixed(2));

          // Update form values
          form.setValue("taxAmount", tax);
          form.setValue("totalAmount", total);
        }
      } catch (e) {
        console.error("Error auto-calculating tax and total:", e);
      }
    }
  }, [billingAmount, form]);

  // Watch for candidate selection changes
  useEffect(() => {
    if (selectedCandidateId && selectedCandidateId !== '') {
      handleCandidateSelection(selectedCandidateId);
    }
  }, [selectedCandidateId]);

  // Track if form has been initialized with dropdown values
  const [formInitialized, setFormInitialized] = useState(false);

  // State variables for dropdown data
  const [clients, setClients] = useState<Client[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [invoiceTypes, setInvoiceTypes] = useState<PublicInvoiceType[]>([]);
  const [staffingTypes, setStaffingTypes] = useState<any[]>([]);
  const [redberylAccounts, setRedberylAccounts] = useState<RedberylAccount[]>([]);
  const [hsnCodes, setHsnCodes] = useState<any[]>([]);
  const [candidates, setCandidates] = useState<any[]>([]);
  const [selectedCandidateDetails, setSelectedCandidateDetails] = useState<CandidateDetailDto | null>(null);
  const [attendanceDays, setAttendanceDays] = useState<number>(0);
  const [calculatedSalary, setCalculatedSalary] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [invoiceTypesLoading, setInvoiceTypesLoading] = useState<boolean>(false);





  // Initial form setup when editing an existing invoice
  useEffect(() => {
    if (invoice && !formInitialized &&
        clients.length > 0 && projects.length > 0 &&
        staffingTypes.length > 0 && redberylAccounts.length > 0 &&
        hsnCodes.length > 0 && !isLoading) {

      console.log("Setting initial form values for editing invoice:", invoice);
      console.log("Available dropdown data:", {
        clients: clients.length,
        projects: projects.length,
        staffingTypes: staffingTypes.length,
        redberylAccounts: redberylAccounts.length,
        hsnCodes: hsnCodes.length,
        candidates: candidates.length
      });

      // Debug the specific field values we're trying to map
      console.log("Invoice field values to map:", {
        staffingType: invoice.staffingType,
        hsnCode: invoice.hsnCode,
        redberylAccount: invoice.redberylAccount,
        candidate: invoice.candidate
      });

      // Parse amounts from string format - handle different currency formats
      const billingAmount = parseFloat(invoice.amount.replace(/[₹,]/g, '')) || 0;
      const taxAmount = parseFloat(invoice.tax.replace(/[₹,]/g, '')) || 0;
      const totalAmount = parseFloat(invoice.total.replace(/[₹SP,]/g, '')) || 0;

      console.log("Parsed amounts:", { billingAmount, taxAmount, totalAmount });

      // Set basic form values
      form.setValue('invoiceNumber', invoice.id);
      form.setValue('billingAmount', billingAmount);
      form.setValue('taxAmount', taxAmount);
      form.setValue('totalAmount', totalAmount);
      form.setValue('issueDate', new Date(invoice.issueDate));
      form.setValue('dueDate', new Date(invoice.dueDate));
      form.setValue('isRecurring', invoice.recurring);
      form.setValue('publishedToFinance', invoice.publishedToFinance || false);
      form.setValue('description', invoice.notes || "");

      // Map display names to IDs for dropdown values

      // Client dropdown - find ID by name
      if (invoice.client) {
        const clientMatch = clients.find(c => c.name === invoice.client);
        if (clientMatch) {
          console.log(`Setting clientId to ${clientMatch.id} for client ${invoice.client}`);
          form.setValue('clientId', clientMatch.id.toString());
        } else {
          console.warn(`Client not found: ${invoice.client}`);
        }
      }

      // Project dropdown - find ID by name
      if (invoice.project) {
        const projectMatch = projects.find(p => p.name === invoice.project);
        if (projectMatch) {
          console.log(`Setting projectId to ${projectMatch.id} for project ${invoice.project}`);
          form.setValue('projectId', projectMatch.id.toString());
        } else {
          console.warn(`Project not found: ${invoice.project}`);
        }
      }

      // Candidate dropdown - find ID by name
      if (invoice.candidate && invoice.candidate !== "-" && invoice.candidate !== null && invoice.candidate !== undefined) {
        console.log(`🔍 Looking for candidate: "${invoice.candidate}" in candidates:`, candidates);
        console.log(`📋 Available candidate names:`, candidates.map(c => c.name));
        console.log(`📊 Candidates length:`, candidates.length);

        const candidateMatch = candidates.find(c => c.name === invoice.candidate);
        if (candidateMatch) {
          console.log(`✅ Found candidate match! Setting candidateId to ${candidateMatch.id} for candidate ${invoice.candidate}`);
          form.setValue('candidateId', candidateMatch.id.toString());
        } else {
          console.warn(`❌ Candidate not found: "${invoice.candidate}"`);
          console.log(`📋 Available candidates:`, candidates.map(c => ({ id: c.id, name: c.name })));
          // Don't set a fallback for candidate since it's optional
        }
      } else {
        console.log("ℹ️ No candidate in invoice data (candidate is empty, '-', null, or undefined), leaving candidate field empty");
        // Explicitly clear the candidate field to ensure it shows "Select candidate"
        form.setValue('candidateId', '');
      }

      // Invoice type dropdown - find ID by type name
      if (invoice.invoiceType) {
        const invoiceTypeMatch = invoiceTypes.find(it => it.invoiceType === invoice.invoiceType);
        if (invoiceTypeMatch) {
          console.log(`Setting invoiceTypeId to ${invoiceTypeMatch.id} for type ${invoice.invoiceType}`);
          form.setValue('invoiceTypeId', invoiceTypeMatch.id.toString());
        } else {
          console.warn(`Invoice type not found: ${invoice.invoiceType}`);
        }
      }

      // Staffing type dropdown - find ID by name
      if (invoice.staffingType && invoice.staffingType !== null) {
        console.log(`Looking for staffing type: "${invoice.staffingType}" in:`, staffingTypes.map(st => st.name));
        const staffingTypeMatch = staffingTypes.find(st => st.name === invoice.staffingType);
        if (staffingTypeMatch) {
          console.log(`Setting staffingTypeId to ${staffingTypeMatch.id} for staffing type ${invoice.staffingType}`);
          form.setValue('staffingTypeId', staffingTypeMatch.id.toString());
        } else {
          console.warn(`Staffing type not found: ${invoice.staffingType}`);
          // Try to find by partial match or set to first available
          const fallbackStaffingType = staffingTypes[0];
          if (fallbackStaffingType) {
            console.log(`Using fallback staffing type: ${fallbackStaffingType.name}`);
            form.setValue('staffingTypeId', fallbackStaffingType.id.toString());
          }
        }
      } else {
        console.log("No staffing type in invoice data, using first available");
        const fallbackStaffingType = staffingTypes[0];
        if (fallbackStaffingType) {
          console.log(`Using default staffing type: ${fallbackStaffingType.name}`);
          form.setValue('staffingTypeId', fallbackStaffingType.id.toString());
        }
      }

      // HSN code dropdown - find ID by code
      if (invoice.hsnCode) {
        console.log(`Looking for HSN code: "${invoice.hsnCode}" in:`, hsnCodes.map(h => h.code));
        const hsnMatch = hsnCodes.find(h => h.code === invoice.hsnCode);
        if (hsnMatch) {
          console.log(`Setting hsnId to ${hsnMatch.id} for HSN code ${invoice.hsnCode}`);
          form.setValue('hsnId', hsnMatch.id.toString());
        } else {
          console.warn(`HSN code not found: ${invoice.hsnCode}`);
          // Use first available HSN code as fallback
          const fallbackHsn = hsnCodes[0];
          if (fallbackHsn) {
            console.log(`Using fallback HSN code: ${fallbackHsn.code}`);
            form.setValue('hsnId', fallbackHsn.id.toString());
          }
        }
      } else {
        console.log("No HSN code in invoice data, using first available");
        const fallbackHsn = hsnCodes[0];
        if (fallbackHsn) {
          console.log(`Using default HSN code: ${fallbackHsn.code}`);
          form.setValue('hsnId', fallbackHsn.id.toString());
        }
      }

      // Redberyl account dropdown - find ID by name
      if (invoice.redberylAccount) {
        console.log(`Looking for redberyl account: "${invoice.redberylAccount}" in:`, redberylAccounts.map(ra => ra.name));
        const accountMatch = redberylAccounts.find(ra => ra.name === invoice.redberylAccount);
        if (accountMatch) {
          console.log(`Setting redberylAccountId to ${accountMatch.id} for account ${invoice.redberylAccount}`);
          form.setValue('redberylAccountId', accountMatch.id.toString());
        } else {
          console.warn(`Redberyl account not found: ${invoice.redberylAccount}`);
          // Try to find by partial match or set to first available
          const fallbackAccount = redberylAccounts[0];
          if (fallbackAccount) {
            console.log(`Using fallback redberyl account: ${fallbackAccount.name}`);
            form.setValue('redberylAccountId', fallbackAccount.id.toString());
          }
        }
      } else {
        console.log("No redberyl account in invoice data, using first available");
        const fallbackAccount = redberylAccounts[0];
        if (fallbackAccount) {
          console.log(`Using default redberyl account: ${fallbackAccount.name}`);
          form.setValue('redberylAccountId', fallbackAccount.id.toString());
        }
      }

      console.log("Finished setting form values for editing");
      setFormInitialized(true);
    }
  }, [
    invoice,
    form,
    formInitialized,
    clients,
    projects,
    candidates,
    invoiceTypes,
    staffingTypes,
    hsnCodes,
    redberylAccounts,
    isLoading
  ]);

  const onSubmit = async (data: FormValues) => {
    console.log("Form submitted:", data);

    // Show a loading toast
    const loadingToast = toast.loading(isEditing ? "Updating invoice..." : "Creating invoice...");

    try {
      // Validate required fields
      if (!data.clientId) {
        toast.dismiss(loadingToast);
        toast.error("Client is required");
        return;
      }
      if (!data.projectId) {
        toast.dismiss(loadingToast);
        toast.error("Project is required");
        return;
      }
      if (!data.invoiceTypeId) {
        toast.dismiss(loadingToast);
        toast.error("Invoice Type is required");
        return;
      }

      // Find related objects for the invoice
      const client = clients.find(c => c.id.toString() === data.clientId.toString());
      const project = projects.find(p => p.id.toString() === data.projectId.toString());

      // Log the related objects for debugging
      console.log("InvoiceForm: Related objects for invoice submission:", {
        client,
        project,
        clientId: data.clientId,
        projectId: data.projectId,
        invoiceTypeId: data.invoiceTypeId,
        staffingTypeId: data.staffingTypeId,
        candidateId: data.candidateId,
        hsnId: data.hsnId,
        redberylAccountId: data.redberylAccountId
      });

      // Ensure numeric values are valid
      let billingAmount = 0;
      let taxAmount = 0;
      let totalAmount = 0;

      try {
        // Handle billing amount - remove currency symbols and commas
        if (typeof data.billingAmount === 'string') {
         // const cleanedValue = data.billingAmount.replace(/[₹$,]/g, '');
          billingAmount = parseFloat(data.billingAmount);
        } else {
          billingAmount = parseFloat(data.billingAmount.toString());
        }

        if (isNaN(billingAmount) || billingAmount <= 0) {
          billingAmount = 100.00; // Default value
        }

        console.log("Processed billing amount:", billingAmount);
      } catch (e) {
        console.error("Error processing billing amount:", e);
        billingAmount = 100.00; // Default value
      }

      // Calculate tax amount as 18% of billing amount
      try {
        // Handle tax amount - remove currency symbols and commas
        if (typeof data.taxAmount === 'string') {
          //const cleanedValue = data.taxAmount.replace(/[₹$,]/g, '');
          taxAmount = parseFloat(data.taxAmount);
        } else {
          taxAmount = parseFloat(data.taxAmount.toString());
        }

        if (isNaN(taxAmount) || taxAmount < 0) {
          // Always calculate tax as 18% of billing amount
          taxAmount = parseFloat((billingAmount * 0.18).toFixed(2));
        }

        console.log("Processed tax amount:", taxAmount);
      } catch (e) {
        console.error("Error processing tax amount:", e);
        taxAmount = parseFloat((billingAmount * 0.18).toFixed(2)); // Default value
      }

      // Calculate total amount as billing amount + tax amount
      try {
        // Handle total amount - remove currency symbols and commas
        if (typeof data.totalAmount === 'string') {
         // const cleanedValue = data.totalAmount.replace(/[₹$,]/g, '');
          totalAmount = parseFloat(data.totalAmount);
        } else {
          totalAmount = parseFloat(data.totalAmount.toString());
        }

        if (isNaN(totalAmount) || totalAmount <= 0) {
          // Always calculate total as billing + tax
          totalAmount = parseFloat((billingAmount + taxAmount).toFixed(2));
        }

        console.log("Processed total amount:", totalAmount);
      } catch (e) {
        console.error("Error processing total amount:", e);
        totalAmount = parseFloat((billingAmount + taxAmount).toFixed(2)); // Fallback calculation
      }

      // Convert form data to invoice object with proper type handling
      // Simplify the data structure to match what the backend expects
      const selectedCandidate = data.candidateId ? candidates.find(c => c.id.toString() === data.candidateId.toString()) : null;
      console.log("Selected candidate for invoice:", selectedCandidate);

      // Log all candidates for debugging
      console.log("All available candidates:", candidates);

      const invoiceData = {
        id: isEditing && invoice ? invoice.id : null,
        invoiceNumber: data.invoiceNumber || `INV-${Date.now()}`,
        clientId: parseInt(data.clientId.toString()), // Send as number
        projectId: parseInt(data.projectId.toString()), // Send as number
        candidateId: data.candidateId ? parseInt(data.candidateId.toString()) : null,
        // Include candidate object if candidateId is provided
        candidate: selectedCandidate ? {
          id: parseInt(selectedCandidate.id.toString()),
          name: selectedCandidate.name
        } : null,
        invoiceTypeId: parseInt(data.invoiceTypeId.toString()), // Send as number
        staffingTypeId: data.staffingTypeId ? parseInt(data.staffingTypeId.toString()) : null,
        // Ensure numeric values are sent as numbers, not strings
        billingAmount: Number(billingAmount),
        taxAmount: Number(taxAmount),
        totalAmount: Number(totalAmount),
        invoiceDate: data.issueDate instanceof Date ? data.issueDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        dueDate: data.dueDate instanceof Date ? data.dueDate.toISOString().split('T')[0] : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        isRecurring: Boolean(data.isRecurring),
        publishedToFinance: Boolean(data.publishedToFinance),
        publishedAt: data.publishedAt ? data.publishedAt.toISOString() : null,
        hsnId: data.hsnId ? parseInt(data.hsnId.toString()) : 1,
        redberylAccountId: data.redberylAccountId ? parseInt(data.redberylAccountId.toString()) : 1,
        description: data.description || ""
      };

      console.log("Prepared invoice data for API:", invoiceData);

      // Import the invoice service
      const { invoiceService } = await import('@/services/invoiceService');

      try {
        // Call the API to save the invoice
        if (isEditing) {
          const result = await invoiceService.updateInvoice(invoice.id, invoiceData);
          toast.dismiss(loadingToast);
          toast.success("Invoice updated successfully!");
          console.log("Update result:", result);
        } else {
          console.log("Sending invoice data to API:", invoiceData);
          console.log("Candidate ID being sent:", invoiceData.candidateId);
          console.log("Candidate object being sent:", invoiceData.candidate);

          // Try the direct endpoint first
          try {
            const response = await fetch('http://localhost:8080/invoices/create-direct', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                clientId: invoiceData.clientId,
                projectId: invoiceData.projectId,
                candidateId: invoiceData.candidateId,
                candidate: invoiceData.candidate,
                invoiceTypeId: invoiceData.invoiceTypeId,
                invoiceNumber: invoiceData.invoiceNumber,
                billingAmount: invoiceData.billingAmount,
                taxAmount: invoiceData.taxAmount,
                totalAmount: invoiceData.totalAmount,
                invoiceDate: invoiceData.invoiceDate,
                dueDate: invoiceData.dueDate,
                isRecurring: invoiceData.isRecurring,
                publishedToFinance: invoiceData.publishedToFinance,
                hsnId: invoiceData.hsnId,
                redberylAccountId: invoiceData.redberylAccountId,
                description: invoiceData.description
              })
            });

            console.log("Direct API response status:", response.status);

            // Get the response text first
            const responseText = await response.text();
            console.log("Direct API response text:", responseText);

            if (response.ok) {
              try {
                // Try to parse as JSON if possible
                const result = responseText ? JSON.parse(responseText) : {};
                console.log("Create result:", result);
                toast.dismiss(loadingToast);
                toast.success(`Invoice ${result.invoiceNumber || ''} created successfully!`);
                onSuccess();
                return;
              } catch (parseError) {
                console.error("Error parsing JSON response:", parseError);
                // If we can't parse as JSON but the response was OK, still consider it a success
                toast.dismiss(loadingToast);
                toast.success("Invoice created successfully!");
                onSuccess();
                return;
              }
            } else {
              console.error("API Error response:", responseText);

              // Check if it's the specific ERROR response
              if (responseText.includes("ERROR-")) {
                throw new Error("Missing required fields. Please check all form values.");
              } else {
                // Try to parse the error response as JSON
                try {
                  const errorJson = JSON.parse(responseText);
                  if (errorJson.message) {
                    throw new Error(errorJson.message);
                  } else {
                    throw new Error(`Server error: ${responseText}`);
                  }
                } catch (parseError) {
                  throw new Error(`Server error: ${responseText}`);
                }
              }
            }
          } catch (directError: any) {
            console.error("Direct API call failed:", directError);

            // Fall back to the service method
            const result = await invoiceService.createInvoice(invoiceData);
            console.log("Create result from service:", result);
            toast.dismiss(loadingToast);

            if (result && result.id) {
              toast.success(`Invoice ${result.invoiceNumber || ''} created successfully!`);
            } else if (result && result.message && result.message.includes('locally')) {
              toast.warning("Invoice created locally. Server connection issue detected.");
            } else {
              toast.success("Invoice created successfully!");
            }
          }
        }

        // Call onSuccess to refresh the invoice list
        onSuccess();
      } catch (apiError: any) {
        console.error("API Error:", apiError);
        toast.dismiss(loadingToast);

        // Show a more detailed error message
        if (apiError.message && apiError.message.includes("Failed to fetch")) {
          toast.error("Could not connect to the server. Please check if the backend is running.");
        } else if (apiError.message && apiError.message.includes("validator")) {
          toast.error("Validation error. Please check the form values and try again.");
          console.error("Validation error details:", apiError);
        } else if (apiError.message && apiError.message.includes("Validation error") ||
                  apiError.message && apiError.message.includes("Missing required fields")) {
          toast.error(apiError.message);
        } else if (apiError.message && apiError.message.includes("Server error")) {
          toast.error(apiError.message);
        } else {
          toast.error(`Failed to ${isEditing ? "update" : "create"} invoice: ${apiError.message || "Unknown error"}`);
        }

        // Try to create a local invoice as fallback
        try {
          console.log("Attempting to create local invoice as fallback");
          const localInvoice = {
            id: Date.now().toString(),
            invoiceNumber: `INV-${Date.now()}`,
            client: client ? client.name : "Client " + data.clientId,
            project: project ? project.name : "Project " + data.projectId,
            amount: `$${billingAmount.toFixed(2)}`,
            tax: `$${taxAmount.toFixed(2)}`,
            total: `$${totalAmount.toFixed(2)}`,
            issueDate: data.issueDate instanceof Date ? data.issueDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            dueDate: data.dueDate instanceof Date ? data.dueDate.toISOString().split('T')[0] : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: "Draft",
            recurring: Boolean(data.isRecurring),
            notes: data.description || ""
          };

          // Store in local storage for persistence
          const localInvoices = JSON.parse(localStorage.getItem('localInvoices') || '[]');
          localInvoices.push(localInvoice);
          localStorage.setItem('localInvoices', JSON.stringify(localInvoices));

          toast.info("Created a local draft invoice as fallback");
          onSuccess();
        } catch (localError) {
          console.error("Failed to create local invoice:", localError);
        }
      }
    } catch (error: any) {
      console.error("Error saving invoice:", error);
      toast.dismiss(loadingToast);
      toast.error(`Failed to save invoice: ${error.message || "Unknown error"}`);
    }
  };

  /**
   * Handle generating an invoice preview
   */
  const handleGenerateInvoice = async (data: FormValues) => {
    try {
      console.log("Starting invoice generation with data:", data);

      // Validate form data
      const validationResult = formSchema.safeParse(data);
      if (!validationResult.success) {
        console.error("Form validation failed:", validationResult.error);
        toast.error("Please fill in all required fields before generating an invoice");
        return;
      }

      // Find related objects for the invoice
      const client = clients.find(c => c.id.toString() === data.clientId.toString());
      const project = projects.find(p => p.id.toString() === data.projectId.toString());
      const invoiceType = invoiceTypes.find(t => t.id.toString() === data.invoiceTypeId.toString());
      const staffingType = data.staffingTypeId ? staffingTypes.find(t => t.id.toString() === data.staffingTypeId.toString()) : null;
      const candidate = data.candidateId ? candidates.find(c => c.id.toString() === data.candidateId.toString()) : null;
      const hsnCode = hsnCodes.find(h => h.id.toString() === data.hsnId.toString());
      const redberylAccount = redberylAccounts.find(a => a.id.toString() === data.redberylAccountId.toString());

      console.log("Found related objects:", {
        client,
        project,
        invoiceType,
        staffingType,
        candidate,
        hsnCode,
        redberylAccount
      });

      // Convert form data to invoice object with proper type handling
      const invoiceData = {
        id: isEditing && invoice ? invoice.id : 0,
        invoiceNumber: data.invoiceNumber,
        clientId: data.clientId.toString(),
        client: client ? {
          id: client.id.toString(),
          name: client.name
        } : null,
        projectId: data.projectId.toString(),
        project: project ? {
          id: project.id.toString(),
          name: project.name
        } : null,
        candidateId: data.candidateId ? data.candidateId.toString() : null,
        candidate: candidate ? {
          id: candidate.id.toString(),
          name: candidate.name
        } : null,
        invoiceTypeId: data.invoiceTypeId.toString(),
        invoiceType: invoiceType ? {
          id: invoiceType.id.toString(),
          invoiceType: invoiceType.invoiceType || "Standard"
        } : {
          id: "1",
          invoiceType: "Standard"
        },
        staffingTypeId: data.staffingTypeId ? data.staffingTypeId.toString() : null,
        staffingType: staffingType ? {
          id: staffingType.id.toString(),
          name: staffingType.name
        } : null,
        billingAmount: data.billingAmount.toString(),
        taxAmount: data.taxAmount.toString(),
        totalAmount: data.totalAmount.toString(),
        invoiceDate: data.issueDate.toISOString().split('T')[0],
        dueDate: data.dueDate.toISOString().split('T')[0],
        isRecurring: Boolean(data.isRecurring),
        publishedToFinance: Boolean(data.publishedToFinance),
        publishedAt: data.publishedAt ? data.publishedAt.toISOString() : null,
        hsnId: data.hsnId.toString(),
        hsn: hsnCode ? {
          id: hsnCode.id.toString(),
          code: hsnCode.code
        } : {
          id: "1",
          code: "998313"
        },
        redberylAccountId: data.redberylAccountId.toString(),
        redberylAccount: redberylAccount ? {
          id: redberylAccount.id.toString(),
          name: redberylAccount.name,
          bankName: redberylAccount.bankName || "",
          accountNo: redberylAccount.accountNo || "",
          ifscCode: redberylAccount.ifscCode || ""
        } : {
          id: "1",
          name: "Default Account",
          bankName: "",
          accountNo: "",
          ifscCode: ""
        },
        description: data.description || "",
        createdAt: new Date().toISOString()
      };

      console.log("Prepared invoice data for API:", invoiceData);

      // Generate and open the invoice preview
      await invoiceGenerationService.openPdfInvoicePreview(invoiceData);
      toast.success("Invoice generated successfully");
    } catch (error) {
      console.error("Error generating invoice:", error);
      toast.error("Failed to generate invoice");
    }
  };

  // State variables for dropdown data (moved to avoid hoisting issues)
  // These are now declared earlier in the component

  // Dedicated useEffect for invoice types using the public service
  useEffect(() => {
    const fetchInvoiceTypes = async () => {
      setInvoiceTypesLoading(true);
      console.log("InvoiceForm: Starting invoice type fetch with public service");

      try {
        // Use the public service which directly accesses the database
        console.log("InvoiceForm: Fetching invoice types with public service...");
        const invoiceTypesData = await publicInvoiceTypeService.getAllInvoiceTypes();

        if (invoiceTypesData && invoiceTypesData.length > 0) {
          console.log("InvoiceForm: Successfully fetched invoice types with public service:", invoiceTypesData);
          setInvoiceTypes(invoiceTypesData);
          toast.success("Invoice types loaded from database");
        } else {
          throw new Error("No invoice types found in database");
        }
      } catch (error) {
        console.error("InvoiceForm: Error fetching invoice types with public service:", error);

        // If public service fails, try the simple service
        try {
          console.log("InvoiceForm: Trying simple service as fallback...");
          const simpleData = await simpleInvoiceTypeService.getAllInvoiceTypes();

          if (simpleData && simpleData.length > 0) {
            // Convert to PublicInvoiceType format
            const convertedData = simpleData.map(item => ({
              id: item.id.toString(),
              invoiceType: item.name,
              typeDesc: item.description
            }));

            console.log("InvoiceForm: Successfully fetched with simple service:", convertedData);
            setInvoiceTypes(convertedData);
            toast.info("Using alternative source for invoice types");
          } else {
            throw new Error("No invoice types found from simple service");
          }
        } catch (simpleError) {
          console.error("InvoiceForm: Error fetching with simple service:", simpleError);

          // If simple service fails, try direct service
          try {
            console.log("InvoiceForm: Trying direct service as last resort...");
            const directData = await directInvoiceTypeService.getAllInvoiceTypes();

            if (directData && directData.length > 0) {
              // Convert to PublicInvoiceType format
              const convertedData = directData.map(item => ({
                id: item.id.toString(),
                invoiceType: item.name || item.invoiceType || "Unknown",
                typeDesc: item.description || item.typeDesc || ""
              }));

              console.log("InvoiceForm: Successfully fetched with direct service:", convertedData);
              setInvoiceTypes(convertedData);
              toast.info("Using fallback source for invoice types");
            } else {
              throw new Error("No invoice types found from direct service");
            }
          } catch (directError) {
            console.error("InvoiceForm: All services failed:", directError);

            // Fallback to hardcoded data as a last resort
            const hardcodedTypes = [
              { id: "1", invoiceType: "Standard", typeDesc: "Regular invoice for services or products" },
              { id: "2", invoiceType: "Proforma", typeDesc: "Preliminary bill of sale sent to buyers in advance of a shipment or delivery" },
              { id: "3", invoiceType: "Credit Note", typeDesc: "Document issued to indicate a return of funds" },
              { id: "4", invoiceType: "Debit Note", typeDesc: "Document issued to request additional payment" }
            ];

            console.log("InvoiceForm: Using hardcoded invoice types:", hardcodedTypes);
            setInvoiceTypes(hardcodedTypes);
            toast.warning("Using default invoice types. Could not connect to database.");
          }
        }
      } finally {
        setInvoiceTypesLoading(false);
      }
    };

    fetchInvoiceTypes();
  }, []);



  // Fetch data for all other dropdowns
  useEffect(() => {
    const fetchDropdownData = async () => {
      setIsLoading(true);
      try {
        // Fetch clients
        const clientsData = await clientService.getAllClients();
        setClients(clientsData);
        console.log("Fetched clients:", clientsData);

        // Fetch projects
        try {
          console.log("InvoiceForm: Fetching projects...");
          const projectsData = await projectService.getAllProjects();

          if (projectsData && projectsData.length > 0) {
            // Ensure IDs are strings for the dropdown and client data is properly structured
            const formattedProjects = projectsData.map(project => {
              // Process client data
              let clientData = project.client;

              // If client is not present but clientId is, create a placeholder client object
              if (!clientData && project.clientId) {
                clientData = { id: project.clientId, name: "Loading..." };

                // Try to fetch client data asynchronously
                (async () => {
                  try {
                    const clientResponse = await fetch(`/api/clients/${project.clientId}`);
                    if (clientResponse.ok) {
                      const clientInfo = await clientResponse.json();
                      console.log(`InvoiceForm: Fetched client info for project ${project.id}:`, clientInfo);

                      // Update the project with the client info
                      setProjects(prevProjects =>
                        prevProjects.map(p =>
                          p.id.toString() === project.id.toString()
                            ? { ...p, client: clientInfo }
                            : p
                        )
                      );
                    }
                  } catch (error) {
                    console.error(`InvoiceForm: Failed to fetch client info for project ${project.id}:`, error);
                  }
                })();
              }

              return {
                ...project,
                id: project.id.toString(),
                client: clientData
              };
            });

            setProjects(formattedProjects);
            console.log("InvoiceForm: Successfully fetched projects:", formattedProjects);
            toast.success("Projects loaded successfully");
          } else {
            console.warn("InvoiceForm: No projects returned from API");
            throw new Error("No projects found");
          }
        } catch (error) {
          console.error("InvoiceForm: Error fetching projects:", error);

          // Try direct fetch as fallback
          try {
            console.log("InvoiceForm: Trying direct fetch for projects...");
            const response = await fetch('/projects/getAll', {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              const data = await response.json();
              console.log("InvoiceForm: Direct fetch projects response:", data);

              if (Array.isArray(data) && data.length > 0) {
                // Ensure IDs are strings for the dropdown and client data is properly structured
                const formattedProjects = data.map(project => {
                  // Process client data
                  let clientData = project.client;

                  // If client is not present but clientId is, create a placeholder client object
                  if (!clientData && project.clientId) {
                    clientData = { id: project.clientId, name: "Loading..." };

                    // Try to fetch client data asynchronously
                    (async () => {
                      try {
                        const clientResponse = await fetch(`/api/clients/${project.clientId}`);
                        if (clientResponse.ok) {
                          const clientInfo = await clientResponse.json();
                          console.log(`InvoiceForm: Fetched client info for project ${project.id}:`, clientInfo);

                          // Update the project with the client info
                          setProjects(prevProjects =>
                            prevProjects.map(p =>
                              p.id.toString() === project.id.toString()
                                ? { ...p, client: clientInfo }
                                : p
                            )
                          );
                        }
                      } catch (error) {
                        console.error(`InvoiceForm: Failed to fetch client info for project ${project.id}:`, error);
                      }
                    })();
                  }

                  return {
                    ...project,
                    id: project.id.toString(),
                    client: clientData
                  };
                });

                setProjects(formattedProjects);
                console.log("InvoiceForm: Successfully fetched projects via direct fetch:", formattedProjects);
                toast.info("Projects loaded via alternative method");
                return;
              }
            }

            throw new Error("Direct fetch failed or returned no data");
          } catch (directError) {
            console.error("InvoiceForm: Direct fetch for projects failed:", directError);

            // Fallback to mock data
            const mockProjects = [
              { id: "1", name: "Website Redesign" },
              { id: "2", name: "ERP Implementation" },
              { id: "3", name: "Mobile App Development" },
            ];

            setProjects(mockProjects);
            console.log("InvoiceForm: Using mock project data:", mockProjects);
            toast.warning("Using sample project data. Could not connect to database.");
          }
        }

        // Fetch staffing types
        try {
          const staffingTypesData = await staffingTypeService.getAllStaffingTypes();
          if (staffingTypesData && staffingTypesData.length > 0) {
            setStaffingTypes(staffingTypesData);
            console.log("Fetched staffing types:", staffingTypesData);
          } else {
            throw new Error("No staffing types returned");
          }
        } catch (error) {
          console.error("Error fetching staffing types:", error);
          // Fallback to mock data with proper structure
          const fallbackStaffingTypes = [
            { id: "1", name: "Full-time", description: "Regular full-time employment" },
            { id: "2", name: "Part-time", description: "Regular part-time employment" },
            { id: "3", name: "Contract", description: "Fixed-term contract employment" },
            { id: "4", name: "Temporary", description: "Short-term temporary employment" },
          ];
          setStaffingTypes(fallbackStaffingTypes);
          console.log("Using fallback staffing types:", fallbackStaffingTypes);
        }

        // Fetch HSN codes
        try {
          const hsnCodesData = await hsnCodeService.getAllHsnCodes();
          setHsnCodes(hsnCodesData);
          console.log("Fetched HSN codes:", hsnCodesData);
        } catch (error) {
          console.error("Error fetching HSN codes:", error);
          // Fallback to mock data
          setHsnCodes([
            { id: "1", code: "998313" },
            { id: "2", code: "998314" },
            { id: "3", code: "998315" },
          ]);
        }

        // Fetch Redberyl accounts
        try {
          console.log("Attempting to fetch Redberyl accounts...");
          const redberylAccountsData = await redberylAccountService.getAllRedberylAccounts();

          if (redberylAccountsData && redberylAccountsData.length > 0) {
            console.log("Successfully fetched Redberyl accounts:", redberylAccountsData);

            // Ensure IDs are strings for the dropdown and add a name if missing
            const formattedAccounts = redberylAccountsData.map(account => ({
              ...account,
              id: account.id.toString(),
              name: account.name || account.accountName || `Account ${account.id} (${account.bankName || 'Unknown Bank'})`
            }));

            setRedberylAccounts(formattedAccounts);
            toast.success("Redberyl accounts loaded successfully");
          } else {
            console.warn("Redberyl accounts data was empty or invalid");
            throw new Error("Empty or invalid Redberyl accounts data");
          }
        } catch (error) {
          console.error("Error fetching Redberyl accounts:", error);
          console.log("Using fallback Redberyl accounts data");

          // Fallback to enhanced mock data with proper structure
          const fallbackAccounts = [
            {
              id: "1",
              name: "HDFC Main Account",
              bankName: "HDFC Bank",
              accountNo: "**************",
              ifscCode: "HDFC0001234",
              accountName: "RedBeryl Tech Solutions Pvt Ltd",
              glCode: "GL001",
              costCenter: "CC001"
            },
            {
              id: "2",
              name: "ICICI Secondary Account",
              bankName: "ICICI Bank",
              accountNo: "**************",
              ifscCode: "ICIC0005678",
              accountName: "RedBeryl Tech Solutions Pvt Ltd",
              glCode: "GL002",
              costCenter: "CC002"
            },
            {
              id: "3",
              name: "SBI Payroll Account",
              bankName: "State Bank of India",
              accountNo: "**************",
              ifscCode: "SBIN0012345",
              accountName: "RedBeryl Tech Solutions Pvt Ltd",
              glCode: "GL003",
              costCenter: "CC003"
            },
          ];

          console.log("Using fallback Redberyl accounts:", fallbackAccounts);
          setRedberylAccounts(fallbackAccounts);
        }

        // Fetch candidates
        try {
          console.log("🔄 Starting candidate fetch...");
          const candidatesData = await candidateService.getAllCandidates();
          console.log("📋 Fetched candidates from service:", candidatesData);

          if (candidatesData && candidatesData.length > 0) {
            console.log("✅ Setting candidates from service:", candidatesData.length, "candidates");
            setCandidates(candidatesData);
          } else {
            console.warn("⚠️ No candidates returned from service, trying alternative endpoints");

            // Try multiple alternative endpoints
            const alternativeEndpoints = [
              '/api/debug/candidates',
              '/api/candidates',
              'http://localhost:8080/candidates/getAll',
              'http://localhost:8080/debug/candidates'
            ];

            let candidatesFound = false;

            for (const endpoint of alternativeEndpoints) {
              try {
                console.log(`🔄 Trying endpoint: ${endpoint}`);
                const response = await fetch(endpoint, {
                  credentials: 'include',
                  headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                  }
                });

                if (response.ok) {
                  const debugCandidates = await response.json();
                  console.log(`✅ Fetched candidates from ${endpoint}:`, debugCandidates);

                  if (debugCandidates && Array.isArray(debugCandidates) && debugCandidates.length > 0) {
                    // Convert to expected format
                    const formattedCandidates = debugCandidates.map((c: any) => ({
                      id: c.id?.toString() || '0',
                      name: c.name || "Unknown Candidate"
                    }));

                    console.log(`📋 Setting formatted candidates from ${endpoint}:`, formattedCandidates);
                    setCandidates(formattedCandidates);
                    candidatesFound = true;
                    break;
                  }
                } else {
                  console.warn(`❌ Endpoint ${endpoint} returned status: ${response.status}`);
                }
              } catch (endpointError) {
                console.error(`❌ Error fetching from ${endpoint}:`, endpointError);
              }
            }

            if (!candidatesFound) {
              console.warn("⚠️ All candidate endpoints failed, using mock data");
              // Fallback to mock data that matches the invoice mock data
              const mockCandidates = [
                { id: "1", name: "John Doe" },
                { id: "2", name: "Jane Smith" },
                { id: "3", name: "Mike Johnson" },
                { id: "4", name: "Sarah Williams" },
                { id: "5", name: "David Brown" },
                { id: "6", name: "Alice Johnson" },
                { id: "7", name: "Bob Wilson" },
                { id: "8", name: "Carol Davis" }
              ];
              console.log("📋 Setting mock candidates:", mockCandidates);
              setCandidates(mockCandidates);
            }
          }
        } catch (error) {
          console.error("❌ Error fetching candidates:", error);
          // Fallback to mock data that matches the invoice mock data
          const mockCandidates = [
            { id: "1", name: "John Doe" },
            { id: "2", name: "Jane Smith" },
            { id: "3", name: "Mike Johnson" },
            { id: "4", name: "Sarah Williams" },
            { id: "5", name: "David Brown" },
            { id: "6", name: "Alice Johnson" },
            { id: "7", name: "Bob Wilson" },
            { id: "8", name: "Carol Davis" }
          ];
          console.log("📋 Setting fallback mock candidates:", mockCandidates);
          setCandidates(mockCandidates);
        }
      } catch (error) {
        console.error("Error fetching dropdown data:", error);
        toast.error("Failed to load some dropdown data. Using fallback values.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDropdownData();
  }, []);

  return (
    <Card>
      <CardContent className="p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {isLoading && (
              <div className="flex items-center justify-center py-4 mb-4 bg-gray-50 rounded-md">
                <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
                <p className="text-sm text-muted-foreground">Loading additional form data...</p>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invoice Number</FormLabel>
                    <FormControl>
                      <Input placeholder="INV-001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="clientId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={clients.map(client => ({
                          value: client.id,
                          label: client.name
                        }))}
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select client"
                        error={!!form.formState.errors.clientId}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="projectId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Project
                      {isLoading && (
                        <Loader2 className="ml-2 h-3 w-3 animate-spin text-muted-foreground" />
                      )}
                    </FormLabel>
                    <FormControl>
                      {isLoading ? (
                        <div className="flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground">
                          <span>Loading projects...</span>
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </div>
                      ) : (
                        <CustomSelect
                          options={projects.map(project => {
                            console.log("Mapping project for dropdown:", project);
                            // Extract client name from the project data
                            const clientName = project.client?.name ||
                                             (project.client ? project.client.name : 'Unknown');

                            return {
                              value: project.id.toString(),
                              label: project.name || "Unnamed Project",
                              description: `Client: ${clientName}`
                            };
                          })}
                          value={field.value}
                          onChange={(value) => {
                            console.log("Project selected:", value);
                            field.onChange(value);

                            // Log the selected project for debugging
                            const selected = projects.find(p => p.id.toString() === value);
                            if (selected) {
                              console.log("Selected project details:", selected);
                              toast.info(`Selected project: ${selected.name}`);
                            }
                          }}
                          placeholder="Select project"
                          error={!!form.formState.errors.projectId}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                    {!isLoading && projects.length === 0 && (
                      <p className="text-xs text-red-500 mt-1">
                        No projects available. Please check your connection.
                      </p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="issueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Issue Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="invoiceTypeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Invoice Type
                      {invoiceTypesLoading && (
                        <Loader2 className="ml-2 h-3 w-3 animate-spin text-muted-foreground" />
                      )}
                    </FormLabel>
                    <FormControl>
                      {invoiceTypesLoading ? (
                        <div className="flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground">
                          <span>Loading invoice types...</span>
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </div>
                      ) : (
                        <CustomSelect
                          options={invoiceTypes.map(type => {
                            console.log("Mapping invoice type for dropdown:", type);
                            return {
                              value: type.id.toString(),
                              label: type.invoiceType,
                              description: type.typeDesc
                            };
                          })}
                          value={field.value}
                          onChange={(value) => {
                            console.log("Invoice type selected:", value);
                            field.onChange(value);

                            // Log the selected invoice type for debugging
                            const selected = invoiceTypes.find(t => t.id.toString() === value);
                            if (selected) {
                              console.log("Selected invoice type details:", selected);
                              toast.info(`Selected invoice type: ${selected.invoiceType}`);
                            }
                          }}
                          placeholder="Select invoice type"
                          error={!!form.formState.errors.invoiceTypeId}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                    {!invoiceTypesLoading && invoiceTypes.length === 0 && (
                      <p className="text-xs text-red-500 mt-1">
                        No invoice types available. Please check your connection.
                      </p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="staffingTypeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Staffing Type</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={staffingTypes.map(type => ({
                          value: type.id,
                          label: type.name
                        }))}
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select staffing type"
                        error={!!form.formState.errors.staffingTypeId}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="billingAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Billing Amount</FormLabel>
                    <FormControl>
                      <Input placeholder="0.00" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="taxAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Amount</FormLabel>
                    <FormControl>
                      <Input placeholder="0.00" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="totalAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Amount</FormLabel>
                    <FormControl>
                      <Input placeholder="0.00" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hsnId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>HSN Code</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={hsnCodes.map(code => ({
                          value: code.id,
                          label: code.code,
                          description: code.description
                        }))}
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select HSN code"
                        error={!!form.formState.errors.hsnId}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="redberylAccountId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Redberyl Account</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        console.log("Redberyl account selected:", value);
                        field.onChange(value);
                      }}
                      value={field.value || undefined}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="hover:cursor-pointer">
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-[300px] z-[9999]">
                        {redberylAccounts.length === 0 ? (
                          <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                            No accounts available
                          </div>
                        ) : (
                          redberylAccounts.map((account) => (
                            <SelectItem
                              key={account.id}
                              value={account.id.toString()}
                              title={`${account.bankName || ''} - ${account.accountNo || ''} - ${account.ifscCode || ''}`}
                              className="py-2"
                            >
                              <div className="flex flex-col">
                                <span className="font-medium">{account.name}</span>
                                <span className="text-xs text-muted-foreground">
                                  {account.bankName || 'Unknown Bank'} • {account.accountNo || 'No Account Number'}
                                </span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {field.value && redberylAccounts.find(a => a.id.toString() === field.value) && (
                      <div className="mt-2 text-xs text-muted-foreground border p-2 rounded-md bg-muted/30">
                        <p className="mb-1">
                          <span className="font-medium">Bank:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.bankName || 'N/A'}
                        </p>
                        <p className="mb-1">
                          <span className="font-medium">Account No:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.accountNo || 'N/A'}
                        </p>
                        <p className="mb-1">
                          <span className="font-medium">IFSC:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.ifscCode || 'N/A'}
                        </p>
                        {redberylAccounts.find(a => a.id.toString() === field.value)?.gstn && (
                          <p className="mb-1">
                            <span className="font-medium">GSTN:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.gstn}
                          </p>
                        )}
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="candidateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Candidate (Optional)</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={candidates.map(candidate => {
                          console.log("Mapping candidate for dropdown:", candidate);
                          return {
                            value: candidate.id.toString(),
                            label: candidate.name || "Unknown Candidate"
                          };
                        })}
                        value={field.value}
                        onChange={(value) => {
                          console.log("Candidate selected:", value);
                          field.onChange(value);

                          // Log the selected candidate for debugging
                          const selected = candidates.find(c => c.id.toString() === value);
                          if (selected) {
                            console.log("Selected candidate details:", selected);
                          }
                        }}
                        placeholder="Select candidate"
                        error={!!form.formState.errors.candidateId}
                      />
                    </FormControl>
                    <FormMessage />
                    {candidates.length === 0 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        No candidates available. You can still create an invoice without a candidate.
                      </p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter invoice description here..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="isRecurring"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Recurring Invoice</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Set this invoice to automatically recur
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="publishedToFinance"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Published to Finance</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Mark this invoice as published to finance department
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" type="button" onClick={onCancel}>Cancel</Button>
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  // Reset the form to initial values
                  if (isEditing && invoice) {
                    console.log("Resetting form to initial values");
                    setFormInitialized(false);

                    // Force a re-render
                    setTimeout(() => {
                      // Reset the form with the original invoice values
                      const billingAmount = parseFloat(invoice.amount.replace(/[₹$,]/g, '')) || 0;
                      const taxAmount = parseFloat(invoice.tax.replace(/[₹$,]/g, '')) || 0;
                      const totalAmount = parseFloat(invoice.total.replace(/[₹$,]/g, '')) || 0;

                      form.reset({
                        invoiceNumber: invoice.id,
                        billingAmount,
                        taxAmount,
                        totalAmount,
                        issueDate: new Date(invoice.issueDate),
                        dueDate: new Date(invoice.dueDate),
                        isRecurring: invoice.recurring,
                        publishedToFinance: invoice.publishedToFinance || false,
                        description: invoice.notes || "",
                        clientId: invoice.client || "",
                        projectId: invoice.project || "",
                        candidateId: invoice.candidate || "",
                        invoiceTypeId: invoice.invoiceType || "",
                        staffingTypeId: invoice.staffingType || "",
                        hsnId: invoice.hsnCode || "",
                        redberylAccountId: invoice.redberylAccount || "",
                      });

                      setFormInitialized(true);
                    }, 100);

                    toast.info("Form has been reset to initial values");
                  } else {
                    form.reset();
                    toast.info("Form has been cleared");
                  }
                }}
                className="bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-700 border-gray-200"
              >
                Reset Form
              </Button>
              <Button
                variant="outline"
                type="button"
                onClick={async () => {
                  // First check if the API is available
                  const isApiAvailable = await invoiceGenerationService.testApiAvailability();
                  if (!isApiAvailable) {
                    toast.error("API not available. Please check that the backend server is running at http://localhost:8080.");
                    return;
                  }

                  // If API is available, proceed with generating the invoice
                  handleGenerateInvoice(form.getValues());
                }}
                className="bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 border-blue-200"
              >
                Generate Invoice
              </Button>
              <Button
                type="submit"
                onClick={() => {
                  // Validate required fields before submission
                  const values = form.getValues();
                  let hasErrors = false;

                  if (!values.clientId) {
                    form.setError('clientId', { message: 'Client is required' });
                    hasErrors = true;
                  }

                  if (!values.projectId) {
                    form.setError('projectId', { message: 'Project is required' });
                    hasErrors = true;
                  }

                  if (!values.invoiceTypeId) {
                    form.setError('invoiceTypeId', { message: 'Invoice Type is required' });
                    hasErrors = true;
                  }

                  if (!values.invoiceNumber) {
                    // Generate a properly formatted invoice number (INV-001 format)
                    // Get the current count of invoices and add 1, or start with 1 if no invoices exist
                    try {
                      // Try to fetch the latest invoice number from the server using the API endpoint
                      fetch('/api/invoices')
                        .then(response => response.json())
                        .then(data => {
                          let nextNumber = 1;
                          if (Array.isArray(data) && data.length > 0) {
                            // Find the highest invoice number
                            const invoiceNumbers = data
                              .map(inv => {
                                const invoiceNumber = inv.invoiceNumber;
                                const match = invoiceNumber?.match(/INV-(\d+)/);
                                return match ? parseInt(match[1], 10) : 0;
                              })
                              .filter(num => !isNaN(num));

                            if (invoiceNumbers.length > 0) {
                              nextNumber = Math.max(...invoiceNumbers) + 1;
                            }
                          }
                          // Format with leading zeros (e.g., INV-001)
                          const defaultInvoiceNumber = `INV-${String(nextNumber).padStart(3, '0')}`;
                          form.setValue('invoiceNumber', defaultInvoiceNumber);
                          console.log(`Generated invoice number: ${defaultInvoiceNumber}`);
                        })
                        .catch(err => {
                          console.error("Error fetching invoices for numbering:", err);
                          // Try fallback endpoint
                          fetch('/api/invoices')
                            .then(response => response.json())
                            .then(data => {
                              let nextNumber = 1;
                              if (Array.isArray(data) && data.length > 0) {
                                const invoiceNumbers = data
                                  .map(inv => {
                                    const match = inv.invoiceNumber?.match(/INV-(\d+)/);
                                    return match ? parseInt(match[1], 10) : 0;
                                  })
                                  .filter(num => !isNaN(num));

                                if (invoiceNumbers.length > 0) {
                                  nextNumber = Math.max(...invoiceNumbers) + 1;
                                }
                              }
                              const defaultInvoiceNumber = `INV-${String(nextNumber).padStart(3, '0')}`;
                              form.setValue('invoiceNumber', defaultInvoiceNumber);
                              console.log(`Generated invoice number from fallback: ${defaultInvoiceNumber}`);
                            })
                            .catch(fallbackErr => {
                              console.error("Fallback endpoint also failed:", fallbackErr);
                              const defaultInvoiceNumber = `INV-001`;
                              form.setValue('invoiceNumber', defaultInvoiceNumber);
                              console.log(`Using default invoice number: ${defaultInvoiceNumber}`);
                            });
                        });
                    } catch (error) {
                      console.error("Error setting invoice number:", error);
                      // Fallback to a simple format
                      const defaultInvoiceNumber = `INV-001`;
                      form.setValue('invoiceNumber', defaultInvoiceNumber);
                      console.log(`Using fallback invoice number: ${defaultInvoiceNumber}`);
                    }
                  }

                  // Validate billing amount
                  if (!values.billingAmount || parseFloat(values.billingAmount.toString()) <= 0) {
                    form.setValue('billingAmount', 100);
                  }

                  // Validate tax amount
                  if (!values.taxAmount || parseFloat(values.taxAmount.toString()) < 0) {
                    form.setValue('taxAmount', 18);
                  }

                  // Validate total amount
                  if (!values.totalAmount || parseFloat(values.totalAmount.toString()) <= 0) {
                    const billing = parseFloat(form.getValues('billingAmount').toString()) || 100;
                    const tax = parseFloat(form.getValues('taxAmount').toString()) || 18;
                    form.setValue('totalAmount', billing + tax);
                  }

                  // Set default HSN code if not provided
                  if (!values.hsnId) {
                    form.setValue('hsnId', '1');
                  }

                  // Set default Redberyl Account if not provided
                  if (!values.redberylAccountId) {
                    form.setValue('redberylAccountId', '1');
                  }

                  if (hasErrors) {
                    toast.error("Please fill in all required fields");
                  }
                }}
              >
                {isEditing ? "Update Invoice" : "Create Invoice"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

// Wrap the InvoiceForm with the ErrorBoundary
const InvoiceFormWithErrorBoundary: React.FC<InvoiceFormProps> = (props) => {
  return (
    <ErrorBoundary>
      <InvoiceForm {...props} />
    </ErrorBoundary>
  );
};

export default InvoiceFormWithErrorBoundary;
