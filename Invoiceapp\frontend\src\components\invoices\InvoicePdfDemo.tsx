import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Eye } from 'lucide-react';
import InvoicePdfTemplate from './InvoicePdfTemplate';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { toast } from 'sonner';

const InvoicePdfDemo: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  // Sample invoice data matching the format from your image
  const sampleInvoice = {
    id: "INV-005",
    client: "abc",
    project: "hadapsar",
    candidate: "prathamesh kadam",
    invoiceType: "Services",
    staffingType: "Full-time",
    amount: "₹20,000.00",
    tax: "₹3,600.00",
    total: "₹23,600.00",
    issueDate: "2025-05-21",
    dueDate: "2025-06-21",
    status: "Draft",
    recurring: false,
    notes: "",
    // Enhanced fields
    employeeName: "prathamesh kadam",
    employeeEngagementCode: "ENG-0016",
    joiningDate: "2025-05-21",
    rate: "₹200.00",
    billAmount: "₹20,000.00",
    cgst: "9%",
    sgst: "9%",
    igst: "18%",
    netPayable: "₹23,600.00",
    bankName: "HDFC Bank",
    branchName: "MG Road Branch",
    accountName: "Acme Corporation Pvt Ltd",
    accountNo: "***********",
    ifscCode: "HDFC0001234",
    accountType: "Current",
    gstin: "29**********2Z5",
    cin: "U12345KA2020PTC012345",
    panNo: "**********",
    attendanceDays: 25,
    hsnCode: "998313"
  };

  const generatePDF = async () => {
    setIsGenerating(true);
    try {
      // Create a temporary div to render the invoice template
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      tempDiv.style.width = '800px';
      tempDiv.style.backgroundColor = 'white';
      document.body.appendChild(tempDiv);

      // Render the invoice template
      const root = document.createElement('div');
      tempDiv.appendChild(root);

      const ReactDOM = await import('react-dom/client');
      const reactRoot = ReactDOM.createRoot(root);
      reactRoot.render(<InvoicePdfTemplate invoice={sampleInvoice} />);

      // Wait for rendering and images to load
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate PDF
      const canvas = await html2canvas(root, {
        scale: 2,
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff',
        width: 800,
        height: root.scrollHeight
      });

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      pdf.save(`RedBeryl-Invoice-${sampleInvoice.id}.pdf`);

      // Clean up
      document.body.removeChild(tempDiv);
      
      toast.success("PDF generated successfully with RedBeryl logo!");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    } finally {
      setIsGenerating(false);
    }
  };

  const previewInvoice = () => {
    // Open preview in new window
    const previewWindow = window.open('', '_blank', 'width=900,height=700');
    if (previewWindow) {
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Invoice Preview - ${sampleInvoice.id}</title>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
          </style>
        </head>
        <body>
          <div id="preview-root"></div>
          <script type="module">
            // This would need to be implemented with proper React rendering
            // For now, we'll show a simple message
            document.getElementById('preview-root').innerHTML = 
              '<h2>Invoice Preview</h2><p>Enhanced invoice with RedBeryl logo will be displayed here.</p>';
          </script>
        </body>
        </html>
      `);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-pink-500 rounded"></div>
          RedBeryl Invoice PDF Generator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Sample Invoice Data:</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div><strong>Invoice ID:</strong> {sampleInvoice.id}</div>
            <div><strong>Client:</strong> {sampleInvoice.client}</div>
            <div><strong>Employee:</strong> {sampleInvoice.employeeName}</div>
            <div><strong>Amount:</strong> {sampleInvoice.total}</div>
            <div><strong>Attendance Days:</strong> {sampleInvoice.attendanceDays}</div>
            <div><strong>HSN Code:</strong> {sampleInvoice.hsnCode}</div>
          </div>
          <div className="mt-3 p-2 bg-blue-50 rounded text-xs">
            <strong>Logo Integration:</strong> The PDF will include the RedBeryl logo in the header.
            To use your actual logo image, place it in <code>/public/assets/redberyl-logo.png</code>
          </div>
        </div>

        <div className="flex gap-3">
          <Button 
            onClick={generatePDF} 
            disabled={isGenerating}
            className="flex-1"
          >
            <Download className="w-4 h-4 mr-2" />
            {isGenerating ? 'Generating PDF...' : 'Download PDF'}
          </Button>
          
          <Button 
            onClick={previewInvoice} 
            variant="outline"
            className="flex-1"
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview Invoice
          </Button>
        </div>

        <div className="text-sm text-gray-600">
          <h4 className="font-semibold mb-1">Features included:</h4>
          <ul className="list-disc list-inside space-y-1">
            <li>RedBeryl company logo and branding</li>
            <li>Professional invoice layout matching your template</li>
            <li>Automatic GST calculations (CGST, SGST, IGST)</li>
            <li>Employee attendance-based billing</li>
            <li>Bank details and payment information</li>
            <li>Authorized signatory section</li>
            <li>Company registration details (GSTIN, CIN, PAN)</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default InvoicePdfDemo;
