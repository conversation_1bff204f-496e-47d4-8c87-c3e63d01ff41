import React from 'react';
import { format } from 'date-fns';
import RedBerylLogo from '../ui/RedBerylLogo';
import { getDefaultRedBerylLogoSVG } from '../../utils/logoUtils';

interface InvoicePdfTemplateProps {
  invoice: {
    id: string;
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
    // Additional fields for enhanced invoice
    employeeName?: string;
    employeeEngagementCode?: string;
    joiningDate?: string;
    rate?: string;
    billAmount?: string;
    cgst?: string;
    sgst?: string;
    igst?: string;
    netPayable?: string;
    bankName?: string;
    branchName?: string;
    accountName?: string;
    accountNo?: string;
    ifscCode?: string;
    accountType?: string;
    gstin?: string;
    cin?: string;
    panNo?: string;
    attendanceDays?: number;
  };
}

const InvoicePdfTemplate: React.FC<InvoicePdfTemplateProps> = ({ invoice }) => {
  // Ensure all required fields are present with enhanced data
  const safeInvoice = {
    ...invoice,
    client: invoice.client || "abc",
    project: invoice.project || "hadapsar",
    candidate: invoice.candidate || "prathamesh kadam",
    invoiceType: invoice.invoiceType || "Services",
    staffingType: invoice.staffingType || "Full-time",
    amount: invoice.amount || "₹200.00",
    tax: invoice.tax || "₹36.00",
    total: invoice.total || "₹236.00",
    issueDate: invoice.issueDate || "2025-05-21",
    dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
    status: invoice.status || "Draft",
    recurring: invoice.recurring || false,
    notes: invoice.notes || "",
    // Enhanced fields with defaults
    employeeName: invoice.employeeName || invoice.candidate || "prathamesh kadam",
    employeeEngagementCode: invoice.employeeEngagementCode || "ENG-0016",
    joiningDate: invoice.joiningDate || "2025-05-21",
    rate: invoice.rate || "₹200.00",
    billAmount: invoice.billAmount || "₹20,000.00",
    cgst: invoice.cgst || "9%",
    sgst: invoice.sgst || "9%",
    igst: invoice.igst || "18%",
    netPayable: invoice.netPayable || "₹23,600.00",
    bankName: invoice.bankName || "HDFC Bank",
    branchName: invoice.branchName || "MG Road Branch",
    accountName: invoice.accountName || "Acme Corporation Pvt Ltd",
    accountNo: invoice.accountNo || "***********",
    ifscCode: invoice.ifscCode || "HDFC0001234",
    accountType: invoice.accountType || "Current",
    gstin: invoice.gstin || "29**********2Z5",
    cin: invoice.cin || "U12345KA2020PTC012345",
    panNo: invoice.panNo || "**********",
    attendanceDays: invoice.attendanceDays || 20,
    hsnCode: invoice.hsnCode || "998313"
  };

  console.log("InvoicePdfTemplate: Rendering with enhanced data:", safeInvoice);

  // Calculate amounts based on attendance if available
  const calculateAmounts = () => {
    const dailyRate = parseFloat(safeInvoice.rate?.replace(/[₹,]/g, '') || '200');
    const days = safeInvoice.attendanceDays || 20;
    const baseAmount = dailyRate * days;
    const cgstAmount = baseAmount * 0.09; // 9%
    const sgstAmount = baseAmount * 0.09; // 9%
    const igstAmount = baseAmount * 0.18; // 18%
    const totalAmount = baseAmount + cgstAmount + sgstAmount;

    return {
      baseAmount: `₹${baseAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      cgstAmount: `₹${cgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      sgstAmount: `₹${sgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      igstAmount: `₹${igstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      totalAmount: `₹${totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`
    };
  };

  const amounts = calculateAmounts();

  return (
    <div id="invoice-pdf-content" style={{
      fontFamily: 'Arial, sans-serif',
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      backgroundColor: 'white',
      color: '#333',
      fontSize: '12px',
      lineHeight: '1.4'
    }}>
      {/* Header with Logo */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        paddingBottom: '20px'
      }}>
        <div style={{ flex: 1 }}>
          {/* RedBeryl Logo as inline SVG for better PDF compatibility */}
          <svg
            width="280"
            height="90"
            viewBox="0 0 600 240"
            style={{ marginBottom: '10px' }}
          >
            {/* Background */}
            <rect width="600" height="240" fill="white"/>

            {/* Cloud shapes representing RedBeryl logo */}
            <g transform="translate(20, 50)">
              {/* Left blue cloud */}
              <path
                d="M0 40 C0 20, 20 0, 40 0 C60 0, 80 20, 80 40 C80 60, 60 80, 40 80 C20 80, 0 60, 0 40 Z"
                fill="#3678FF"
              />

              {/* Right pink/magenta cloud */}
              <path
                d="M50 20 C50 10, 60 5, 70 5 C80 5, 90 10, 90 20 C90 30, 80 35, 70 35 C60 35, 50 30, 50 20 Z"
                fill="#EA336A"
              />

              {/* Bottom connecting cloud */}
              <path
                d="M30 50 C30 45, 35 40, 40 40 C45 40, 50 45, 50 50 C50 55, 45 60, 40 60 C35 60, 30 55, 30 50 Z"
                fill="#9C27B0"
              />

              {/* Small connecting elements */}
              <circle cx="55" cy="35" r="4" fill="#2196F3"/>
              <circle cx="45" cy="45" r="3" fill="#F44336"/>
              <circle cx="65" cy="25" r="2" fill="#FF9800"/>
            </g>

            {/* RedBeryl Text */}
            <g transform="translate(130, 50)">
              {/* Red text */}
              <text x="0" y="45" fontFamily="Arial, sans-serif" fontSize="52" fontWeight="bold" fill="#EA336A">Red</text>

              {/* Beryl text */}
              <text x="120" y="45" fontFamily="Arial, sans-serif" fontSize="52" fontWeight="bold" fill="#1565C0">Beryl</text>

              {/* TECH SOLUTIONS */}
              <text x="0" y="75" fontFamily="Arial, sans-serif" fontSize="18" fontWeight="600" fill="#555555" letterSpacing="3px">TECH SOLUTIONS</text>

              {/* Tagline */}
              <text x="0" y="100" fontFamily="Arial, sans-serif" fontSize="14" fill="#777777" fontStyle="italic">Integrates Business With Technology</text>
            </g>
          </svg>
        </div>
        <div style={{
          textAlign: 'center',
          flex: 1
        }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            margin: '0',
            color: '#333',
            textDecoration: 'underline',
            letterSpacing: '2px'
          }}>INVOICE</h1>
        </div>
        <div style={{ flex: 1 }}></div>
      </div>

      {/* Invoice Details and Billed To Section */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '40px',
        marginBottom: '30px'
      }}>
        {/* Invoice Details */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333'
          }}>Invoice Details :-</h3>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice Date:</strong> {format(new Date(safeInvoice.issueDate), "dd/MM/yyyy")}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice No.:</strong> {safeInvoice.id}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice Month:</strong> {format(new Date(safeInvoice.issueDate), "MMMM yyyy")}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice For:</strong> {safeInvoice.invoiceType}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>HSN No.:</strong> {safeInvoice.hsnCode}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Employee Name:</strong> {safeInvoice.employeeName}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Employee Engagement Code:</strong> {safeInvoice.employeeEngagementCode}
          </div>
        </div>

        {/* Billed To */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333'
          }}>Billed To :-</h3>
          <div style={{ marginBottom: '5px' }}>
            <strong>{safeInvoice.client}</strong>
          </div>
          <div style={{ marginBottom: '5px' }}>
            {safeInvoice.project}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>GST No:</strong> {safeInvoice.gstin}
          </div>
        </div>
      </div>

      {/* Billing Table */}
      <table style={{
        width: '100%',
        borderCollapse: 'collapse',
        marginBottom: '20px',
        border: '1px solid #333'
      }}>
        <thead>
          <tr>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Employee Name</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Joining Date</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Rate</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Bill Amount</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>GST</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Total Bill Amount</th>
          </tr>
          <tr>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '2px',
              border: '1px solid #333',
              fontSize: '9px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <span>CGST @{safeInvoice.cgst}</span>
                <span>SGST @{safeInvoice.sgst}</span>
                <span>IGST @{safeInvoice.igst}</span>
              </div>
            </th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{safeInvoice.employeeName}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{format(new Date(safeInvoice.joiningDate), "dd/MM/yyyy")}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{safeInvoice.rate}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{amounts.baseAmount}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{amounts.cgstAmount}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px',
              fontWeight: 'bold'
            }}>{amounts.totalAmount}</td>
          </tr>
        </tbody>
      </table>

      {/* Net Payable */}
      <div style={{
        fontSize: '14px',
        fontWeight: 'bold',
        marginBottom: '20px',
        textAlign: 'left'
      }}>
        <strong>Net Payable: {amounts.totalAmount}</strong> (= Twenty Three Thousand Six Hundred Only)
      </div>

      {/* Payment Information and Authorized Signatory */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {/* Payment Information */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333',
            backgroundColor: '#f8f9fa',
            padding: '5px',
            textAlign: 'center',
            border: '1px solid #333'
          }}>Payment Information</h3>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '11px'
          }}>
            <tbody>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Bank Name:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.bankName}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Branch Name:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.branchName}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Account Name:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.accountName}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Account No:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.accountNo}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>IFSC Code:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.ifscCode}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Account Type:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.accountType}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>GSTIN:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.gstin}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>CIN:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.cin}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>PAN No:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.panNo}</td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Authorized Signatory */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333',
            backgroundColor: '#f8f9fa',
            padding: '5px',
            textAlign: 'center',
            border: '1px solid #333'
          }}>Authorized Signatory</h3>
          <div style={{
            height: '100px',
            border: '1px solid #333',
            display: 'flex',
            alignItems: 'flex-end',
            justifyContent: 'center',
            padding: '10px',
            fontSize: '12px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '40px' }}></div>
              <div>For RedBeryl Tech Solutions Pvt. Ltd.</div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={{
        textAlign: 'center',
        fontSize: '12px',
        color: '#333',
        borderTop: '1px solid #333',
        paddingTop: '10px',
        fontStyle: 'italic'
      }}>
        Thank you for doing business with us.
      </div>
    </div>
  );
};

export default InvoicePdfTemplate;
