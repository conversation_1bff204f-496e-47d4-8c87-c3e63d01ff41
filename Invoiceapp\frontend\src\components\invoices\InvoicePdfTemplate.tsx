import React from 'react';
import { format } from 'date-fns';
import RedBerylLogo from '../ui/RedBerylLogo';
import { getDefaultRedBerylLogoSVG } from '../../utils/logoUtils';

interface InvoicePdfTemplateProps {
  invoice: {
    id: string;
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
    // Additional fields for enhanced invoice
    employeeName?: string;
    employeeEngagementCode?: string;
    joiningDate?: string;
    rate?: string;
    billAmount?: string;
    cgst?: string;
    sgst?: string;
    igst?: string;
    netPayable?: string;
    bankName?: string;
    branchName?: string;
    accountName?: string;
    accountNo?: string;
    ifscCode?: string;
    accountType?: string;
    gstin?: string;
    cin?: string;
    panNo?: string;
    attendanceDays?: number;
  };
}

const InvoicePdfTemplate: React.FC<InvoicePdfTemplateProps> = ({ invoice }) => {
  // Ensure all required fields are present with enhanced data
  const safeInvoice = {
    ...invoice,
    client: invoice.client || "abc",
    project: invoice.project || "hadapsar",
    candidate: invoice.candidate || "prathamesh kadam",
    invoiceType: invoice.invoiceType || "Services",
    staffingType: invoice.staffingType || "Full-time",
    amount: invoice.amount || "₹200.00",
    tax: invoice.tax || "₹36.00",
    total: invoice.total || "₹236.00",
    issueDate: invoice.issueDate || "2025-05-21",
    dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
    status: invoice.status || "Draft",
    recurring: invoice.recurring || false,
    notes: invoice.notes || "",
    // Enhanced fields with defaults
    employeeName: invoice.employeeName || invoice.candidate || "prathamesh kadam",
    employeeEngagementCode: invoice.employeeEngagementCode || "ENG-0016",
    joiningDate: invoice.joiningDate || "2025-05-21",
    rate: invoice.rate || "₹200.00",
    billAmount: invoice.billAmount || "₹20,000.00",
    cgst: invoice.cgst || "9%",
    sgst: invoice.sgst || "9%",
    igst: invoice.igst || "18%",
    netPayable: invoice.netPayable || "₹23,600.00",
    bankName: invoice.bankName || "HDFC Bank",
    branchName: invoice.branchName || "MG Road Branch",
    accountName: invoice.accountName || "Acme Corporation Pvt Ltd",
    accountNo: invoice.accountNo || "***********",
    ifscCode: invoice.ifscCode || "HDFC0001234",
    accountType: invoice.accountType || "Current",
    gstin: invoice.gstin || "29**********2Z5",
    cin: invoice.cin || "U12345KA2020PTC012345",
    panNo: invoice.panNo || "**********",
    attendanceDays: invoice.attendanceDays || 20,
    hsnCode: invoice.hsnCode || "998313"
  };

  console.log("InvoicePdfTemplate: Rendering with enhanced data:", safeInvoice);

  // Calculate amounts based on attendance if available
  const calculateAmounts = () => {
    const dailyRate = parseFloat(safeInvoice.rate?.replace(/[₹,]/g, '') || '200');
    const days = safeInvoice.attendanceDays || 20;
    const baseAmount = dailyRate * days;
    const cgstAmount = baseAmount * 0.09; // 9%
    const sgstAmount = baseAmount * 0.09; // 9%
    const igstAmount = baseAmount * 0.18; // 18%
    const totalAmount = baseAmount + cgstAmount + sgstAmount;

    return {
      baseAmount: `₹${baseAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      cgstAmount: `₹${cgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      sgstAmount: `₹${sgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      igstAmount: `₹${igstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
      totalAmount: `₹${totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`
    };
  };

  const amounts = calculateAmounts();

  return (
    <div id="invoice-pdf-content" style={{
      fontFamily: 'Arial, sans-serif',
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      backgroundColor: 'white',
      color: '#333',
      fontSize: '12px',
      lineHeight: '1.4'
    }}>
      {/* Header with Logo */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        paddingBottom: '20px'
      }}>
        <div style={{ flex: 1 }}>
          <img
            src="data:image/svg+xml;base64,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"
            alt="RedBeryl Tech Solutions"
            style={{
              width: '280px',
              height: '90px',
              objectFit: 'contain',
              marginBottom: '10px'
            }}
          />
        </div>
        <div style={{
          textAlign: 'center',
          flex: 1
        }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            margin: '0',
            color: '#333',
            textDecoration: 'underline',
            letterSpacing: '2px'
          }}>INVOICE</h1>
        </div>
        <div style={{ flex: 1 }}></div>
      </div>

      {/* Invoice Details and Billed To Section */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '40px',
        marginBottom: '30px'
      }}>
        {/* Invoice Details */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333'
          }}>Invoice Details :-</h3>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice Date:</strong> {format(new Date(safeInvoice.issueDate), "dd/MM/yyyy")}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice No.:</strong> {safeInvoice.id}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice Month:</strong> {format(new Date(safeInvoice.issueDate), "MMMM yyyy")}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Invoice For:</strong> {safeInvoice.invoiceType}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>HSN No.:</strong> {safeInvoice.hsnCode}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Employee Name:</strong> {safeInvoice.employeeName}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Employee Engagement Code:</strong> {safeInvoice.employeeEngagementCode}
          </div>
        </div>

        {/* Billed To */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333'
          }}>Billed To :-</h3>
          <div style={{ marginBottom: '5px' }}>
            <strong>{safeInvoice.client}</strong>
          </div>
          <div style={{ marginBottom: '5px' }}>
            {safeInvoice.project}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>GST No:</strong> {safeInvoice.gstin}
          </div>
        </div>
      </div>

      {/* Billing Table */}
      <table style={{
        width: '100%',
        borderCollapse: 'collapse',
        marginBottom: '20px',
        border: '1px solid #333'
      }}>
        <thead>
          <tr>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Employee Name</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Joining Date</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Rate</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Bill Amount</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>GST</th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '8px',
              border: '1px solid #333',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>Total Bill Amount</th>
          </tr>
          <tr>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '2px',
              border: '1px solid #333',
              fontSize: '9px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <span>CGST @{safeInvoice.cgst}</span>
                <span>SGST @{safeInvoice.sgst}</span>
                <span>IGST @{safeInvoice.igst}</span>
              </div>
            </th>
            <th style={{
              backgroundColor: '#f8f9fa',
              textAlign: 'center',
              padding: '4px',
              border: '1px solid #333',
              fontSize: '10px'
            }}></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{safeInvoice.employeeName}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{format(new Date(safeInvoice.joiningDate), "dd/MM/yyyy")}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{safeInvoice.rate}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{amounts.baseAmount}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px'
            }}>{amounts.cgstAmount}</td>
            <td style={{
              padding: '8px',
              border: '1px solid #333',
              textAlign: 'center',
              fontSize: '11px',
              fontWeight: 'bold'
            }}>{amounts.totalAmount}</td>
          </tr>
        </tbody>
      </table>

      {/* Net Payable */}
      <div style={{
        fontSize: '14px',
        fontWeight: 'bold',
        marginBottom: '20px',
        textAlign: 'left'
      }}>
        <strong>Net Payable: {amounts.totalAmount}</strong> (= Twenty Three Thousand Six Hundred Only)
      </div>

      {/* Payment Information and Authorized Signatory */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {/* Payment Information */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333',
            backgroundColor: '#f8f9fa',
            padding: '5px',
            textAlign: 'center',
            border: '1px solid #333'
          }}>Payment Information</h3>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '11px'
          }}>
            <tbody>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Bank Name:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.bankName}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Branch Name:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.branchName}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Account Name:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.accountName}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Account No:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.accountNo}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>IFSC Code:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.ifscCode}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>Account Type:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.accountType}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>GSTIN:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.gstin}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>CIN:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.cin}</td>
              </tr>
              <tr>
                <td style={{ padding: '3px', border: '1px solid #333', fontWeight: 'bold' }}>PAN No:</td>
                <td style={{ padding: '3px', border: '1px solid #333' }}>{safeInvoice.panNo}</td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Authorized Signatory */}
        <div>
          <h3 style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#333',
            backgroundColor: '#f8f9fa',
            padding: '5px',
            textAlign: 'center',
            border: '1px solid #333'
          }}>Authorized Signatory</h3>
          <div style={{
            height: '100px',
            border: '1px solid #333',
            display: 'flex',
            alignItems: 'flex-end',
            justifyContent: 'center',
            padding: '10px',
            fontSize: '12px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '40px' }}></div>
              <div>For RedBeryl Tech Solutions Pvt. Ltd.</div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={{
        textAlign: 'center',
        fontSize: '12px',
        color: '#333',
        borderTop: '1px solid #333',
        paddingTop: '10px',
        fontStyle: 'italic'
      }}>
        Thank you for doing business with us.
      </div>
    </div>
  );
};

export default InvoicePdfTemplate;
