
import { useState, FormEvent, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Bell,
  Search,
  User,
  ChevronDown,
  LogOut,
  Settings,
  UserCircle
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";

interface HeaderProps {
  // Removed sidebar toggle functionality
}

const Header = ({}: HeaderProps) => {
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const { logout, username } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check if current page is dashboard
  const isDashboardPage = location.pathname === "/" || location.pathname === "/dashboard";

  // Reset search state when navigating away from dashboard
  useEffect(() => {
    if (!isDashboardPage) {
      setSearchQuery("");
      setDebouncedQuery("");
      setSearchOpen(false);
    }
  }, [isDashboardPage]);

  // Sample notifications data
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      title: "New Invoice",
      description: "Invoice #INV-2023-045 has been created",
      time: "10 minutes ago",
      read: false
    },
    {
      id: 2,
      title: "Payment Received",
      description: "Payment of $15,750 received from TechCorp",
      time: "2 hours ago",
      read: false
    },
    {
      id: 3,
      title: "Document Approved",
      description: "Contract for Wayne Enterprises has been approved",
      time: "Yesterday",
      read: true
    }
  ]);

  // Debounce search query
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300); // 300ms debounce delay

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  const handleLogout = () => {
    logout();
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
    navigate("/auth/login");
  };

  // Auto-search when debounced query changes
  useEffect(() => {
    if (debouncedQuery.trim() && isDashboardPage) {
      // Only search within the dashboard
      navigate(`/?search=${encodeURIComponent(debouncedQuery)}`);
    }
  }, [debouncedQuery, navigate, isDashboardPage]);

  const handleSearch = (e: FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim() && isDashboardPage) {
      // Only search within the dashboard
      toast({
        title: "Dashboard Search",
        description: `Searching dashboard for: ${searchQuery}`,
      });

      // Always navigate to dashboard with search query parameter
      navigate(`/?search=${encodeURIComponent(searchQuery)}`);

      // Close mobile search
      setSearchOpen(false);
    }
  };

  // Close search on escape key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setSearchOpen(false);
    }
  };

  // Close search when clicking outside
  const handleClickOutside = () => {
    if (searchOpen) {
      setSearchOpen(false);
    }
  };

  // Handle notification click
  const handleNotificationClick = (id: number) => {
    // Mark notification as read
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );

    toast({
      title: "Notification",
      description: "Notification marked as read",
    });

    // Navigate based on notification type (example)
    if (id === 1) {
      navigate('/invoices');
    } else if (id === 2) {
      navigate('/payments');
    } else if (id === 3) {
      navigate('/documents');
    }
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );

    toast({
      title: "Notifications",
      description: "All notifications marked as read",
    });
  };

  // Count unread notifications
  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <header className="sticky top-0 z-30 flex items-center justify-between w-full h-16 px-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center gap-2">

        {isDashboardPage && (
          <div className="hidden md:flex md:w-64 lg:w-80">
            <form onSubmit={handleSearch} className="relative w-full">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search dashboard..."
                className="w-full bg-background pl-8 md:w-64 lg:w-80"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleKeyDown}
              />
              <button type="submit" className="sr-only">Search</button>
            </form>
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        {isDashboardPage && (
          <>
            {searchOpen ? (
              <form onSubmit={handleSearch} className="relative md:hidden">
                <Input
                  type="search"
                  placeholder="Search dashboard..."
                  className="w-48 bg-background pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onBlur={handleClickOutside}
                  autoFocus
                />
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <button type="submit" className="sr-only">Search</button>
              </form>
            ) : (
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={() => setSearchOpen(true)}
              >
                <Search className="h-5 w-5" />
              </Button>
            )}
          </>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && (
                <span className="absolute top-1 right-1 flex h-3 w-3">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-3 w-3 bg-primary"></span>
                </span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel className="flex justify-between items-center">
              <span>Notifications</span>
              {unreadCount > 0 && (
                <span className="text-xs bg-primary text-primary-foreground px-2 py-0.5 rounded-full">
                  {unreadCount} new
                </span>
              )}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {notifications.length > 0 ? (
              <>
                {notifications.map((notification) => (
                  <DropdownMenuItem
                    key={notification.id}
                    className={`cursor-pointer flex flex-col items-start py-2 ${!notification.read ? 'bg-muted/50' : ''}`}
                    onClick={() => handleNotificationClick(notification.id)}
                  >
                    <div className="flex w-full justify-between">
                      <span className="font-medium">{notification.title}</span>
                      <span className="text-xs text-muted-foreground">{notification.time}</span>
                    </div>
                    <span className="text-xs text-muted-foreground mt-1">{notification.description}</span>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <div className="flex justify-between px-2 py-1.5">
                  <DropdownMenuItem
                    className="cursor-pointer text-primary text-sm"
                    onClick={markAllAsRead}
                  >
                    Mark all as read
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer text-primary text-sm"
                    onClick={() => navigate('/notifications')}
                  >
                    View all
                  </DropdownMenuItem>
                </div>
              </>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No notifications
              </div>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                <User className="h-4 w-4" />
              </div>
              <span className="hidden md:inline-flex">{username || 'User'}</span>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => navigate('/profile')} className="cursor-pointer">
              <UserCircle className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => navigate('/settings')} className="cursor-pointer">
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
              <LogOut className="mr-2 h-4 w-4" />
              <span>Logout</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
