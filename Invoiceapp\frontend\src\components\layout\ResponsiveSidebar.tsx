import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  LayoutDashboard,
  Users,
  FileText,
  CreditCard,
  Briefcase,
  UserRound,
  File,
  ChevronRight,
  ChevronDown,
  Settings,
  Hash,
  UserCog,
  UserPlus
} from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

interface MenuItem {
  icon: React.ElementType;
  label: string;
  path?: string;
  children?: {
    icon: React.ElementType;
    label: string;
    path: string;
  }[];
}

const menuItems: MenuItem[] = [
  { icon: LayoutDashboard, label: "Dashboard", path: "/" },
  { icon: Briefcase, label: "Clients & Projects", path: "/clients" },
  { icon: UserRound, label: "Candidates", path: "/candidates" },
  { icon: FileText, label: "Invoices", path: "/invoices" },
  { icon: CreditCard, label: "Payments", path: "/payments" },
  { icon: Users, label: "CRM", path: "/crm" },
  { icon: File, label: "Documents", path: "/documents" },
  { icon: UserPlus, label: "BDMs", path: "/bdms" },
  {
    icon: Settings,
    label: "Masters",
    children: [
      { icon: Settings, label: "Role Master", path: "/masters/admin" },
      { icon: Hash, label: "HSN Codes", path: "/masters/hsn-codes" },
      { icon: UserCog, label: "SPOCs", path: "/masters/spocs" },
      { icon: UserCog, label: "Staffing Types", path: "/masters/staffing-types" },
      { icon: FileText, label: "Invoice Types", path: "/masters/invoice-types" },
      { icon: CreditCard, label: "Redberyl Accounts", path: "/masters/redberyl-accounts" },
    ]
  }
];

const ResponsiveSidebar = ({ isOpen, toggleSidebar }: SidebarProps) => {
  const location = useLocation();
  const [openGroups, setOpenGroups] = useState<Record<string, boolean>>({
    Masters: true, // Default open state for Masters
  });

  const toggleGroup = (label: string) => {
    setOpenGroups(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  // Check if any child route is active
  const isGroupActive = (children: MenuItem['children']) => {
    if (!children) return false;
    return children.some(child => location.pathname === child.path);
  };

  return (
    <aside
      className={cn(
        "fixed inset-y-0 left-0 z-50 flex flex-col border-r bg-sidebar transition-all duration-300 ease-in-out overflow-hidden",
        isOpen ? "w-64" : "w-16",
        "border-sidebar-border",
        "md:block", // Always show on medium screens and up
        !isOpen && "max-md:w-0" // On mobile, completely hide when closed
      )}
    >
      <div className="flex h-16 items-center border-b border-sidebar-border px-4">
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary">
            <span className="font-bold text-primary-foreground">RB</span>
          </div>
          {isOpen && <span className="font-semibold text-sidebar-foreground">Redberyl</span>}
        </div>
        <div className="flex-1"></div>
        {isOpen && (
          <Button variant="ghost" size="icon" onClick={toggleSidebar} className="text-sidebar-foreground/80">
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-transparent">
        <nav className="grid gap-1 px-2">
          {isOpen ? (
            // Expanded sidebar view
            <>
              {menuItems.map((item, index) => (
                <React.Fragment key={item.path || `group-${index}`}>
                  {item.children ? (
                    // Group with children (like Masters)
                    <Collapsible
                      open={openGroups[item.label]}
                      onOpenChange={() => toggleGroup(item.label)}
                      className={cn(
                        "w-full",
                        isGroupActive(item.children) && "bg-sidebar-accent/20 rounded-md"
                      )}
                    >
                      <CollapsibleTrigger asChild>
                        <button
                          className="flex items-center justify-between w-full rounded-md px-3 py-2 text-sm text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                        >
                          <div className="flex items-center gap-3">
                            <item.icon className="h-4 w-4" />
                            <span>{item.label}</span>
                          </div>
                          <ChevronDown className={cn("h-4 w-4 transition-transform", openGroups[item.label] && "transform rotate-180")} />
                        </button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="pl-9 pr-2 py-1 space-y-1">
                        {item.children.map((child) => (
                          <NavLink
                            key={child.path}
                            to={child.path}
                            className={({ isActive }) =>
                              cn(
                                "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                                isActive
                                  ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                                  : "text-sidebar-foreground/80"
                              )
                            }
                            onClick={(e) => {
                              // On mobile, close the sidebar after clicking a link
                              if (window.innerWidth < 768) {
                                e.preventDefault();
                                toggleSidebar();
                                setTimeout(() => {
                                  window.location.href = child.path;
                                }, 300);
                              }
                            }}
                          >
                            <child.icon className="h-4 w-4" />
                            <span className="truncate">{child.label}</span>
                          </NavLink>
                        ))}
                      </CollapsibleContent>
                    </Collapsible>
                  ) : (
                    // Regular menu item
                    <NavLink
                      to={item.path || '/'}
                      className={({ isActive }) =>
                        cn(
                          "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                          isActive
                            ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                            : "text-sidebar-foreground/80"
                        )
                      }
                      onClick={(e) => {
                        // On mobile, close the sidebar after clicking a link
                        if (window.innerWidth < 768) {
                          e.preventDefault();
                          toggleSidebar();
                          setTimeout(() => {
                            window.location.href = item.path || '/';
                          }, 300);
                        }
                      }}
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.label}</span>
                    </NavLink>
                  )}
                </React.Fragment>
              ))}
            </>
          ) : (
            // Collapsed sidebar view (icons only)
            <>
              {menuItems.map((item, index) => (
                <div key={item.path || `group-${index}`} className="relative group">
                  {item.children ? (
                    // Group with children (like Masters)
                    <>
                      <button
                        className={cn(
                          "flex justify-center rounded-md p-2 w-full text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                          isGroupActive(item.children) && "bg-sidebar-accent text-sidebar-accent-foreground"
                        )}
                        onClick={() => toggleGroup(item.label)}
                      >
                        <item.icon className="h-5 w-5" />
                      </button>

                      {/* Tooltip for collapsed menu - only show on non-touch devices */}
                      <div className="absolute left-full top-0 ml-2 hidden group-hover:block z-50 max-md:!hidden">
                        <div className="bg-popover text-popover-foreground shadow-md rounded-md py-2 px-2 min-w-[180px]">
                          <div className="font-medium px-2 py-1 text-sm">{item.label}</div>
                          <div className="mt-1 space-y-1">
                            {item.children.map((child) => (
                              <NavLink
                                key={child.path}
                                to={child.path}
                                className={({ isActive }) =>
                                  cn(
                                    "flex items-center gap-2 rounded-md px-2 py-1 text-sm hover:bg-accent",
                                    isActive ? "bg-accent text-accent-foreground" : ""
                                  )
                                }
                                onClick={(e) => {
                                  // On mobile, close the sidebar after clicking a link
                                  if (window.innerWidth < 768) {
                                    e.preventDefault();
                                    toggleSidebar();
                                    setTimeout(() => {
                                      window.location.href = child.path;
                                    }, 300);
                                  }
                                }}
                              >
                                <child.icon className="h-4 w-4" />
                                <span>{child.label}</span>
                              </NavLink>
                            ))}
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    // Regular menu item
                    <>
                      <NavLink
                        to={item.path || '/'}
                        className={({isActive}) => cn(
                          "flex justify-center rounded-md p-2 text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                          isActive && "bg-sidebar-accent text-sidebar-accent-foreground"
                        )}
                        onClick={(e) => {
                          // On mobile, close the sidebar after clicking a link
                          if (window.innerWidth < 768) {
                            e.preventDefault();
                            toggleSidebar();
                            setTimeout(() => {
                              window.location.href = item.path || '/';
                            }, 300);
                          }
                        }}
                      >
                        <item.icon className="h-5 w-5" />
                      </NavLink>

                      {/* Tooltip for collapsed menu - only show on non-touch devices */}
                      <div className="absolute left-full top-0 ml-2 hidden group-hover:block z-50 max-md:!hidden">
                        <div className="bg-popover text-popover-foreground shadow-md rounded-md py-1 px-2">
                          <span className="text-sm">{item.label}</span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </>
          )}
        </nav>
      </div>
    </aside>
  );
};

export default ResponsiveSidebar;
