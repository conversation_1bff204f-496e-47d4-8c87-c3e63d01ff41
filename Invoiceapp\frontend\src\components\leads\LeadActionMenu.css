.action-menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 3px 0;
  user-select: none;
  position: relative;
  z-index: 10;
}

.action-menu-item:hover,
.action-menu-item:focus {
  background-color: rgba(0, 0, 0, 0.05);
  outline: none;
  transform: translateY(-1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.action-menu-item:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  box-shadow: none;
}

.action-menu-item-icon {
  margin-right: 10px;
  flex-shrink: 0;
}

.action-menu-item-text {
  flex-grow: 1;
  font-weight: 500;
}

.action-menu-item-edit {
  color: #0284c7; /* blue-600 */
}

.action-menu-item-edit:hover {
  background-color: rgba(2, 132, 199, 0.1);
}

.action-menu-item-convert {
  color: #0891b2; /* cyan-600 */
}

.action-menu-item-convert:hover {
  background-color: rgba(8, 145, 178, 0.1);
}

.action-menu-item-delete {
  color: #e11d48; /* red-600 */
}

.action-menu-item-delete:hover {
  background-color: rgba(225, 29, 72, 0.1);
}
