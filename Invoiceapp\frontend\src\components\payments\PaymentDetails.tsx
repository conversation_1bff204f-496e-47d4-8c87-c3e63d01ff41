import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { format } from "date-fns";
import {
  Calendar,
  CreditCard,
  Building,
  FileText,
  DollarSign,
  CheckCircle,
  Clock,
  Landmark
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

export interface PaymentData {
  id: string;
  invoiceId: string;
  client: string;
  amount: string;
  date: string;
  method: string;
  status: string;
  notes?: string;
}

interface PaymentDetailsProps {
  payment: PaymentData | null;
  open: boolean;
  onClose: () => void;
}

const PaymentDetails: React.FC<PaymentDetailsProps> = ({
  payment,
  open,
  onClose,
}) => {
  if (!payment) return null;

  const getMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case "bank transfer":
        return <Landmark className="h-4 w-4 mr-2" />;
      case "credit card":
        return <CreditCard className="h-4 w-4 mr-2" />;
      case "check":
        return <FileText className="h-4 w-4 mr-2" />;
      default:
        return <DollarSign className="h-4 w-4 mr-2" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case "pending":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      default:
        return (
          <Badge>
            {status}
          </Badge>
        );
    }
  };

  console.log("Rendering PaymentDetails with payment:", payment, "open:", open);

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      console.log("Dialog onOpenChange:", isOpen);
      if (!isOpen) onClose();
    }}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="text-xl">Payment Details</DialogTitle>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Payment ID and Status */}
          <div className="flex justify-between items-center">
            <div className="font-semibold text-lg">{payment.id}</div>
            {getStatusBadge(payment.status)}
          </div>

          {/* Payment Details */}
          <div className="grid gap-4">
            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <span className="text-muted-foreground">Amount:</span>
              <span className="font-medium text-lg">{payment.amount}</span>
            </div>

            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <span className="text-muted-foreground">Date:</span>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{format(new Date(payment.date), "MMMM d, yyyy")}</span>
              </div>
            </div>

            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <span className="text-muted-foreground">Method:</span>
              <div className="flex items-center">
                {getMethodIcon(payment.method)}
                <span>{payment.method}</span>
              </div>
            </div>

            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <span className="text-muted-foreground">Client:</span>
              <div className="flex items-center">
                <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{payment.client}</span>
              </div>
            </div>

            <div className="grid grid-cols-[120px_1fr] items-center gap-2">
              <span className="text-muted-foreground">Invoice:</span>
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{payment.invoiceId}</span>
              </div>
            </div>

            {payment.notes && (
              <div className="mt-2">
                <span className="text-muted-foreground block mb-1">Notes:</span>
                <div className="p-3 bg-muted rounded-md text-sm">
                  {payment.notes}
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentDetails;
