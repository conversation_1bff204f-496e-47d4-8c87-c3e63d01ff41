import React from 'react';

interface RedBerylLogoProps {
  width?: number;
  height?: number;
  className?: string;
}

const RedBerylLogo: React.FC<RedBerylLogoProps> = ({ 
  width = 200, 
  height = 80, 
  className = "" 
}) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 400 160" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Background */}
      <rect width="400" height="160" fill="white"/>
      
      {/* Cloud-like shapes representing the logo design */}
      <g transform="translate(20, 30)">
        {/* Left cloud shape - Blue */}
        <path 
          d="M20 40 C10 40, 0 50, 0 60 C0 70, 10 80, 20 80 L60 80 C70 80, 80 70, 80 60 C80 50, 70 40, 60 40 L20 40 Z" 
          fill="#3366CC"
        />
        
        {/* Right cloud shape - Pink/Red */}
        <path 
          d="M50 20 C40 20, 30 30, 30 40 C30 50, 40 60, 50 60 L90 60 C100 60, 110 50, 110 40 C110 30, 100 20, 90 20 L50 20 Z" 
          fill="#E91E63"
        />
        
        {/* Connecting element */}
        <circle cx="55" cy="50" r="8" fill="#666"/>
      </g>
      
      {/* RedBeryl Text */}
      <g transform="translate(140, 30)">
        <text x="0" y="30" fontFamily="Arial, sans-serif" fontSize="32" fontWeight="bold" fill="#E91E63">Red</text>
        <text x="70" y="30" fontFamily="Arial, sans-serif" fontSize="32" fontWeight="bold" fill="#3366CC">Beryl</text>
        
        {/* TECH SOLUTIONS */}
        <text x="0" y="55" fontFamily="Arial, sans-serif" fontSize="14" fontWeight="normal" fill="#666" letterSpacing="2px">TECH SOLUTIONS</text>
        
        {/* Tagline */}
        <text x="0" y="75" fontFamily="Arial, sans-serif" fontSize="11" fill="#999">Integrates Business With Technology</text>
      </g>
    </svg>
  );
};

export default RedBerylLogo;
