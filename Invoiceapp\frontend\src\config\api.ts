// API configuration
import { getApiBaseUrl } from '@/utils/ipUtils';

// Use dynamic hostname instead of hardcoded localhost
export const API_BASE_URL = `${getApiBaseUrl()}/api`;

// Authentication configuration
export const AUTH_CONFIG = {
  username: 'admin',
  password: 'admin123'
};

// Default headers for all requests
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};
