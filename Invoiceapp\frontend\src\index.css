
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Import custom styles */
@import './styles/tabs-custom.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 226 70% 55.9%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 217 91% 60%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 220 70% 13%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 28% 17%;
    --sidebar-ring: 224 76% 48%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --sidebar-background: 220 70% 13%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 28% 17%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Fix for dropdown menus */
  [data-radix-popper-content-wrapper] {
    position: fixed !important;
    z-index: 9999 !important;
    pointer-events: auto !important;
  }

  /* Ensure dropdown items are visible */
  [data-radix-select-content] {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    overflow: visible !important;
  }

  /* Ensure dropdown items are clickable */
  [data-radix-select-item] {
    cursor: pointer !important;
    user-select: none !important;
  }

  /* Ensure dropdown viewport is visible */
  [data-radix-select-viewport] {
    padding: 4px !important;
    background-color: white !important;
  }
}

.kanban-column {
  @apply min-h-[500px] w-full max-w-[350px] rounded-lg bg-secondary p-4;
}

.kanban-card {
  @apply mb-3 cursor-grab rounded-md border border-border bg-card p-3 shadow-sm;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--secondary));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Custom sidebar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-400 {
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scrollbar-track-transparent {
  scrollbar-track-color: transparent;
}

/* Responsive sidebar adjustments */
@media (max-width: 768px) {
  .sidebar-collapsed {
    width: 0;
    overflow: hidden;
  }
}
