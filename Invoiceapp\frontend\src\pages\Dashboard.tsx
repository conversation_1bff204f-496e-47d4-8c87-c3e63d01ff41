
import { AreaChart, BadgeDollarSign, BarChart3, ClipboardListIcon, FileText, Settings, Users, Search, RefreshCw } from "lucide-react";
import StatCard from "@/components/dashboard/StatCard";
import RevenueChart from "@/components/dashboard/RevenueChart";
import TopClientsChart from "@/components/dashboard/TopClientsChart";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDashboardMetrics } from "@/hooks/useDashboardMetrics";
import { Skeleton } from "@/components/ui/skeleton";

const Dashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);

  // Fetch dashboard metrics
  const { metrics, loading, error, refetch } = useDashboardMetrics();

  // Parse search query from URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const query = params.get("search");

    if (query) {
      setSearchQuery(query);
      performDashboardSearch(query);
    } else {
      setSearchQuery("");
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [location.search]);

  // Function to perform search within dashboard data
  const performDashboardSearch = (query: string) => {
    setIsSearching(true);
    const lowerQuery = query.toLowerCase();

    // Search in dashboard data
    const results = [];

    // Search in stat cards
    if (lowerQuery.includes("total") ||
        lowerQuery.includes("invoice") ||
        lowerQuery.includes("243") ||
        lowerQuery.includes("pending approval") ||
        lowerQuery.includes("12%")) {
      results.push({
        type: "statCard",
        title: "Total Invoices",
        description: "243 invoices, 32 pending approval",
        trend: "+12% from last month",
        icon: <FileText className="h-5 w-5 text-primary" />,
        onClick: () => navigate('/invoices')
      });
    }

    if (lowerQuery.includes("total") ||
        lowerQuery.includes("payment") ||
        lowerQuery.includes("285,324") ||
        lowerQuery.includes("pending payments") ||
        lowerQuery.includes("4%")) {
      results.push({
        type: "statCard",
        title: "Total Payments",
        description: "₹285,324 total, 14 pending payments",
        trend: "+4% from last month",
        icon: <BadgeDollarSign className="h-5 w-5 text-primary" />,
        onClick: () => navigate('/payments')
      });
    }

    if (lowerQuery.includes("active") ||
        lowerQuery.includes("client") ||
        lowerQuery.includes("45") ||
        lowerQuery.includes("new this month") ||
        lowerQuery.includes("5%")) {
      results.push({
        type: "statCard",
        title: "Active Clients",
        description: "45 clients, 3 new this month",
        trend: "+5% from last month",
        icon: <Users className="h-5 w-5 text-primary" />,
        onClick: () => navigate('/clients')
      });
    }

    if (lowerQuery.includes("document") ||
        lowerQuery.includes("128") ||
        lowerQuery.includes("awaiting approval") ||
        lowerQuery.includes("-4%") ||
        lowerQuery.includes("4%")) {
      results.push({
        type: "statCard",
        title: "Documents",
        description: "128 documents, 12 awaiting approval",
        trend: "-4% from last month",
        icon: <ClipboardListIcon className="h-5 w-5 text-primary" />,
        onClick: () => navigate('/documents')
      });
    }

    // Search in recent activities
    if (lowerQuery.includes("invoice") ||
        lowerQuery.includes("inv-2023-042") ||
        lowerQuery.includes("acme") ||
        lowerQuery.includes("corporation") ||
        lowerQuery.includes("12,500")) {
      results.push({
        type: "activity",
        title: "Invoice #INV-2023-042 created",
        description: "Acme Corporation - $12,500",
        time: "2h ago",
        icon: <FileText className="h-5 w-5 text-primary" />,
        onClick: handleInvoiceClick
      });
    }

    if (lowerQuery.includes("payment") ||
        lowerQuery.includes("received") ||
        lowerQuery.includes("globex") ||
        lowerQuery.includes("inc") ||
        lowerQuery.includes("24,000")) {
      results.push({
        type: "activity",
        title: "Payment received",
        description: "Globex Inc. - $24,000",
        time: "5h ago",
        icon: <BadgeDollarSign className="h-5 w-5 text-primary" />,
        onClick: handlePaymentClick
      });
    }

    if (lowerQuery.includes("new") ||
        lowerQuery.includes("client") ||
        lowerQuery.includes("added") ||
        lowerQuery.includes("wayne") ||
        lowerQuery.includes("enterprises")) {
      results.push({
        type: "activity",
        title: "New client added",
        description: "Wayne Enterprises",
        time: "1d ago",
        icon: <Users className="h-5 w-5 text-primary" />,
        onClick: handleClientClick
      });
    }

    // Search in performance metrics
    if (lowerQuery.includes("performance") ||
        lowerQuery.includes("metric") ||
        lowerQuery.includes("revenue growth") ||
        lowerQuery.includes("new clients") ||
        lowerQuery.includes("payment rate") ||
        lowerQuery.includes("document processing")) {
      results.push({
        type: "metrics",
        title: "Performance Metrics",
        description: "Monthly key performance indicators",
        icon: <BarChart3 className="h-5 w-5 text-primary" />,
        onClick: () => {}
      });
    }

    // Search in revenue overview
    if (lowerQuery.includes("revenue") ||
        lowerQuery.includes("overview") ||
        lowerQuery.includes("monthly") ||
        lowerQuery.includes("performance")) {
      results.push({
        type: "chart",
        title: "Revenue Overview",
        description: "Monthly revenue performance",
        icon: <AreaChart className="h-5 w-5 text-primary" />,
        onClick: () => {}
      });
    }

    // Search in top clients
    if (lowerQuery.includes("top") ||
        lowerQuery.includes("client") ||
        lowerQuery.includes("revenue") ||
        lowerQuery.includes("contribution")) {
      results.push({
        type: "chart",
        title: "Top Clients",
        description: "Clients by revenue contribution",
        icon: <Users className="h-5 w-5 text-primary" />,
        onClick: () => navigate('/clients')
      });
    }

    setSearchResults(results);
  };

  // Handler functions for navigation
  const handleInvoiceClick = () => navigate('/invoices');
  const handlePaymentClick = () => navigate('/payments');
  const handleClientClick = () => navigate('/clients');

  // Clear search
  const clearSearch = () => {
    navigate('/');
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">Overview of your business metrics and performance.</p>
        </div>
        {searchQuery && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearSearch}
            className="flex items-center gap-1"
          >
            Clear Search
          </Button>
        )}
      </div>

      {/* Search Results */}
      {searchQuery && (
        <div className="rounded-xl border bg-card text-card-foreground shadow p-6">
          <div className="flex items-center gap-2 mb-4">
            <Search className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Search Results for "{searchQuery}"</h3>
          </div>

          {searchResults.length > 0 ? (
            <div className="space-y-4">
              {searchResults.map((result, index) => (
                <div
                  key={index}
                  className="flex items-center gap-4 rounded-lg border p-3 cursor-pointer hover:bg-muted/50 transition-colors relative group"
                  onClick={result.onClick}
                >
                  <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                    {result.icon}
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium group-hover:text-primary transition-colors">{result.title}</p>
                    <p className="text-xs text-muted-foreground">{result.description}</p>
                    {result.trend && (
                      <p className="text-xs text-green-500">{result.trend}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    {result.time && (
                      <div className="text-xs text-muted-foreground">{result.time}</div>
                    )}
                    <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                      {result.type === "statCard" || result.type === "activity" ? "View →" : "Details →"}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Alert>
              <Search className="h-4 w-4" />
              <AlertTitle>No results found</AlertTitle>
              <AlertDescription>
                No dashboard items match your search query. Try a different search term.
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Refresh button */}
        {!loading && (
          <div className="absolute top-24 right-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        )}

        {loading ? (
          // Loading skeletons
          <>
            <Skeleton className="h-[140px] w-full rounded-xl" />
            <Skeleton className="h-[140px] w-full rounded-xl" />
            <Skeleton className="h-[140px] w-full rounded-xl" />
            <Skeleton className="h-[140px] w-full rounded-xl" />
          </>
        ) : error ? (
          // Error state
          <div className="col-span-4">
            <Alert variant="destructive">
              <AlertTitle>Error loading dashboard data</AlertTitle>
              <AlertDescription>
                {error.message}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetch()}
                  className="ml-2"
                >
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          // Data loaded successfully
          <>
            <StatCard
              title="Total Invoices"
              value={metrics?.totalInvoices.count.toString() || "243"}
              icon={<FileText className="h-4 w-4" />}
              trend={{
                value: metrics?.totalInvoices.trend || 12,
                isPositive: (metrics?.totalInvoices.trend || 0) >= 0
              }}
              description={`${metrics?.totalInvoices.pendingApproval || 32} pending approval`}
              linkTo="/invoices"
            />
            <StatCard
              title="Total Payments"
              value={metrics?.totalPayments.amount ?
                new Intl.NumberFormat('en-IN', {
                  style: 'currency',
                  currency: 'INR',
                  maximumFractionDigits: 0
                }).format(metrics.totalPayments.amount) :
                "₹285,324"
              }
              icon={<BadgeDollarSign className="h-4 w-4" />}
              trend={{
                value: Math.abs(metrics?.totalPayments.trend || 4),
                isPositive: (metrics?.totalPayments.trend || 0) >= 0
              }}
              description={`${metrics?.totalPayments.pendingPayments || 14} pending payments`}
              linkTo="/payments"
            />
            <StatCard
              title="Active Clients"
              value={metrics?.activeClients.count.toString() || "45"}
              icon={<Users className="h-4 w-4" />}
              trend={{
                value: Math.abs(metrics?.activeClients.trend || 5),
                isPositive: (metrics?.activeClients.trend || 0) >= 0
              }}
              description={`${metrics?.activeClients.newThisMonth || 3} new this month`}
              linkTo="/clients"
            />
            <StatCard
              title="Documents"
              value={metrics?.documents.count.toString() || "128"}
              icon={<ClipboardListIcon className="h-4 w-4" />}
              trend={{
                value: Math.abs(metrics?.documents.trend || 4),
                isPositive: (metrics?.documents.trend || 0) >= 0
              }}
              description={`${metrics?.documents.awaitingApproval || 12} awaiting approval`}
              linkTo="/documents"
            />
          </>
        )}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <RevenueChart />
        <TopClientsChart />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="md:col-span-2">
          <div className="rounded-xl border bg-card text-card-foreground shadow p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Recent Activities</h3>
                <p className="text-sm text-muted-foreground">Your latest system activities</p>
              </div>
            </div>
            <div className="mt-4 space-y-4">
              <div
                className="flex items-center gap-4 rounded-lg border p-3 cursor-pointer hover:bg-muted/50 transition-colors relative group"
                onClick={handleInvoiceClick}
              >
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                  <FileText className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium group-hover:text-primary transition-colors">Invoice #INV-2023-042 created</p>
                  <p className="text-xs text-muted-foreground">Acme Corporation - $12,500</p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="text-xs text-muted-foreground">2h ago</div>
                  <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">View →</span>
                </div>
              </div>
              <div
                className="flex items-center gap-4 rounded-lg border p-3 cursor-pointer hover:bg-muted/50 transition-colors relative group"
                onClick={handlePaymentClick}
              >
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                  <BadgeDollarSign className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium group-hover:text-primary transition-colors">Payment received</p>
                  <p className="text-xs text-muted-foreground">Globex Inc. - $24,000</p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="text-xs text-muted-foreground">5h ago</div>
                  <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">View →</span>
                </div>
              </div>
              <div
                className="flex items-center gap-4 rounded-lg border p-3 cursor-pointer hover:bg-muted/50 transition-colors relative group"
                onClick={handleClientClick}
              >
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium group-hover:text-primary transition-colors">New client added</p>
                  <p className="text-xs text-muted-foreground">Wayne Enterprises</p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="text-xs text-muted-foreground">1d ago</div>
                  <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">View →</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="md:col-span-2">
          <div className="rounded-xl border bg-card text-card-foreground shadow p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Performance Metrics</h3>
                <p className="text-sm text-muted-foreground">Monthly key performance indicators</p>
              </div>
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
              </div>
            </div>
            <div className="mt-4 space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">Revenue Growth</p>
                  <p className="text-sm font-bold text-green-500">+18%</p>
                </div>
                <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
                  <div className="h-full w-[70%] rounded-full bg-primary"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">New Clients</p>
                  <p className="text-sm font-bold text-green-500">+12%</p>
                </div>
                <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
                  <div className="h-full w-[65%] rounded-full bg-primary"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">Payment Rate</p>
                  <p className="text-sm font-bold text-yellow-500">+2%</p>
                </div>
                <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
                  <div className="h-full w-[85%] rounded-full bg-primary"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">Document Processing</p>
                  <p className="text-sm font-bold text-red-500">-5%</p>
                </div>
                <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
                  <div className="h-full w-[45%] rounded-full bg-primary"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
