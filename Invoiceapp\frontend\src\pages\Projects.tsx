import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2, Plus, RefreshCcw, Search } from "lucide-react";
import { useEntityData } from "@/hooks/useEntityData";
import StatusDropdown from "@/components/common/StatusDropdown";
import ActionMenuSimple from "@/components/common/ActionMenuSimple";
import DynamicProjectFormDialog from "@/components/projects/DynamicProjectFormDialog";
import { toast } from "sonner";
import { projectService } from "@/services/projectService";
import { directApiService } from "@/services/directApiService";
import { useTabClickHandler } from "@/hooks/useTabClickHandler";

// Define types for better type safety
interface Project {
  id: string | number;
  name: string;
  client: { id: string | number; name: string } | string;
  clientId?: string | number;
  clientName?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  value?: string | number;
}

const Projects = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isProjectDialogOpen, setIsProjectDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);

  // Add state for direct API fetching
  const [directProjects, setDirectProjects] = useState<Project[]>([]);
  const [directLoading, setDirectLoading] = useState<boolean>(true);
  const [directError, setDirectError] = useState<Error | null>(null);

  // Use the tab click handler hook
  const { fetchProjectsData } = useTabClickHandler();

  // Call fetchProjectsData when the component mounts
  useEffect(() => {
    console.log("Projects component mounted, fetching data from specific endpoint...");
    fetchProjectsData();
  }, []);

  // Function to fetch projects directly from the API
  const fetchProjectsDirectly = async () => {
    setDirectLoading(true);
    setDirectError(null);

    try {
      console.log("Fetching projects directly from API...");

      // First try the specific endpoint - http://localhost:8080/projects/getAll
      try {
        console.log("Trying specific endpoint first: http://localhost:8080/projects/getAll");

        // Make a direct fetch to the specific endpoint
        const response = await fetch('http://localhost:8080/projects/getAll', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': 'Basic ' + btoa('admin:admin123')
          },
          credentials: 'omit'
        });

        // Log the raw response for debugging
        console.log("Raw response:", response);

        if (!response.ok) {
          console.error(`Specific endpoint returned status ${response.status}`);
          throw new Error(`Failed to fetch projects: ${response.status}`);
        }

        const text = await response.text();
        console.log("Raw response text:", text);

        let data;
        try {
          data = JSON.parse(text);
          console.log("Projects fetched from specific endpoint:", data);
        } catch (parseError) {
          console.error("Error parsing JSON:", parseError);
          console.log("Invalid JSON response:", text);
          throw new Error("Invalid JSON response");
        }

        // Log the exact structure of the response to help with debugging
        console.log("Response type:", typeof data);
        console.log("Response keys:", data ? Object.keys(data) : "No keys");

        // Handle different response formats
        let projectsArray = [];

        if (Array.isArray(data)) {
          console.log("Data is an array with length:", data.length);
          projectsArray = data;
        } else if (data && typeof data === 'object') {
          // Check for common response formats
          if (Array.isArray(data.content)) {
            console.log("Data has content array with length:", data.content.length);
            projectsArray = data.content;
          } else if (Array.isArray(data.data)) {
            console.log("Data has data array with length:", data.data.length);
            projectsArray = data.data;
          } else if (data.data && Array.isArray(data.data.content)) {
            console.log("Data has nested content array with length:", data.data.content.length);
            projectsArray = data.data.content;
          } else if (data.projects && Array.isArray(data.projects)) {
            console.log("Data has projects array with length:", data.projects.length);
            projectsArray = data.projects;
          } else if (data.items && Array.isArray(data.items)) {
            console.log("Data has items array with length:", data.items.length);
            projectsArray = data.items;
          } else {
            // If it's an object but not in a recognized format, try to convert it to an array
            console.log("Data is an object but not in a recognized format");
            const singleProject = data;
            projectsArray = [singleProject];
          }
        }

        // If we have real data from the API, use it
        if (projectsArray.length > 0) {
          console.log("Using real data from API with length:", projectsArray.length);
          console.log("Raw API data:", JSON.stringify(projectsArray, null, 2));

          // Map the projects to the expected format
          const mappedProjects = projectsArray.map(project => {
            // Log each project to see its structure
            console.log("Mapping project:", JSON.stringify(project, null, 2));

            // Create a properly formatted project object
            return {
              id: project.id || `api-${Math.random().toString(36).substring(2, 9)}`,
              name: project.name || "Unnamed Project",
              client: typeof project.client === 'object' ? project.client :
                     project.clientId ? { id: project.clientId, name: project.clientName || `Client ${project.clientId}` } :
                     { id: 0, name: "Unknown Client" },
              description: project.description || "",
              startDate: project.startDate || new Date().toISOString(),
              endDate: project.endDate || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
              status: project.status || "Not Started",
              value: project.value ? (project.value.toString().startsWith('$') ? project.value : `$${project.value}`) : "$0.00"
            };
          });

          console.log("Mapped projects from API:", JSON.stringify(mappedProjects, null, 2));

          // IMPORTANT: Use ONLY the real API data, not combined with sample data
          setDirectProjects(mappedProjects);
          setDirectLoading(false);
          return; // Exit early if successful
        } else {
          console.warn("No projects found in API response, using sample data as fallback");
          // Only use sample data if API returned no results
          // setDirectProjects(sampleProjectData);
          setDirectLoading(false);
          return;
        }
      } catch (specificEndpointError) {
        console.error("Error fetching from specific endpoint:", specificEndpointError);
        // Continue to fallback approach
      }

      // Try the service method that uses the specific endpoint
      try {
        console.log("Trying service method with specific endpoint...");
        const projects = await directApiService.getProjectsFromSpecificEndpoint();
        console.log("Projects fetched from specific endpoint via service:", projects);

        if (Array.isArray(projects) && projects.length > 0) {
          setDirectProjects(projects);
          setDirectLoading(false);
          return; // Exit early if successful
        } else {
          console.warn("Service method returned empty or non-array result:", projects);
        }
      } catch (serviceError) {
        console.error("Error fetching from service method:", serviceError);
        // Continue to fallback approach
      }

      // Fallback to the general approach
      console.log("Falling back to general approach...");
      const projects = await directApiService.getAllProjects();
      console.log("Projects fetched from fallback endpoints:", projects);

      if (Array.isArray(projects) && projects.length > 0) {
        setDirectProjects(projects);
      } else {
        console.warn("Fallback API calls returned empty or non-array result:", projects);
      }

      setDirectLoading(false);
    } catch (error) {
      console.error("Error fetching projects directly:", error);
      setDirectError(error instanceof Error ? error : new Error(String(error)));
      setDirectLoading(false);
    }
  };

  // Fetch projects directly on component mount
  useEffect(() => {
    fetchProjectsDirectly();
  }, []);

  // Fetch clients data for the project form
  const {
    data: clientsData,
    loading: clientsLoading,
    error: clientsError
  } = useEntityData({
    entityType: 'clients',
    useMockData: false,
    cacheTime: 3600000, // Cache for 1 hour
    refetchInterval: 0
  });

  // Update filtered projects when directProjects changes
  useEffect(() => {
    console.log("Projects page: data updated", {
      directProjects: directProjects?.length,
      directLoading
    });

    // Log the projects data to help with debugging
    console.log("Projects data to be displayed:", directProjects);

    // Only map if we have projects to map
    if (directProjects && directProjects.length > 0) {
      // Apply search filter if needed
      if (searchTerm) {
        const filtered = directProjects.filter(
          (project) =>
            project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (typeof project.client === 'string'
              ? project.client.toLowerCase().includes(searchTerm.toLowerCase())
              : (project.client?.name || '').toLowerCase().includes(searchTerm.toLowerCase())
            )
        );

        // Use filtered results
        setFilteredProjects(filtered);
      } else {
        // Use all projects
        setFilteredProjects(directProjects);
      }
    } else {
      // No projects available
      setFilteredProjects([]);
    }

    // Force loading to false to ensure UI updates
    setDirectLoading(false);
  }, [directProjects, searchTerm, directLoading]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);
  };

  const handleAddProject = () => {
    // Get a valid client ID from the database or use a default
    const validClientId = clientsData && clientsData.length > 0 ? clientsData[0].id : 1;

    // Create a new project with default values matching the ProjectDto structure
    const newProject = {
      name: "New Project",
      clientId: validClientId,
      description: "Project description",
      email: "<EMAIL>",
      phone: "1234567890",
      status: "Not Started",
      value: "10000",
      // Add required fields based on the ProjectDto structure
      hsnCodeId: 1,
      engagementCode: "ENG-" + Math.floor(Math.random() * 1000),
      clientPartnerName: "John Doe",
      clientPartnerEmail: "<EMAIL>",
      clientPartnerPhone: "1234567890",
      bdmId: 1,
      commissionPercentage: 10,
      commissionAmount: 1000
    };

    setSelectedProject(newProject);
    setIsProjectDialogOpen(true);
  };

  // Function to create a sample project directly in the database
  const createSampleProject = async () => {
    try {
      const loadingToast = toast.loading("Creating sample project...");

      // Get a valid client ID from the database or use a default
      const validClientId = clientsData && clientsData.length > 0 ? clientsData[0].id : 1;

      // Create a properly structured project payload
      const sampleProject = {
        name: "Sample Project " + new Date().toLocaleTimeString(),
        clientId: validClientId,
        description: "This is a sample project created for testing",
        email: "<EMAIL>",
        phone: "9876543210",
        status: "Not Started",
        value: "5000",
        // Add required fields based on the ProjectDto structure
        hsnCodeId: 1,
        engagementCode: "ENG-" + Math.floor(Math.random() * 1000),
        clientPartnerName: "John Doe",
        clientPartnerEmail: "<EMAIL>",
        clientPartnerPhone: "1234567890",
        bdmId: 1,
        commissionPercentage: 10,
        commissionAmount: 500
      };

      console.log("Creating sample project with payload:", sampleProject);

      // Try to create project using both services
      try {
        // First try with direct API service
        const savedProject = await directApiService.createProject(sampleProject);
        console.log("Sample project created with direct API:", savedProject);

        toast.success("Sample project created successfully", {
          id: loadingToast
        });
      } catch (directError) {
        console.error("Error creating sample project with direct API:", directError);

        // Fall back to project service
        try {
          const savedProject = await projectService.createProject(sampleProject);
          console.log("Sample project created with project service:", savedProject);

          toast.success("Sample project created successfully", {
            id: loadingToast
          });
        } catch (serviceError) {
          console.error("Error creating sample project with project service:", serviceError);
          throw serviceError;
        }
      }

      // Refresh the projects list using both methods
      refetchProjects();
      fetchProjectsDirectly();
    } catch (error) {
      console.error("Error creating sample project:", error);
      toast.error("Failed to create sample project", {
        description: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  const handleEditProject = (id: string | number) => {
    // Find the project to edit
    const project = directProjects.find(p => p.id === id);
    if (project) {
      console.log("Editing project:", project);
      setSelectedProject(project);
      setIsProjectDialogOpen(true);
      toast.success(`Editing project: ${project.name}`);
    }
  };

  const handleViewInvoices = (id: string | number) => {
    toast.success(`Viewing invoices for project with ID: ${id}`);
    // In a real app, you would navigate to the invoices page for this project
  };

  const handleDeleteProject = (id: string | number) => {
    // Remove project from the list
    const updatedProjects = filteredProjects.filter(project => project.id !== id);
    setFilteredProjects(updatedProjects);

    toast.success(`Project with ID: ${id} deleted successfully`);
    // In a real app, you would delete the project from the database
  };

  const handleStatusChange = (projectId: string | number, newStatus: string) => {
    // Update the project status in the filtered projects list
    const updatedProjects = filteredProjects.map(project =>
      project.id === projectId ? { ...project, status: newStatus } : project
    );
    setFilteredProjects(updatedProjects);

    // Show success message
    toast.success(`Project status updated to: ${newStatus}`);

    // In a real app, you would update the project status in the database
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Projects</h2>
        <p className="text-muted-foreground">Manage your projects.</p>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search projects..."
            className="pl-8 w-full md:w-[300px]"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchProjectsDirectly}>
            Refresh Data
          </Button>
          <Button variant="outline" onClick={createSampleProject}>
            Create Sample
          </Button>
          <Button onClick={handleAddProject}>
            <Plus className="mr-2 h-4 w-4" /> Add Project
          </Button>
        </div>
      </div>

      {directError && (
        <Card className="mb-4 border-red-200">
          <CardHeader>
            <CardTitle className="text-red-500">API Connection Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-red-500">
              <p className="font-semibold">Error connecting to the API:</p>
              <div className="mb-2">
                <p className="font-medium">API Error:</p>
                <p>{directError instanceof Error ? directError.message : 'Unknown error'}</p>
              </div>
              <p className="mt-2">No data available. You can try to:</p>
              <ul className="list-disc pl-5 mt-1">
                <li>Check if the backend server is running on port 8080</li>
                <li>Verify API endpoints are correct</li>
                <li>Check browser console for detailed error messages</li>
                <li>Click "Refresh Data" to try again</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Projects {directProjects?.length > 0 ? `(${directProjects.length})` : ''}</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Timeline</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {directLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center">
                          <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                          <span className="text-muted-foreground">Loading projects...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : directError ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-10 text-red-500">
                        <div className="flex flex-col items-center justify-center">
                          <span className="font-bold mb-2">Error loading projects</span>
                          <span className="text-sm mb-4">
                            {directError instanceof Error
                              ? directError.message
                              : 'An unexpected error occurred. Please try again.'}
                          </span>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              onClick={fetchProjectsDirectly}
                            >
                              Retry
                            </Button>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredProjects.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6">
                        <div className="flex flex-col items-center justify-center space-y-2">
                          <span className="text-muted-foreground">
                            No projects found
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Trigger a direct API fetch
                              console.log("Manually refreshing project data");
                              fetchProjectsDirectly();
                            }}
                          >
                            <RefreshCcw className="mr-2 h-4 w-4" />
                            Refresh Data
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={handleAddProject}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            Add Project
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredProjects.map((project) => (
                      <TableRow key={project.id}>
                        <TableCell className="font-medium">{project.name}</TableCell>
                        <TableCell>
                          {typeof project.client === 'string'
                            ? project.client
                            : (project.client?.name || 'Unknown client')}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-sm">
                              Start: {project.startDate ? new Date(project.startDate).toLocaleDateString() : 'Not set'}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              End: {project.endDate ? new Date(project.endDate).toLocaleDateString() : 'Not set'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <StatusDropdown
                            currentStatus={project.status || 'Not set'}
                            onStatusChange={(newStatus) => handleStatusChange(project.id, newStatus)}
                          />
                        </TableCell>
                        <TableCell>{project.value || '$0.00'}</TableCell>
                        <TableCell className="text-right">
                          <ActionMenuSimple
                            projectId={project.id}
                            onEdit={handleEditProject}
                            onView={handleViewInvoices}
                            onDelete={handleDeleteProject}
                          />
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      <DynamicProjectFormDialog
        open={isProjectDialogOpen}
        onOpenChange={setIsProjectDialogOpen}
        projectId={selectedProject?.id}
        defaultValues={selectedProject}
        clients={clientsData?.map(client => ({ id: client.id, name: client.name })) || []}
        onSave={async (projectData) => {
          try {
            // Create a loading toast
            const loadingToast = toast.loading("Saving project...");

            // Make the API call to save the project
            console.log("Original project data:", JSON.stringify(projectData, null, 2));

            let savedProject;

            try {
              if (projectData.id) {
                // Update existing project
                savedProject = await projectService.updateProject(projectData.id, projectData);
              } else {
                // Try to create with direct API first
                try {
                  savedProject = await directApiService.createProject(projectData);
                  console.log("Project created with direct API:", savedProject);
                } catch (directError) {
                  console.error("Error creating project with direct API:", directError);
                  // Fall back to project service
                  savedProject = await projectService.createProject(projectData);
                  console.log("Project created with project service:", savedProject);
                }
              }

              console.log("Project saved successfully:", savedProject);
            } catch (error) {
              console.error("Error saving project:", error);
              throw error;
            }

            // Update the toast
            toast.success("Project saved successfully", {
              id: loadingToast
            });

            // Close the dialog
            setIsProjectDialogOpen(false);

            // Refresh the projects list
            fetchProjectsDirectly();
          } catch (error) {
            console.error("Error saving project:", error);
            toast.error("Failed to save project", {
              description: error instanceof Error ? error.message : "Unknown error"
            });
          }
        }}
      />
    </div>
  );
};

export default Projects;
