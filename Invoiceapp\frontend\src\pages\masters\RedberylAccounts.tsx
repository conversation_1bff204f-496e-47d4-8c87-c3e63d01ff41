import React, { useState, useEffect } from "react";
import { z } from "zod";
import MasterPageTemplate, { MasterItem } from "@/components/masters/MasterPageTemplate";
import { toast } from "sonner";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { redberylAccountService, RedberylAccount } from "@/services/redberylAccountService";

// Define the schema for Redberyl account form
const redberylAccountSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  glCode: z.string().optional(),
  costCenter: z.string().optional(),
  accountingNotes: z.string().optional(),
  bankName: z.string().min(1, "Bank name is required"),
  branchName: z.string().optional(),
  accountName: z.string().optional(),
  accountNumber: z.string().min(1, "Account number is required"),
  ifscCode: z.string().optional(),
  accountType: z.string().optional(),
  gstn: z.string().optional(),
  cin: z.string().optional(),
  panNo: z.string().optional(),
});

// Mock data for Redberyl accounts
const initialRedberylAccounts: MasterItem[] = [
  {
    id: "1",
    name: "Main Account",
    description: "Primary business account for all transactions",
    glCode: "GL001",
    costCenter: "CC001",
    accountingNotes: "Main operating account for the company",
    bankName: "HDFC Bank",
    branchName: "Andheri East",
    accountName: "Redberyl Solutions Pvt Ltd",
    accountNumber: "**********",
    ifscCode: "HDFC0001234",
    accountType: "Current",
    gstn: "27**********1Z5",
    cin: "U72200MH2020PTC123456",
    panNo: "**********",
  },
  {
    id: "2",
    name: "Secondary Account",
    description: "Secondary account for specific clients",
    glCode: "GL002",
    costCenter: "CC002",
    accountingNotes: "Used for international transactions",
    bankName: "ICICI Bank",
    branchName: "Bandra West",
    accountName: "Redberyl Solutions Pvt Ltd",
    accountNumber: "**********",
    ifscCode: "ICIC0004321",
    accountType: "Current",
    gstn: "27**********1Z5",
    cin: "U72200MH2020PTC123456",
    panNo: "**********",
  },
  {
    id: "3",
    name: "Tax Account",
    description: "Account for tax payments and refunds",
    glCode: "GL003",
    costCenter: "CC003",
    accountingNotes: "Used exclusively for tax-related transactions",
    bankName: "SBI Bank",
    branchName: "Powai",
    accountName: "Redberyl Solutions Pvt Ltd - Tax",
    accountNumber: "**********",
    ifscCode: "SBIN0005678",
    accountType: "Current",
    gstn: "27**********1Z5",
    cin: "U72200MH2020PTC123456",
    panNo: "**********",
  },
];

const RedberylAccounts = () => {
  const [redberylAccounts, setRedberylAccounts] = useState<MasterItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch data from the API
  useEffect(() => {
    const fetchRedberylAccounts = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log("Fetching Redberyl accounts from API...");
        const accounts = await redberylAccountService.getAllRedberylAccounts();
        console.log("Fetched Redberyl accounts:", accounts);

        if (accounts && accounts.length > 0) {
          // Map the API response to the MasterItem format
          const formattedAccounts: MasterItem[] = accounts.map(account => ({
            id: account.id.toString(),
            name: account.name || account.accountName || `Account ${account.id}`,
            description: account.accountingNotes || "",
            glCode: account.glCode || "",
            costCenter: account.costCenter || "",
            accountingNotes: account.accountingNotes || "",
            bankName: account.bankName || "",
            branchName: account.branchName || "",
            accountName: account.accountName || "",
            accountNumber: account.accountNo || "",
            // Also add accountNo to ensure compatibility with the API
            accountNo: account.accountNo || "",
            ifscCode: account.ifscCode || "",
            accountType: account.accountType || "",
            gstn: account.gstn || "",
            cin: account.cin || "",
            panNo: account.panNo || "",
          }));

          console.log("Formatted Redberyl accounts:", formattedAccounts);
          setRedberylAccounts(formattedAccounts);
          toast.success("Redberyl accounts loaded successfully");
        } else {
          console.warn("No Redberyl accounts found or empty response");
          setRedberylAccounts(initialRedberylAccounts);
          toast.info("Using default Redberyl accounts data");
        }
      } catch (error) {
        console.error("Error fetching Redberyl accounts:", error);
        setError("Failed to load Redberyl accounts. Using default data.");
        setRedberylAccounts(initialRedberylAccounts);
        toast.error("Failed to load Redberyl accounts. Using default data.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchRedberylAccounts();
  }, []);

  // Handle adding a new Redberyl account
  const handleAddRedberylAccount = async (data: z.infer<typeof redberylAccountSchema>) => {
    try {
      // Prepare the data for the API
      const newAccountData: Partial<RedberylAccount> = {
        name: data.name,
        accountingNotes: data.description,
        glCode: data.glCode,
        costCenter: data.costCenter,
        bankName: data.bankName,
        branchName: data.branchName,
        accountName: data.accountName || data.name,
        accountNo: data.accountNumber,
        ifscCode: data.ifscCode,
        accountType: data.accountType,
        gstn: data.gstn,
        cin: data.cin,
        panNo: data.panNo,
      };

      console.log("Creating new Redberyl account:", newAccountData);

      // Call the API to create the account
      const createdAccount = await fetch('http://localhost:8080/redberyl-accounts/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Basic ' + btoa('admin:admin123')
        },
        body: JSON.stringify(newAccountData)
      });

      if (!createdAccount.ok) {
        throw new Error(`Failed to create account: ${createdAccount.status} ${createdAccount.statusText}`);
      }

      const createdAccountData = await createdAccount.json();
      console.log("Created Redberyl account:", createdAccountData);

      // Format the new account for the UI
      const newRedberylAccount: MasterItem = {
        id: createdAccountData.id.toString(),
        name: createdAccountData.name || createdAccountData.accountName || `Account ${createdAccountData.id}`,
        description: createdAccountData.accountingNotes || "",
        glCode: createdAccountData.glCode || "",
        costCenter: createdAccountData.costCenter || "",
        accountingNotes: createdAccountData.accountingNotes || "",
        bankName: createdAccountData.bankName || "",
        branchName: createdAccountData.branchName || "",
        accountName: createdAccountData.accountName || "",
        accountNumber: createdAccountData.accountNo || "",
        accountNo: createdAccountData.accountNo || "", // Add accountNo for API compatibility
        ifscCode: createdAccountData.ifscCode || "",
        accountType: createdAccountData.accountType || "",
        gstn: createdAccountData.gstn || "",
        cin: createdAccountData.cin || "",
        panNo: createdAccountData.panNo || "",
      };

      // Update the local state
      setRedberylAccounts([...redberylAccounts, newRedberylAccount]);
      toast.success("Redberyl account added successfully");
    } catch (error) {
      console.error("Error adding Redberyl account:", error);
      toast.error("Failed to add Redberyl account");

      // Fallback to local state update for demo purposes
      const newRedberylAccount: MasterItem = {
        id: (redberylAccounts.length + 1).toString(),
        name: data.name,
        description: data.description || "",
        glCode: data.glCode || "",
        costCenter: data.costCenter || "",
        accountingNotes: data.accountingNotes || "",
        bankName: data.bankName,
        branchName: data.branchName || "",
        accountName: data.accountName || "",
        accountNumber: data.accountNumber,
        accountNo: data.accountNumber, // Add accountNo for API compatibility
        ifscCode: data.ifscCode || "",
        accountType: data.accountType || "",
        gstn: data.gstn || "",
        cin: data.cin || "",
        panNo: data.panNo || "",
      };

      setRedberylAccounts([...redberylAccounts, newRedberylAccount]);
      toast.info("Added account locally (API call failed)");
    }
  };

  // Handle editing a Redberyl account
  const handleEditRedberylAccount = async (id: string, data: z.infer<typeof redberylAccountSchema>) => {
    try {
      // Prepare the data for the API
      const updateAccountData: Partial<RedberylAccount> = {
        name: data.name,
        accountingNotes: data.description,
        glCode: data.glCode,
        costCenter: data.costCenter,
        bankName: data.bankName,
        branchName: data.branchName,
        accountName: data.accountName || data.name,
        accountNo: data.accountNumber,
        ifscCode: data.ifscCode,
        accountType: data.accountType,
        gstn: data.gstn,
        cin: data.cin,
        panNo: data.panNo,
      };

      console.log(`Updating Redberyl account with ID ${id}:`, updateAccountData);

      // Call the API to update the account
      const updatedAccount = await fetch(`http://localhost:8080/redberyl-accounts/update/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Basic ' + btoa('admin:admin123')
        },
        body: JSON.stringify(updateAccountData)
      });

      if (!updatedAccount.ok) {
        throw new Error(`Failed to update account: ${updatedAccount.status} ${updatedAccount.statusText}`);
      }

      const updatedAccountData = await updatedAccount.json();
      console.log("Updated Redberyl account:", updatedAccountData);

      // Update the local state with the response from the API
      const updatedRedberylAccounts = redberylAccounts.map((account) =>
        account.id === id
          ? {
              ...account,
              name: updatedAccountData.name || updatedAccountData.accountName || account.name,
              description: updatedAccountData.accountingNotes || account.description,
              glCode: updatedAccountData.glCode || account.glCode,
              costCenter: updatedAccountData.costCenter || account.costCenter,
              accountingNotes: updatedAccountData.accountingNotes || account.accountingNotes,
              bankName: updatedAccountData.bankName || account.bankName,
              branchName: updatedAccountData.branchName || account.branchName,
              accountName: updatedAccountData.accountName || account.accountName,
              accountNumber: updatedAccountData.accountNo || account.accountNumber,
              accountNo: updatedAccountData.accountNo || account.accountNumber, // Add accountNo for API compatibility
              ifscCode: updatedAccountData.ifscCode || account.ifscCode,
              accountType: updatedAccountData.accountType || account.accountType,
              gstn: updatedAccountData.gstn || account.gstn,
              cin: updatedAccountData.cin || account.cin,
              panNo: updatedAccountData.panNo || account.panNo,
            }
          : account
      );

      setRedberylAccounts(updatedRedberylAccounts);
      toast.success("Redberyl account updated successfully");
    } catch (error) {
      console.error(`Error updating Redberyl account with ID ${id}:`, error);
      toast.error("Failed to update Redberyl account");

      // Fallback to local state update for demo purposes
      const updatedRedberylAccounts = redberylAccounts.map((account) =>
        account.id === id
          ? {
              ...account,
              name: data.name,
              description: data.description || "",
              glCode: data.glCode || "",
              costCenter: data.costCenter || "",
              accountingNotes: data.accountingNotes || "",
              bankName: data.bankName,
              branchName: data.branchName || "",
              accountName: data.accountName || "",
              accountNumber: data.accountNumber,
              accountNo: data.accountNumber, // Add accountNo for API compatibility
              ifscCode: data.ifscCode || "",
              accountType: data.accountType || "",
              gstn: data.gstn || "",
              cin: data.cin || "",
              panNo: data.panNo || "",
            }
          : account
      );

      setRedberylAccounts(updatedRedberylAccounts);
      toast.info("Updated account locally (API call failed)");
    }
  };

  // Handle deleting a Redberyl account
  const handleDeleteRedberylAccount = async (id: string) => {
    try {
      console.log(`Deleting Redberyl account with ID ${id}`);

      // Call the API to delete the account
      const deleteResponse = await fetch(`http://localhost:8080/redberyl-accounts/deleteById/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': 'Basic ' + btoa('admin:admin123')
        }
      });

      if (!deleteResponse.ok) {
        throw new Error(`Failed to delete account: ${deleteResponse.status} ${deleteResponse.statusText}`);
      }

      console.log(`Successfully deleted Redberyl account with ID ${id}`);

      // Update the local state
      const updatedRedberylAccounts = redberylAccounts.filter((account) => account.id !== id);
      setRedberylAccounts(updatedRedberylAccounts);
      toast.success("Redberyl account deleted successfully");
    } catch (error) {
      console.error(`Error deleting Redberyl account with ID ${id}:`, error);
      toast.error("Failed to delete Redberyl account");

      // Fallback to local state update for demo purposes
      const updatedRedberylAccounts = redberylAccounts.filter((account) => account.id !== id);
      setRedberylAccounts(updatedRedberylAccounts);
      toast.info("Deleted account locally (API call failed)");
    }
  };

  // Handle initializing default account
  const handleInitializeDefault = async () => {
    try {
      console.log("Initializing default RedBeryl account...");

      const response = await fetch('/api/noauth/redberyl-accounts/init-default', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const message = await response.text();
        toast.success(message);

        // Refresh the accounts list
        const accounts = await redberylAccountService.getAllRedberylAccounts();
        if (accounts && accounts.length > 0) {
          const formattedAccounts: MasterItem[] = accounts.map(account => ({
            id: account.id.toString(),
            name: account.accountName || account.name || "Unknown Account",
            description: account.accountingNotes || "RedBeryl account",
            glCode: account.glCode || "",
            costCenter: account.costCenter || "",
            accountingNotes: account.accountingNotes || "",
            bankName: account.bankName || "",
            branchName: account.branchName || "",
            accountName: account.accountName || "",
            accountNumber: account.accountNo || "",
            ifscCode: account.ifscCode || "",
            accountType: account.accountType || "",
            gstn: account.gstn || "",
            cin: account.cin || "",
            panNo: account.panNo || "",
          }));
          setRedberylAccounts(formattedAccounts);
        }
      } else {
        const errorMessage = await response.text();
        toast.error(`Failed to initialize default account: ${errorMessage}`);
      }
    } catch (error) {
      console.error("Error initializing default account:", error);
      toast.error("Failed to initialize default account");
    }
  };

  // Extra form fields for Redberyl accounts
  const extraFields = (
    <>
      {/* Accounting Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          name="glCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>GL Code</FormLabel>
              <FormControl>
                <Input placeholder="Enter GL code" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="costCenter"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cost Center</FormLabel>
              <FormControl>
                <Input placeholder="Enter cost center" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        name="accountingNotes"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Accounting Notes</FormLabel>
            <FormControl>
              <Input placeholder="Enter accounting notes" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Bank Account Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          name="bankName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bank Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter bank name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="branchName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Branch Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter branch name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          name="accountName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Account Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter account name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="accountNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Account Number</FormLabel>
              <FormControl>
                <Input placeholder="Enter account number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          name="ifscCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>IFSC Code</FormLabel>
              <FormControl>
                <Input placeholder="Enter IFSC code" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="accountType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Account Type</FormLabel>
              <FormControl>
                <Input placeholder="Enter account type" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Company Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          name="gstn"
          render={({ field }) => (
            <FormItem>
              <FormLabel>GSTN</FormLabel>
              <FormControl>
                <Input placeholder="Enter GSTN" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="cin"
          render={({ field }) => (
            <FormItem>
              <FormLabel>CIN</FormLabel>
              <FormControl>
                <Input placeholder="Enter CIN" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="panNo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>PAN Number</FormLabel>
              <FormControl>
                <Input placeholder="Enter PAN number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </>
  );

  // Extra columns for the table
  const extraColumns = [
    {
      header: "GL Code",
      accessor: "glCode",
    },
    {
      header: "Bank Name",
      accessor: "bankName",
    },
    {
      header: "Account Name",
      accessor: "accountName",
    },
    {
      header: "Account Number",
      accessor: "accountNumber",
    },
    {
      header: "IFSC Code",
      accessor: "ifscCode",
    },
    {
      header: "Account Type",
      accessor: "accountType",
    },
    {
      header: "GSTN",
      accessor: "gstn",
    },
  ];

  return (
    <MasterPageTemplate
      title="Redberyl Accounts"
      description="Manage Redberyl bank accounts for invoicing and payments"
      items={redberylAccounts}
      formSchema={redberylAccountSchema}
      extraFields={extraFields}
      extraColumns={extraColumns}
      onAdd={handleAddRedberylAccount}
      onEdit={handleEditRedberylAccount}
      onDelete={handleDeleteRedberylAccount}
      isLoading={isLoading}
      error={error || undefined}
    />
  );
};

export default RedberylAccounts;
