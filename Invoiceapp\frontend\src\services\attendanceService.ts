import api from './api';

export interface AttendanceDto {
  id?: number;
  candidateId: number;
  month: number;
  year: number;
  daysWorked: number;
  dailyRate: number;
  calculatedSalary: number;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CandidateDetailDto {
  id: number;
  name: string;
  designation?: string;
  panNo?: string;
  aadharNo?: string;
  uanNo?: string;
  experienceInYrs?: number;
  bankAccountNo?: string;
  branchName?: string;
  ifscCode?: string;
  address?: string;
  salaryOffered?: number;
  billingRate?: number;
  joiningDate?: string;
  
  // Client information
  clientId?: number;
  clientName?: string;
  
  // Project information
  projectId?: number;
  projectName?: string;
  
  // SPOC information
  managerSpocId?: number;
  managerSpocName?: string;
  hrSpocId?: number;
  hrSpocName?: string;
  financeSpocId?: number;
  financeSpocName?: string;
  
  // Calculated daily rate
  dailyRate?: number;
  
  createdAt?: string;
  updatedAt?: string;
}

export interface AttendanceWithCandidateDto extends AttendanceDto {
  candidateName: string;
}

class AttendanceService {
  
  // Get all attendance records
  async getAllAttendance(): Promise<AttendanceWithCandidateDto[]> {
    const response = await api.get('/attendance');
    return response.data;
  }

  // Get attendance by ID
  async getAttendanceById(id: number): Promise<AttendanceDto> {
    const response = await api.get(`/attendance/${id}`);
    return response.data;
  }

  // Get attendance by candidate ID
  async getAttendanceByCandidateId(candidateId: number): Promise<AttendanceDto[]> {
    const response = await api.get(`/attendance/candidate/${candidateId}`);
    return response.data;
  }

  // Get attendance by candidate, month, and year
  async getAttendanceByCandidateMonthYear(candidateId: number, month: number, year: number): Promise<AttendanceDto> {
    const response = await api.get(`/attendance/candidate/${candidateId}/month/${month}/year/${year}`);
    return response.data;
  }

  // Create new attendance record
  async createAttendance(attendance: Omit<AttendanceDto, 'id' | 'createdAt' | 'updatedAt'>): Promise<AttendanceDto> {
    const response = await api.post('/attendance', attendance);
    return response.data;
  }

  // Update attendance record
  async updateAttendance(id: number, attendance: Partial<AttendanceDto>): Promise<AttendanceDto> {
    const response = await api.put(`/attendance/${id}`, attendance);
    return response.data;
  }

  // Delete attendance record
  async deleteAttendance(id: number): Promise<void> {
    await api.delete(`/attendance/${id}`);
  }

  // Calculate salary for given days
  async calculateSalary(candidateId: number, days: number): Promise<{ dailyRate: number; calculatedSalary: number }> {
    const response = await api.get(`/attendance/calculate-salary/${candidateId}/${days}`);
    return response.data;
  }

  // Get candidate details for auto-population
  async getCandidateDetailById(candidateId: number): Promise<CandidateDetailDto> {
    const response = await api.get(`/candidates/getDetailById/${candidateId}`);
    return response.data;
  }
}

export default new AttendanceService();
