import { api } from '@/services/api';

export interface HsnCode {
  id: number;
  code: string;
  description: string;
  gstRate: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Maps the backend HSN code response to the frontend HSN code format
 * @param hsnCode The HSN code data from the backend
 * @returns HsnCode with consistent field names
 */
export const mapHsnCodeResponse = (hsnCode: any): HsnCode => {
  if (!hsnCode) return {} as HsnCode;

  return {
    ...hsnCode,
    // Map snake_case to camelCase for consistency
    createdAt: hsnCode.created_at || hsnCode.createdAt,
    updatedAt: hsnCode.updated_at || hsnCode.updatedAt,
    // Ensure gstRate is a number
    gstRate: typeof hsnCode.gstRate === 'string' ? parseFloat(hsnCode.gstRate) : (hsnCode.gstRate || 0),
  };
};

export const hsnCodeService = {
  /**
   * Get all HSN codes
   * @returns Promise with array of HSN codes
   */
  getAllHsnCodes: async (): Promise<HsnCode[]> => {
    try {
      console.log('HsnCodeService: Fetching all HSN codes');

      // Try multiple endpoints in order of preference
      const endpoints = [
        '/hsn-codes/getAll',
        '/api/hsn-codes',
        '/hsn-codes',
        '/api/noauth/hsn-codes'
      ];

      let response = null;
      let data = null;

      // Try each endpoint until one works
      for (const endpoint of endpoints) {
        try {
          console.log(`HsnCodeService: Trying endpoint ${endpoint}`);

          response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': 'Basic ' + btoa('admin:admin123')
            }
          });

          console.log(`HsnCodeService: Response status for ${endpoint}:`, response.status);

          if (response.ok) {
            data = await response.json();
            console.log(`HsnCodeService: Successfully fetched HSN codes from ${endpoint}:`, data);

            if (Array.isArray(data) && data.length > 0) {
              break; // Exit the loop if we got valid data
            } else if (data && typeof data === 'object') {
              // Check if the response is an object with data property
              if (Array.isArray(data.data) && data.data.length > 0) {
                data = data.data;
                break;
              }

              // Try to find any array property
              for (const key in data) {
                if (Array.isArray(data[key]) && data[key].length > 0) {
                  data = data[key];
                  break;
                }
              }

              if (Array.isArray(data) && data.length > 0) {
                break;
              } else {
                console.warn(`HsnCodeService: Endpoint ${endpoint} returned object without valid data array`);
              }
            } else {
              console.warn(`HsnCodeService: Endpoint ${endpoint} returned empty array or invalid data`);
            }
          } else {
            console.warn(`HsnCodeService: Endpoint ${endpoint} returned status ${response.status}`);
          }
        } catch (endpointError) {
          console.error(`HsnCodeService: Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If we have valid data from any endpoint, process and return it
      if (data && Array.isArray(data) && data.length > 0) {
        console.log('HsnCodeService: Processing HSN code data:', data);
        return data.map(mapHsnCodeResponse);
      }

      // If all endpoints failed, return mock data
      console.warn('HsnCodeService: All endpoints failed, returning mock data');
      return [
        {
          id: 1,
          code: "998313",
          description: "IT consulting services",
          gstRate: 18
        },
        {
          id: 2,
          code: "998314",
          description: "IT design and development services",
          gstRate: 18
        },
        {
          id: 3,
          code: "998315",
          description: "IT support services",
          gstRate: 18
        }
      ];
    } catch (error) {
      console.error('HsnCodeService: Error in getAllHsnCodes:', error);

      // Return mock data in case of error
      return [
        {
          id: 1,
          code: "998313",
          description: "IT consulting services",
          gstRate: 18
        },
        {
          id: 2,
          code: "998314",
          description: "IT design and development services",
          gstRate: 18
        },
        {
          id: 3,
          code: "998315",
          description: "IT support services",
          gstRate: 18
        }
      ];
    }
  },

  /**
   * Get HSN code by ID
   * @param id HSN code ID
   * @returns Promise with HSN code data
   */
  getHsnCodeById: async (id: number): Promise<HsnCode> => {
    try {
      console.log(`HsnCodeService: Fetching HSN code with ID ${id}`);
      const hsnCode = await api.getHsnCode(id);
      console.log(`HsnCodeService: Successfully fetched HSN code with ID ${id}`, hsnCode);
      return mapHsnCodeResponse(hsnCode);
    } catch (error) {
      console.error(`HsnCodeService: Error fetching HSN code with ID ${id}:`, error);

      // Try to fetch HSN code directly from the API
      try {
        console.log(`HsnCodeService: Trying alternative approach to fetch HSN code with ID ${id}`);

        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        // Try endpoints, prioritizing the one without /v1
        const endpoints = [
          `/hsn-codes/${id}`,
          `/api/hsn-codes/${id}`,
          `/v1/hsn-codes/${id}`
        ];

        for (const endpoint of endpoints) {
          try {
            console.log(`HsnCodeService: Trying endpoint ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
              // Remove credentials: 'include' as it conflicts with wildcard CORS
            });

            if (!response.ok) {
              console.warn(`HsnCodeService: Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`HsnCodeService: Successfully fetched HSN code from ${endpoint}`, data);

            // Map and return the HSN code data
            return mapHsnCodeResponse(data);
          } catch (endpointError) {
            console.error(`HsnCodeService: Error fetching from ${endpoint}:`, endpointError);
          }
        }

        // If all endpoints fail, throw the original error
        throw error;
      } catch (fallbackError) {
        console.error(`HsnCodeService: All approaches to fetch HSN code with ID ${id} failed:`, fallbackError);
        throw error;
      }
    }
  },

  /**
   * Create a new HSN code
   * @param hsnCodeData HSN code data
   * @returns Promise with created HSN code data
   */
  createHsnCode: async (hsnCodeData: Partial<HsnCode>): Promise<HsnCode> => {
    try {
      console.log('HsnCodeService: Creating new HSN code', hsnCodeData);

      // Use the API service to create the HSN code
      const result = await api.createHsnCode(hsnCodeData);
      console.log('HsnCodeService: Successfully created HSN code', result);

      // Map and return the HSN code data
      return mapHsnCodeResponse(result);
    } catch (error) {
      console.error('HsnCodeService: Error creating HSN code:', error);

      // Try direct API call as fallback
      try {
        console.log('HsnCodeService: Trying fallback approach to create HSN code');

        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        // Try endpoints, prioritizing the one without /v1
        const endpoints = [
          '/hsn-codes',
          '/api/hsn-codes',
          '/hsn-codes/create',
          '/v1/hsn-codes'
        ];

        for (const endpoint of endpoints) {
          try {
            console.log(`HsnCodeService: Trying to create HSN code using endpoint ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'POST',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              },
              body: JSON.stringify(hsnCodeData)
              // Remove credentials: 'include' as it conflicts with wildcard CORS
            });

            if (!response.ok) {
              console.warn(`HsnCodeService: Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`HsnCodeService: Successfully created HSN code using ${endpoint}`, data);

            // Map and return the HSN code data
            return mapHsnCodeResponse(data);
          } catch (endpointError) {
            console.error(`HsnCodeService: Error creating HSN code using ${endpoint}:`, endpointError);
          }
        }

        throw new Error('Failed to create HSN code');
      } catch (fallbackError) {
        console.error('HsnCodeService: All approaches to create HSN code failed:', fallbackError);
        throw error;
      }
    }
  },

  /**
   * Update an existing HSN code
   * @param id HSN code ID
   * @param hsnCodeData HSN code data
   * @returns Promise with updated HSN code data
   */
  updateHsnCode: async (id: number, hsnCodeData: Partial<HsnCode>): Promise<HsnCode> => {
    try {
      console.log(`HsnCodeService: Updating HSN code with ID ${id}`, hsnCodeData);

      // Use the API service to update the HSN code
      const result = await api.updateHsnCode(id, hsnCodeData);
      console.log(`HsnCodeService: Successfully updated HSN code with ID ${id}`, result);

      // Map and return the HSN code data
      return mapHsnCodeResponse(result);
    } catch (error) {
      console.error(`HsnCodeService: Error updating HSN code with ID ${id}:`, error);

      // Try direct API call as fallback
      try {
        console.log(`HsnCodeService: Trying fallback approach to update HSN code with ID ${id}`);

        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        // Try endpoints, prioritizing the one without /v1
        const endpoints = [
          `/hsn-codes/${id}`,
          `/api/hsn-codes/${id}`,
          `/hsn-codes/update/${id}`,
          `/v1/hsn-codes/${id}`
        ];

        for (const endpoint of endpoints) {
          try {
            console.log(`HsnCodeService: Trying to update HSN code using endpoint ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'PUT',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              },
              body: JSON.stringify(hsnCodeData)
              // Remove credentials: 'include' as it conflicts with wildcard CORS
            });

            if (!response.ok) {
              console.warn(`HsnCodeService: Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`HsnCodeService: Successfully updated HSN code using ${endpoint}`, data);

            // Map and return the HSN code data
            return mapHsnCodeResponse(data);
          } catch (endpointError) {
            console.error(`HsnCodeService: Error updating HSN code using ${endpoint}:`, endpointError);
          }
        }

        throw new Error(`Failed to update HSN code with ID ${id}`);
      } catch (fallbackError) {
        console.error(`HsnCodeService: All approaches to update HSN code with ID ${id} failed:`, fallbackError);
        throw error;
      }
    }
  },

  /**
   * Delete an HSN code
   * @param id HSN code ID
   * @returns Promise with void
   */
  deleteHsnCode: async (id: number): Promise<void> => {
    try {
      console.log(`HsnCodeService: Deleting HSN code with ID ${id}`);

      // Use the API service to delete the HSN code
      await api.deleteHsnCode(id);
      console.log(`HsnCodeService: Successfully deleted HSN code with ID ${id}`);

      return;
    } catch (error) {
      console.error(`HsnCodeService: Error deleting HSN code with ID ${id}:`, error);

      // Try direct API call as fallback
      try {
        console.log(`HsnCodeService: Trying fallback approach to delete HSN code with ID ${id}`);

        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        // Try endpoints, prioritizing the one without /v1
        const endpoints = [
          `/hsn-codes/${id}`,
          `/api/hsn-codes/${id}`,
          `/hsn-codes/deleteById/${id}`,
          `/v1/hsn-codes/${id}`
        ];

        for (const endpoint of endpoints) {
          try {
            console.log(`HsnCodeService: Trying to delete HSN code using endpoint ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'DELETE',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
              // Remove credentials: 'include' as it conflicts with wildcard CORS
            });

            if (!response.ok) {
              console.warn(`HsnCodeService: Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            console.log(`HsnCodeService: Successfully deleted HSN code using ${endpoint}`);
            return;
          } catch (endpointError) {
            console.error(`HsnCodeService: Error deleting HSN code using ${endpoint}:`, endpointError);
          }
        }

        throw new Error(`Failed to delete HSN code with ID ${id}`);
      } catch (fallbackError) {
        console.error(`HsnCodeService: All approaches to delete HSN code with ID ${id} failed:`, fallbackError);
        throw error;
      }
    }
  }
};

export default hsnCodeService;
