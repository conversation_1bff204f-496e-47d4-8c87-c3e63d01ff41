import { api } from './api';
import { getProxiedUrl, getBasicAuthHeader } from '@/utils/apiUtils';

export interface Lead {
  id?: string | number;
  name: string;
  company: string;
  email: string;
  phone: string;
  status: string;
  source: string;
  dateAdded?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Convert backend date format to frontend format if needed
const formatLead = (lead: any): Lead => {
  return {
    id: lead.id,
    name: lead.name,
    company: lead.company || '',
    email: lead.email,
    phone: lead.phone || '',
    status: lead.status || 'New',
    source: lead.source || 'Website',
    dateAdded: lead.createdAt ? new Date(lead.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    createdAt: lead.createdAt,
    updatedAt: lead.updatedAt
  };
};

export const leadService = {
  // Get all leads
  getLeads: async (): Promise<Lead[]> => {
    try {
      console.log('Fetching all leads...');

      // Direct API call to the backend endpoint through the proxy
      console.log('Sending GET request to /api/leads');
      const response = await fetch('/api/leads', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Include credentials for CORS
      });

      // Log the full response for debugging
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        // Handle 204 No Content as an empty array
        if (response.status === 204) {
          console.log('No leads found (204 No Content)');
          return [];
        }

        const errorText = await response.text();
        console.error('API error response:', errorText);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const responseText = await response.text();
      console.log('Raw API response:', responseText);

      // Handle empty response
      if (!responseText.trim()) {
        console.log('Empty response from API, returning empty array');
        return [];
      }

      try {
        const data = JSON.parse(responseText);
        console.log('Leads data from API:', data);

        // Map the data to the Lead interface
        if (Array.isArray(data)) {
          return data.map(formatLead);
        } else {
          console.warn('API returned non-array data:', data);
          return [];
        }
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        return [];
      }
    } catch (error) {
      console.error('Error fetching leads:', error);

      // For development purposes, return mock data
      console.log('Returning mock lead data for development');
      return [
        {
          id: 1,
          name: "John Smith",
          company: "Acme Corporation",
          email: "<EMAIL>",
          phone: "************",
          status: "New",
          source: "Website",
          dateAdded: new Date().toISOString().split('T')[0]
        },
        {
          id: 2,
          name: "Jane Doe",
          company: "XYZ Industries",
          email: "<EMAIL>",
          phone: "************",
          status: "Contacted",
          source: "Referral",
          dateAdded: new Date().toISOString().split('T')[0]
        }
      ];
    }
  },

  // Get lead by ID
  getLead: async (id: number | string): Promise<Lead> => {
    try {
      // Try the standard API endpoint first
      try {
        const response = await api.getLead(Number(id));
        return formatLead(response);
      } catch (error) {
        // If that fails, try the alternative endpoint
        console.log('Trying alternative endpoint for lead by ID...');
        const apiUrl = getProxiedUrl(`/leads/getById/${id}`);
        const response = await fetch(apiUrl);

        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return formatLead(data);
      }
    } catch (error) {
      console.error(`Error fetching lead with ID ${id}:`, error);
      throw error;
    }
  },

  // Create new lead
  createLead: async (lead: Lead): Promise<Lead> => {
    try {
      console.log('Creating new lead with data:', lead);

      // Prepare the lead data for the backend
      const leadData = {
        name: lead.name,
        company: lead.company || '',
        email: lead.email,
        phone: lead.phone || '',
        status: lead.status || 'New',
        source: lead.source || 'Website'
      };

      console.log('Formatted lead data for API:', leadData);

      // Direct API call to the backend endpoint through the proxy
      console.log('Sending POST request to /api/leads');

      // Make the API call with credentials for the proxy
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(leadData),
        credentials: 'include' // Include credentials for CORS
      });

      // Log the full response for debugging
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries([...response.headers.entries()]));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const responseText = await response.text();
      console.log('Raw API response:', responseText);

      // Handle empty response
      if (!responseText.trim()) {
        console.error('Empty response from API');
        throw new Error('Empty response from API - data not saved to database');
      }

      try {
        const createdLead = JSON.parse(responseText);
        console.log('Created lead from API:', createdLead);
        return formatLead(createdLead);
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        throw new Error('Invalid JSON response from API - data not saved to database');
      }
    } catch (error) {
      console.error('Error creating lead:', error);
      // Re-throw the error instead of returning mock data
      // This ensures the UI shows an error message instead of falsely indicating success
      throw error;
    }
  },

  // Update lead
  updateLead: async (id: number | string, lead: Lead): Promise<Lead> => {
    try {
      // Prepare the lead data for the backend
      const leadData = {
        name: lead.name,
        company: lead.company,
        email: lead.email,
        phone: lead.phone,
        status: lead.status,
        source: lead.source
      };

      // Try both endpoints
      try {
        // Try the standard API endpoint first
        const apiUrl = getProxiedUrl(`/leads/${id}`);
        const response = await fetch(apiUrl, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(leadData),
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }

        const updatedLead = await response.json();
        return formatLead(updatedLead);
      } catch (error) {
        console.log('Trying alternative endpoint for update lead...');
        // If that fails, try the alternative endpoint
        const apiUrl = getProxiedUrl(`/leads/update/${id}`);
        const response = await fetch(apiUrl, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(leadData),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.message || `API error: ${response.status} ${response.statusText}`
          );
        }

        const updatedLead = await response.json();
        return formatLead(updatedLead);
      }
    } catch (error) {
      console.error(`Error updating lead with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete lead
  deleteLead: async (id: number | string): Promise<void> => {
    try {
      // Try both endpoints
      try {
        // Try the standard API endpoint first
        const apiUrl = getProxiedUrl(`/leads/${id}`);
        const response = await fetch(apiUrl, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }

        return;
      } catch (error) {
        console.log('Trying alternative endpoint for delete lead...');
        // If that fails, try the alternative endpoint
        const apiUrl = getProxiedUrl(`/leads/deleteById/${id}`);
        const response = await fetch(apiUrl, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.message || `API error: ${response.status} ${response.statusText}`
          );
        }
      }
    } catch (error) {
      console.error(`Error deleting lead with ID ${id}:`, error);
      throw error;
    }
  },

  // Update lead status
  updateLeadStatus: async (id: number | string, status: string): Promise<Lead> => {
    try {
      // Get the current lead
      const currentLead = await leadService.getLead(id);

      // Update the status
      return await leadService.updateLead(id, { ...currentLead, status });
    } catch (error) {
      console.error(`Error updating status for lead with ID ${id}:`, error);
      throw error;
    }
  }
};
