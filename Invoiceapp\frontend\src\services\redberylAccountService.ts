import { DEFAULT_HEADERS } from '@/config/api';
import { getAuthToken } from '@/utils/auth';
import { getProxiedUrl, getBasicAuthHeader } from '@/utils/apiUtils';

export interface RedberylAccount {
  id: string | number;  // Allow both string and number for flexibility
  name?: string;
  glCode?: string;
  costCenter?: string;
  accountingNotes?: string;
  bankName?: string;
  branchName?: string;
  accountName?: string;
  accountNo?: string;
  ifscCode?: string;
  accountType?: string;
  gstn?: string;
  cin?: string;
  panNo?: string;
  created_at?: string;
  updated_at?: string;

  // Mapped fields for consistency
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Maps the backend redberyl account response to the frontend format
 * @param account The redberyl account data from the backend
 * @returns RedberylAccount with consistent field names
 */
export const mapRedberylAccountResponse = (account: any): RedberylAccount => {
  if (!account) return {} as RedberylAccount;

  return {
    ...account,
    // Map snake_case to camelCase for consistency
    glCode: account.gl_code || account.glCode,
    costCenter: account.cost_center || account.costCenter,
    accountingNotes: account.accounting_notes || account.accountingNotes,
    bankName: account.bank_name || account.bankName,
    branchName: account.branch_name || account.branchName,
    accountName: account.account_name || account.accountName,
    accountNo: account.account_no || account.accountNo,
    ifscCode: account.ifsc_code || account.ifscCode,
    accountType: account.account_type || account.accountType,
    panNo: account.pan_no || account.panNo,
    createdAt: account.created_at || account.createdAt,
    updatedAt: account.updated_at || account.updatedAt,
  };
};

// Helper function to handle API requests
const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const token = getAuthToken();
  const headers = {
    ...DEFAULT_HEADERS,
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...(options.headers || {}),
  };

  const apiUrl = getProxiedUrl(endpoint);
  const response = await fetch(apiUrl, {
    ...options,
    headers,
    credentials: 'include'
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `API request failed: ${response.status} ${response.statusText} - ${errorText}`
    );
  }

  // For 204 No Content responses, return null
  if (response.status === 204) {
    return null as T;
  }

  return response.json();
};

export const redberylAccountService = {
  /**
   * Get all redberyl accounts
   * @returns Promise with array of redberyl accounts
   */
  getAllRedberylAccounts: async (): Promise<RedberylAccount[]> => {
    try {
      console.log('RedberylAccountService: Fetching all redberyl accounts');

      // Try public endpoint first (no auth required)
      console.log('Trying public endpoint /api/noauth/redberyl-accounts');
      const response = await fetch('/api/noauth/redberyl-accounts', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Redberyl account data received:', data);

        if (Array.isArray(data)) {
          return data.map(account => mapRedberylAccountResponse(account));
        }
      }

      console.error(`Direct fetch failed with status: ${response.status}. Trying alternative endpoints...`);

      // Try alternative endpoints if the first one fails
      const proxyEndpoints = [
        '/api/redberyl-accounts',
        '/redberyl-accounts/getAll',
        '/redberyl-accounts',
      ];

      for (const endpoint of proxyEndpoints) {
        try {
          console.log(`Trying alternative endpoint: ${endpoint}`);
          const altResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': getBasicAuthHeader()
            },
            credentials: 'include'
          });

          if (!altResponse.ok) {
            console.error(`Fetch to ${endpoint} failed with status: ${altResponse.status}`);
            continue;
          }

          const data = await altResponse.json();
          console.log(`Successfully received data from ${endpoint}:`, data);

          if (Array.isArray(data)) {
            return data.map(account => mapRedberylAccountResponse(account));
          } else {
            console.error('Unexpected response format:', data);
          }
        } catch (directError) {
          console.error(`Fetch to ${endpoint} failed:`, directError);
        }
      }

      // If all attempts fail, try direct connection to backend
      console.log('Trying direct connection to backend at http://localhost:8080/redberyl-accounts/getAll');
      const directResponse = await fetch('http://localhost:8080/redberyl-accounts/getAll', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        credentials: 'include'
      });

      if (directResponse.ok) {
        const data = await directResponse.json();
        console.log('Successfully received data from direct backend connection:', data);

        if (Array.isArray(data)) {
          return data.map(account => mapRedberylAccountResponse(account));
        }
      }

      // If all attempts fail, return empty array to prevent UI errors
      console.error('All fetch attempts failed. Returning empty array.');
      return [];
    } catch (error) {
      console.error('Error fetching redberyl accounts:', error);
      // Return empty array instead of throwing to prevent UI errors
      return [];
    }
  },

  /**
   * Get redberyl account by ID
   * @param id Redberyl account ID
   * @returns Promise with redberyl account data
   */
  getRedberylAccountById: async (id: number): Promise<RedberylAccount> => {
    try {
      console.log(`RedberylAccountService: Fetching redberyl account with ID ${id}`);

      // Try direct fetch with API prefix first (this is what the proxy in vite.config.ts expects)
      console.log(`Trying direct fetch to /api/redberyl-accounts/getById/${id}`);
      const response = await fetch(`/api/redberyl-accounts/getById/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Redberyl account data received:', data);
        return mapRedberylAccountResponse(data);
      }

      console.error(`Direct fetch failed with status: ${response.status}. Trying alternative endpoints...`);

      // Try alternative endpoints if the first one fails
      const proxyEndpoints = [
        `/redberyl-accounts/getById/${id}`,
        `/api/redberyl-accounts/${id}`,
        `/redberyl-accounts/${id}`,
      ];

      for (const endpoint of proxyEndpoints) {
        try {
          console.log(`Trying alternative endpoint: ${endpoint}`);
          const altResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': getBasicAuthHeader()
            },
            credentials: 'include'
          });

          if (!altResponse.ok) {
            console.error(`Fetch to ${endpoint} failed with status: ${altResponse.status}`);
            continue;
          }

          const data = await altResponse.json();
          console.log(`Successfully received data from ${endpoint}:`, data);
          return mapRedberylAccountResponse(data);
        } catch (directError) {
          console.error(`Fetch to ${endpoint} failed:`, directError);
        }
      }

      // If all attempts fail, try direct connection to backend
      console.log(`Trying direct connection to backend at http://localhost:8080/redberyl-accounts/getById/${id}`);
      const directResponse = await fetch(`http://localhost:8080/redberyl-accounts/getById/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': getBasicAuthHeader()
        },
        credentials: 'include'
      });

      if (directResponse.ok) {
        const data = await directResponse.json();
        console.log('Successfully received data from direct backend connection:', data);
        return mapRedberylAccountResponse(data);
      }

      // If all attempts fail, return empty object to prevent UI errors
      console.error('All fetch attempts failed. Returning empty object.');
      return {} as RedberylAccount;
    } catch (error) {
      console.error(`Error fetching redberyl account with ID ${id}:`, error);
      // Return empty object instead of throwing to prevent UI errors
      return {} as RedberylAccount;
    }
  }
};
