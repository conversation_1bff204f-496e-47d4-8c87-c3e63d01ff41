/* Custom styles for tabs to ensure proper z-index */

/* Ensure the tabs container has a stacking context */
.tabs-container {
  position: relative;
  z-index: 1;
}

/* Increase z-index for tab content */
[data-state="active"][role="tabpanel"] {
  position: relative;
  z-index: 50 !important;
}

/* Ensure tab panels are above other content */
[role="tabpanel"] {
  position: relative;
  z-index: 40;
}

/* Ensure tab triggers are properly positioned */
[role="tab"] {
  position: relative;
  z-index: 30;
}

/* Ensure tab list is properly positioned */
[role="tablist"] {
  position: relative;
  z-index: 20;
}
