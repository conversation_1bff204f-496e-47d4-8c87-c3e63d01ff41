/**
 * Utility functions for API URLs
 */

/**
 * Converts a backend endpoint to a proxied URL that works with V<PERSON>'s proxy configuration
 * @param endpoint The backend endpoint (e.g., '/clients/getAll', '/api/clients', etc.)
 * @returns A URL that will work with the Vite proxy
 */
export const getProxiedUrl = (endpoint: string): string => {
  // Remove leading slash if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;

  // If the endpoint already starts with 'api/', 'candidates/', or 'redberyl-accounts/', use it as is
  if (cleanEndpoint.startsWith('api/') ||
      cleanEndpoint.startsWith('candidates/') ||
      cleanEndpoint.startsWith('redberyl-accounts/')) {
    return `/${cleanEndpoint}`;
  }

  // Otherwise, add the /api/ prefix for most endpoints
  return `/api/${cleanEndpoint}`;
};

/**
 * Converts a list of backend endpoints to proxied URLs
 * @param endpoints List of backend endpoints
 * @returns List of proxied URLs
 */
export const getProxiedUrls = (endpoints: string[]): string[] => {
  return endpoints.map(endpoint => getProxiedUrl(endpoint));
};

/**
 * Authentication configuration
 */
export const AUTH_CONFIG = {
  username: 'admin',
  password: 'admin123'
};

/**
 * Creates a basic auth header
 * @returns The Authorization header value
 */
export const getBasicAuthHeader = (): string => {
  return 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
};

/**
 * Default headers for API requests
 */
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};
