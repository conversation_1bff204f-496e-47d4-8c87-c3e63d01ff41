/**
 * Authentication utilities for the application
 */

// Local storage keys
const TOKEN_KEY = 'auth_token';
const USER_KEY = 'auth_user';

/**
 * Save authentication token to local storage
 * @param token The authentication token to save
 */
export const setAuthToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * Get the authentication token from local storage
 * @returns The authentication token or null if not found
 */
export const getAuthToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * Remove the authentication token from local storage
 */
export const removeAuthToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
};

/**
 * Check if the user is authenticated
 * @returns True if the user is authenticated, false otherwise
 */
export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

/**
 * Save user data to local storage
 * @param user The user data to save
 */
export const setUser = (user: any): void => {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * Get the user data from local storage
 * @returns The user data or null if not found
 */
export const getUser = (): any | null => {
  const userData = localStorage.getItem(USER_KEY);
  return userData ? JSON.parse(userData) : null;
};

/**
 * Remove the user data from local storage
 */
export const removeUser = (): void => {
  localStorage.removeItem(USER_KEY);
};

/**
 * Log out the user by removing all authentication data
 */
export const logout = (): void => {
  removeAuthToken();
  removeUser();
};

/**
 * Basic authentication helper
 * @param username The username
 * @param password The password
 * @returns The base64 encoded authentication string
 */
export const getBasicAuth = (username: string, password: string): string => {
  return `Basic ${btoa(`${username}:${password}`)}`;
};
