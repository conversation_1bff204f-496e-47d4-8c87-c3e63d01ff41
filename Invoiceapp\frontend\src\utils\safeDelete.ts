/**
 * Utility functions for safely handling delete operations
 * These functions help prevent accidental data loss by adding extra safeguards
 */

import { toast } from "sonner";

/**
 * Safely delete an entity with cascade warning and confirmation
 * @param entityType The type of entity being deleted (e.g., 'client', 'project')
 * @param entityId The ID of the entity to delete
 * @param entityName The name of the entity for display purposes
 * @param relatedEntities Array of related entity types that will be affected
 * @param deleteFunction The actual function to call if confirmed
 * @param onSuccess Callback to run on successful deletion
 * @returns Promise that resolves when the operation is complete
 */
export const safeDeleteWithCascadeWarning = async (
  entityType: string,
  entityId: string | number,
  entityName: string,
  relatedEntities: string[],
  deleteFunction: (id: string | number) => Promise<void>,
  onSuccess?: () => void
): Promise<boolean> => {
  // Create a warning message about cascade deletion
  const relatedEntitiesText = relatedEntities.length > 0
    ? `This will also delete all related ${relatedEntities.join(", ")}!`
    : "";

  // First confirmation with toast
  toast.warning(
    `WARNING: You are about to delete ${entityType} "${entityName}"`,
    {
      description: relatedEntitiesText,
      duration: 5000,
      action: {
        label: "Cancel",
        onClick: () => {
          toast.success(`${entityType.charAt(0).toUpperCase() + entityType.slice(1)} deletion cancelled`);
          return false;
        }
      }
    }
  );

  // Second confirmation with browser dialog
  const confirmMessage = `Are you sure you want to delete ${entityType} "${entityName}"?${
    relatedEntities.length > 0 
      ? `\n\nWARNING: This will also delete all related ${relatedEntities.join(", ")}!` 
      : ""
  }\n\nThis action cannot be undone.`;

  if (!window.confirm(confirmMessage)) {
    toast.info(`${entityType.charAt(0).toUpperCase() + entityType.slice(1)} deletion cancelled`);
    return false;
  }

  // For critical entities with related data, add a third confirmation with text entry
  if (relatedEntities.length > 0) {
    const confirmText = `DELETE-${entityName}`;
    const userConfirmation = window.prompt(
      `FINAL WARNING: This will delete ${entityType} "${entityName}" AND ALL RELATED DATA!\n\n` +
      `This includes: ${relatedEntities.join(", ")}\n\n` +
      `This action CANNOT be undone and may result in significant data loss.\n\n` +
      `To confirm, please type: ${confirmText}`
    );

    if (userConfirmation !== confirmText) {
      toast.error(`${entityType.charAt(0).toUpperCase() + entityType.slice(1)} deletion cancelled - confirmation text did not match`);
      return false;
    }
  }

  // Show loading toast
  const loadingToast = toast.loading(`Deleting ${entityType}: ${entityName}...`);

  try {
    // Log the deletion attempt
    console.log(`Deleting ${entityType} with ID: ${entityId}, Name: ${entityName}`);
    
    // Call the delete function
    await deleteFunction(entityId);
    
    // Update the toast
    toast.success(`${entityType.charAt(0).toUpperCase() + entityType.slice(1)} deleted successfully`, { id: loadingToast });
    
    // Call the success callback if provided
    if (onSuccess) {
      onSuccess();
    }
    
    return true;
  } catch (error) {
    console.error(`Error deleting ${entityType}:`, error);
    
    // Update the toast with error
    toast.error(
      `Failed to delete ${entityType}`, 
      { 
        id: loadingToast,
        description: error instanceof Error ? error.message : 'Unknown error'
      }
    );
    
    return false;
  }
};

/**
 * Safely delete an entity with basic confirmation
 * @param entityType The type of entity being deleted
 * @param entityId The ID of the entity to delete
 * @param entityName The name of the entity for display
 * @param deleteFunction The actual function to call if confirmed
 * @param onSuccess Callback to run on successful deletion
 * @returns Promise that resolves when the operation is complete
 */
export const safeDelete = async (
  entityType: string,
  entityId: string | number,
  entityName: string,
  deleteFunction: (id: string | number) => Promise<void>,
  onSuccess?: () => void
): Promise<boolean> => {
  return safeDeleteWithCascadeWarning(
    entityType,
    entityId,
    entityName,
    [], // No related entities
    deleteFunction,
    onSuccess
  );
};
