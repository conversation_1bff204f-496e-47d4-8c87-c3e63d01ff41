// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/Invoiceapp/frontend/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/Desktop/Invoiceapp/frontend/node_modules/@vitejs/plugin-react-swc/index.mjs";
import path from "path";
import { componentTagger } from "file:///C:/Users/<USER>/Desktop/Invoiceapp/frontend/node_modules/lovable-tagger/dist/index.js";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\Invoiceapp\\frontend";
var vite_config_default = defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0",
    // Listen on all interfaces
    port: 3e3,
    strictPort: true,
    open: true,
    cors: true,
    proxy: {
      "/api": {
        target: "http://localhost:8080",
        // Try localhost first
        changeOrigin: true,
        secure: false,
        rewrite: (path2) => path2,
        // Log more details about the connection
        onProxyReq: (proxyReq, req, res) => {
          console.log(`Proxying ${req.method} request to: ${req.url}`);
        },
        onError: (err, req, res) => {
          console.error("Proxy error details:", err);
          console.log("Attempting to connect to backend at IP address instead of localhost");
        },
        configure: (proxy, _options) => {
          proxy.on("error", (err, req, res) => {
            console.log("proxy error", err);
            if (req && req.url) {
              console.log("Retrying with IP address...");
              const ipTarget = "http://************:8080";
              const newPath = req.url.replace("/api", "");
              const newUrl = `${ipTarget}${newPath}`;
              console.log(`Redirecting to: ${newUrl}`);
              if (res && !res.headersSent) {
                res.writeHead(307, { "Location": newUrl });
                res.end();
              }
            }
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Request to the Target:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log("Received Response from the Target:", proxyRes.statusCode, req.url);
          });
        }
      }
    }
  },
  base: "/",
  plugins: [
    react(),
    mode === "development" && componentTagger()
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  }
}));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
