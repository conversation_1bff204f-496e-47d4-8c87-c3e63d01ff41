package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.service.BdmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/bdms")
@Tag(name = "BDM", description = "BDM management API")
public class BdmController {

    @Autowired
    private BdmService bdmService;

    @GetMapping
    @Operation(summary = "Get all BDMs", description = "Retrieve a list of all BDMs")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmDto>> getAllBdms() {
        List<BdmDto> bdms = bdmService.getAllBdms();
        return new ResponseEntity<>(bdms, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get BDM by ID", description = "Retrieve a BDM by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmDto> getBdmById(@PathVariable Long id) {
        BdmDto bdm = bdmService.getBdmById(id);
        return new ResponseEntity<>(bdm, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create BDM", description = "Create a new BDM")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmDto> createBdm(@Valid @RequestBody BdmDto bdmDto) {
        BdmDto createdBdm = bdmService.createBdm(bdmDto);
        return new ResponseEntity<>(createdBdm, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update BDM", description = "Update an existing BDM")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmDto> updateBdm(@PathVariable Long id, @Valid @RequestBody BdmDto bdmDto) {
        BdmDto updatedBdm = bdmService.updateBdm(id, bdmDto);
        return new ResponseEntity<>(updatedBdm, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete BDM", description = "Delete a BDM by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteBdm(@PathVariable Long id) {
        bdmService.deleteBdm(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
