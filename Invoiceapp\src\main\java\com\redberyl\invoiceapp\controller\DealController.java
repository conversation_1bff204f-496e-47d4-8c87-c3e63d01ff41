package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DealDto;
import com.redberyl.invoiceapp.service.DealService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
// import org.springframework.security.access.prepost.PreAuthorize; - Temporarily disabled for testing
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/deals")
@Tag(name = "Deal", description = "Deal management API")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DealController {

    @Autowired
    private DealService dealService;

    @GetMapping
    @Operation(summary = "Get all deals", description = "Retrieve a list of all deals")
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    // - Temporarily disabled for testing
    public ResponseEntity<List<DealDto>> getAllDeals() {
        List<DealDto> deals = dealService.getAllDeals();
        return new ResponseEntity<>(deals, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get deal by ID", description = "Retrieve a deal by its ID")
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    // - Temporarily disabled for testing
    public ResponseEntity<DealDto> getDealById(@PathVariable Long id) {
        DealDto deal = dealService.getDealById(id);
        return new ResponseEntity<>(deal, HttpStatus.OK);
    }

    @GetMapping("/lead/{leadId}")
    @Operation(summary = "Get deals by lead ID", description = "Retrieve all deals for a specific lead")
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    // - Temporarily disabled for testing
    public ResponseEntity<List<DealDto>> getDealsByLeadId(@PathVariable Long leadId) {
        List<DealDto> deals = dealService.getDealsByLeadId(leadId);
        return new ResponseEntity<>(deals, HttpStatus.OK);
    }

    @GetMapping("/client/{clientId}")
    @Operation(summary = "Get deals by client ID", description = "Retrieve all deals for a specific client")
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    // - Temporarily disabled for testing
    public ResponseEntity<List<DealDto>> getDealsByClientId(@PathVariable Long clientId) {
        List<DealDto> deals = dealService.getDealsByClientId(clientId);
        return new ResponseEntity<>(deals, HttpStatus.OK);
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "Get deals by status", description = "Retrieve all deals with a specific status")
    // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    // - Temporarily disabled for testing
    public ResponseEntity<List<DealDto>> getDealsByStatus(@PathVariable String status) {
        List<DealDto> deals = dealService.getDealsByStatus(status);
        return new ResponseEntity<>(deals, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create deal", description = "Create a new deal")
    // @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')") - Temporarily
    // disabled for testing
    public ResponseEntity<DealDto> createDeal(@Valid @RequestBody DealDto dealDto) {
        DealDto createdDeal = dealService.createDeal(dealDto);
        return new ResponseEntity<>(createdDeal, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update deal", description = "Update an existing deal")
    // @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')") - Temporarily
    // disabled for testing
    public ResponseEntity<DealDto> updateDeal(@PathVariable Long id, @Valid @RequestBody DealDto dealDto) {
        DealDto updatedDeal = dealService.updateDeal(id, dealDto);
        return new ResponseEntity<>(updatedDeal, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete deal", description = "Delete a deal by its ID")
    // @PreAuthorize("hasRole('ADMIN')") - Temporarily disabled for testing
    public ResponseEntity<Void> deleteDeal(@PathVariable Long id) {
        dealService.deleteDeal(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
