package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceMilestoneDto;
import com.redberyl.invoiceapp.service.InvoiceMilestoneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/invoice-milestones")
@Tag(name = "Invoice Milestone", description = "Invoice Milestone management API")
public class InvoiceMilestoneController {

    @Autowired
    private InvoiceMilestoneService invoiceMilestoneService;

    @GetMapping
    @Operation(summary = "Get all invoice milestones", description = "Retrieve a list of all invoice milestones")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceMilestoneDto>> getAllInvoiceMilestones() {
        List<InvoiceMilestoneDto> invoiceMilestones = invoiceMilestoneService.getAllInvoiceMilestones();
        return new ResponseEntity<>(invoiceMilestones, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get invoice milestone by ID", description = "Retrieve an invoice milestone by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceMilestoneDto> getInvoiceMilestoneById(@PathVariable Long id) {
        InvoiceMilestoneDto invoiceMilestone = invoiceMilestoneService.getInvoiceMilestoneById(id);
        return new ResponseEntity<>(invoiceMilestone, HttpStatus.OK);
    }

    @GetMapping("/invoice/{invoiceId}")
    @Operation(summary = "Get invoice milestones by invoice ID", description = "Retrieve all invoice milestones for a specific invoice")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceMilestoneDto>> getInvoiceMilestonesByInvoiceId(@PathVariable Long invoiceId) {
        List<InvoiceMilestoneDto> invoiceMilestones = invoiceMilestoneService.getInvoiceMilestonesByInvoiceId(invoiceId);
        return new ResponseEntity<>(invoiceMilestones, HttpStatus.OK);
    }

    @GetMapping("/due-before")
    @Operation(summary = "Get invoice milestones due before a date", description = "Retrieve all invoice milestones due before a specific date")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceMilestoneDto>> getInvoiceMilestonesByDateBefore(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        List<InvoiceMilestoneDto> invoiceMilestones = invoiceMilestoneService.getInvoiceMilestonesByDateBefore(date);
        return new ResponseEntity<>(invoiceMilestones, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create invoice milestone", description = "Create a new invoice milestone")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceMilestoneDto> createInvoiceMilestone(@Valid @RequestBody InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestoneDto createdInvoiceMilestone = invoiceMilestoneService.createInvoiceMilestone(invoiceMilestoneDto);
        return new ResponseEntity<>(createdInvoiceMilestone, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update invoice milestone", description = "Update an existing invoice milestone")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceMilestoneDto> updateInvoiceMilestone(@PathVariable Long id, @Valid @RequestBody InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestoneDto updatedInvoiceMilestone = invoiceMilestoneService.updateInvoiceMilestone(id, invoiceMilestoneDto);
        return new ResponseEntity<>(updatedInvoiceMilestone, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete invoice milestone", description = "Delete an invoice milestone by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceMilestone(@PathVariable Long id) {
        invoiceMilestoneService.deleteInvoiceMilestone(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
