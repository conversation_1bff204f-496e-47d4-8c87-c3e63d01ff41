package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ReminderDto;
import com.redberyl.invoiceapp.service.ReminderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/reminders")
@Tag(name = "Reminder", description = "Reminder management API")
public class ReminderController {

    @Autowired
    private ReminderService reminderService;

    @GetMapping
    @Operation(summary = "Get all reminders", description = "Retrieve a list of all reminders")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getAllReminders() {
        List<ReminderDto> reminders = reminderService.getAllReminders();
        return new ResponseEntity<>(reminders, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get reminder by ID", description = "Retrieve a reminder by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ReminderDto> getReminderById(@PathVariable Long id) {
        ReminderDto reminder = reminderService.getReminderById(id);
        return new ResponseEntity<>(reminder, HttpStatus.OK);
    }

    @GetMapping("/invoice/{invoiceId}")
    @Operation(summary = "Get reminders by invoice ID", description = "Retrieve all reminders for a specific invoice")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getRemindersByInvoiceId(@PathVariable Long invoiceId) {
        List<ReminderDto> reminders = reminderService.getRemindersByInvoiceId(invoiceId);
        return new ResponseEntity<>(reminders, HttpStatus.OK);
    }

    @GetMapping("/method/{method}")
    @Operation(summary = "Get reminders by method", description = "Retrieve all reminders with a specific method")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getRemindersByMethod(@PathVariable String method) {
        List<ReminderDto> reminders = reminderService.getRemindersByMethod(method);
        return new ResponseEntity<>(reminders, HttpStatus.OK);
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "Get reminders by status", description = "Retrieve all reminders with a specific status")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ReminderDto>> getRemindersByStatus(@PathVariable String status) {
        List<ReminderDto> reminders = reminderService.getRemindersByStatus(status);
        return new ResponseEntity<>(reminders, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create reminder", description = "Create a new reminder")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ReminderDto> createReminder(@Valid @RequestBody ReminderDto reminderDto) {
        ReminderDto createdReminder = reminderService.createReminder(reminderDto);
        return new ResponseEntity<>(createdReminder, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update reminder", description = "Update an existing reminder")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ReminderDto> updateReminder(@PathVariable Long id, @Valid @RequestBody ReminderDto reminderDto) {
        ReminderDto updatedReminder = reminderService.updateReminder(id, reminderDto);
        return new ResponseEntity<>(updatedReminder, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete reminder", description = "Delete a reminder by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteReminder(@PathVariable Long id) {
        reminderService.deleteReminder(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
