package com.redberyl.invoiceapp.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.view.RedirectView;

@Controller
public class SwaggerRedirectController {

    @GetMapping("/swagger")
    public RedirectView redirectToSwaggerUi() {
        return new RedirectView("/api/swagger-ui/index.html");
    }
    
    @GetMapping("/docs")
    public RedirectView redirectToDocs() {
        return new RedirectView("/api/swagger-ui/index.html");
    }
    
    @GetMapping("/api-docs-ui")
    public RedirectView redirectToApiDocsUi() {
        return new RedirectView("/api/swagger-ui/index.html");
    }
}
