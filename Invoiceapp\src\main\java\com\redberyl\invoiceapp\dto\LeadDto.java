package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class LeadDto extends BaseDto {
    private Long id;
    
    @NotBlank(message = "Lead name is required")
    private String name;
    
    @Email(message = "Invalid email format")
    private String email;
    
    private String phone;
    private String source;
    private String status;
    
    // Include the related entity collections
    @Builder.Default
    private Set<DealDto> deals = new HashSet<>();
    
    // Communications relationship removed as requested
}
