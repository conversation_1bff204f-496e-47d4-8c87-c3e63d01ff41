package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class ReminderDto extends BaseDto {
    private Long id;

    @NotNull(message = "Invoice ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long invoiceId;

    // Remove circular reference to avoid compilation issues
    // private InvoiceDto invoice;

    private String method;
    private String status;
    private String note;
}
