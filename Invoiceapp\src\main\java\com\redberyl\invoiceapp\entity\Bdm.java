package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "bdms")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Bdm extends BaseEntity {

    @Id
    @SequenceGenerator(name = "bdm_seq", sequenceName = "bdm_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "bdm_seq")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "email")
    private String email;

    @Column(name = "phone")
    private String phone;

    @Column(name = "gst_number")
    private String gstNumber;

    @Column(name = "billing_address")
    private String billingAddress;

    @OneToMany(mappedBy = "bdm", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<Project> projects = new HashSet<>();

    @OneToMany(mappedBy = "bdm", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<BdmPayment> payments = new HashSet<>();
}
