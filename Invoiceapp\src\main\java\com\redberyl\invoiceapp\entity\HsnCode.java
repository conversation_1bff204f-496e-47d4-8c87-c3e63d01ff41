package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Table(name = "hsn_codes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HsnCode extends BaseEntity {

    @Id
    @SequenceGenerator(name = "hsn_code_seq", sequenceName = "hsn_code_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "hsn_code_seq")
    private Long id;

    @Column(name = "code", nullable = false, unique = true)
    private String code;

    @Column(name = "description")
    private String description;

    @Column(name = "gst_rate", precision = 5, scale = 2)
    private BigDecimal gstRate;
}
