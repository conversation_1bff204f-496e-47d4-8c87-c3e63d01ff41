package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.InvoiceAuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InvoiceAuditLogRepository extends JpaRepository<InvoiceAuditLog, Long> {
    List<InvoiceAuditLog> findByInvoiceId(Long invoiceId);
    List<InvoiceAuditLog> findByPerformedBy(String performedBy);
}
