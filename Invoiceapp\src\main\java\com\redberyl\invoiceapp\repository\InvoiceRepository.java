package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Invoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);
    List<Invoice> findByClientId(Long clientId);
    List<Invoice> findByProjectId(Long projectId);
    List<Invoice> findByCandidateId(Long candidateId);
    List<Invoice> findByInvoiceDateBetween(LocalDate startDate, LocalDate endDate);
    List<Invoice> findByDueDateBefore(LocalDate date);
    List<Invoice> findByIsRecurring(Boolean isRecurring);
    List<Invoice> findByPublishedToFinance(Boolean publishedToFinance);
}
