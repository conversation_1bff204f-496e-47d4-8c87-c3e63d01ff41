package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.CandidateDto;

import java.util.List;

public interface CandidateService {
    List<CandidateDto> getAllCandidates();
    CandidateDto getCandidateById(Long id);
    List<CandidateDto> getCandidatesByClientId(Long clientId);
    List<CandidateDto> getCandidatesByProjectId(Long projectId);
    CandidateDto createCandidate(CandidateDto candidateDto);
    CandidateDto updateCandidate(Long id, CandidateDto candidateDto);
    void deleteCandidate(Long id);
}
