package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.LeadDto;

import java.util.List;

public interface LeadService {
    List<LeadDto> getAllLeads();
    LeadDto getLeadById(Long id);
    List<LeadDto> getLeadsByStatus(String status);
    List<LeadDto> getLeadsBySource(String source);
    LeadDto createLead(LeadDto leadDto);
    LeadDto updateLead(Long id, LeadDto leadDto);
    void deleteLead(Long id);
}
