package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.PaymentDto;

import java.time.LocalDate;
import java.util.List;

public interface PaymentService {
    List<PaymentDto> getAllPayments();
    PaymentDto getPaymentById(Long id);
    List<PaymentDto> getPaymentsByInvoiceId(Long invoiceId);
    List<PaymentDto> getPaymentsByDateRange(LocalDate startDate, LocalDate endDate);
    List<PaymentDto> getPaymentsByPaymentMode(String paymentMode);
    PaymentDto createPayment(PaymentDto paymentDto);
    PaymentDto updatePayment(Long id, PaymentDto paymentDto);
    void deletePayment(Long id);
}
