package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.ProjectDto;

import java.util.List;

public interface ProjectService {
    List<ProjectDto> getAllProjects();
    ProjectDto getProjectById(Long id);
    List<ProjectDto> getProjectsByClientId(Long clientId);
    List<ProjectDto> getProjectsByBdmId(Long bdmId);
    ProjectDto createProject(ProjectDto projectDto);
    ProjectDto updateProject(Long id, ProjectDto projectDto);
    void deleteProject(Long id);
}
