package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Project;
import com.redberyl.invoiceapp.entity.Spoc;
import com.redberyl.invoiceapp.repository.CandidateRepository;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.ProjectRepository;
import com.redberyl.invoiceapp.repository.SpocRepository;
import com.redberyl.invoiceapp.service.CandidateService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CandidateServiceImpl implements CandidateService {

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SpocRepository spocRepository;

    @Override
    public List<CandidateDto> getAllCandidates() {
        return candidateRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CandidateDto getCandidateById(Long id) {
        Candidate candidate = candidateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Candidate not found with id: " + id));
        return convertToDto(candidate);
    }

    @Override
    public List<CandidateDto> getCandidatesByClientId(Long clientId) {
        return candidateRepository.findByClientId(clientId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CandidateDto> getCandidatesByProjectId(Long projectId) {
        return candidateRepository.findByProjectId(projectId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public CandidateDto createCandidate(CandidateDto candidateDto) {
        Candidate candidate = convertToEntity(candidateDto);
        Candidate savedCandidate = candidateRepository.save(candidate);
        return convertToDto(savedCandidate);
    }

    @Override
    @Transactional
    public CandidateDto updateCandidate(Long id, CandidateDto candidateDto) {
        Candidate existingCandidate = candidateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Candidate not found with id: " + id));
        
        updateCandidateFromDto(existingCandidate, candidateDto);
        
        Candidate updatedCandidate = candidateRepository.save(existingCandidate);
        return convertToDto(updatedCandidate);
    }

    @Override
    @Transactional
    public void deleteCandidate(Long id) {
        if (!candidateRepository.existsById(id)) {
            throw new EntityNotFoundException("Candidate not found with id: " + id);
        }
        candidateRepository.deleteById(id);
    }

    private CandidateDto convertToDto(Candidate candidate) {
        CandidateDto dto = new CandidateDto();
        dto.setId(candidate.getId());
        dto.setClientId(candidate.getClient() != null ? candidate.getClient().getId() : null);
        dto.setProjectId(candidate.getProject() != null ? candidate.getProject().getId() : null);
        dto.setName(candidate.getName());
        dto.setJoiningDate(candidate.getJoiningDate());
        dto.setBillingRate(candidate.getBillingRate());
        dto.setDesignation(candidate.getDesignation());
        dto.setPanNo(candidate.getPanNo());
        dto.setAadharNo(candidate.getAadharNo());
        dto.setUanNo(candidate.getUanNo());
        dto.setExperienceInYrs(candidate.getExperienceInYrs());
        dto.setBankAccountNo(candidate.getBankAccountNo());
        dto.setBranchName(candidate.getBranchName());
        dto.setIfscCode(candidate.getIfscCode());
        dto.setAddress(candidate.getAddress());
        dto.setSalaryOffered(candidate.getSalaryOffered());
        dto.setManagerSpocId(candidate.getManagerSpoc() != null ? candidate.getManagerSpoc().getId() : null);
        dto.setAccountHeadSpocId(candidate.getAccountHeadSpoc() != null ? candidate.getAccountHeadSpoc().getId() : null);
        dto.setBusinessHeadSpocId(candidate.getBusinessHeadSpoc() != null ? candidate.getBusinessHeadSpoc().getId() : null);
        dto.setHrSpocId(candidate.getHrSpoc() != null ? candidate.getHrSpoc().getId() : null);
        dto.setFinanceSpocId(candidate.getFinanceSpoc() != null ? candidate.getFinanceSpoc().getId() : null);
        return dto;
    }

    private Candidate convertToEntity(CandidateDto dto) {
        Candidate candidate = new Candidate();
        candidate.setId(dto.getId());
        
        if (dto.getClientId() != null) {
            Client client = clientRepository.findById(dto.getClientId())
                    .orElseThrow(() -> new EntityNotFoundException("Client not found with id: " + dto.getClientId()));
            candidate.setClient(client);
        }
        
        if (dto.getProjectId() != null) {
            Project project = projectRepository.findById(dto.getProjectId())
                    .orElseThrow(() -> new EntityNotFoundException("Project not found with id: " + dto.getProjectId()));
            candidate.setProject(project);
        }
        
        updateCandidateFromDto(candidate, dto);
        
        return candidate;
    }
    
    private void updateCandidateFromDto(Candidate candidate, CandidateDto dto) {
        candidate.setName(dto.getName());
        candidate.setJoiningDate(dto.getJoiningDate());
        candidate.setBillingRate(dto.getBillingRate());
        candidate.setDesignation(dto.getDesignation());
        candidate.setPanNo(dto.getPanNo());
        candidate.setAadharNo(dto.getAadharNo());
        candidate.setUanNo(dto.getUanNo());
        candidate.setExperienceInYrs(dto.getExperienceInYrs());
        candidate.setBankAccountNo(dto.getBankAccountNo());
        candidate.setBranchName(dto.getBranchName());
        candidate.setIfscCode(dto.getIfscCode());
        candidate.setAddress(dto.getAddress());
        candidate.setSalaryOffered(dto.getSalaryOffered());
        
        if (dto.getManagerSpocId() != null) {
            Spoc managerSpoc = spocRepository.findById(dto.getManagerSpocId())
                    .orElseThrow(() -> new EntityNotFoundException("Manager SPOC not found with id: " + dto.getManagerSpocId()));
            candidate.setManagerSpoc(managerSpoc);
        }
        
        if (dto.getAccountHeadSpocId() != null) {
            Spoc accountHeadSpoc = spocRepository.findById(dto.getAccountHeadSpocId())
                    .orElseThrow(() -> new EntityNotFoundException("Account Head SPOC not found with id: " + dto.getAccountHeadSpocId()));
            candidate.setAccountHeadSpoc(accountHeadSpoc);
        }
        
        if (dto.getBusinessHeadSpocId() != null) {
            Spoc businessHeadSpoc = spocRepository.findById(dto.getBusinessHeadSpocId())
                    .orElseThrow(() -> new EntityNotFoundException("Business Head SPOC not found with id: " + dto.getBusinessHeadSpocId()));
            candidate.setBusinessHeadSpoc(businessHeadSpoc);
        }
        
        if (dto.getHrSpocId() != null) {
            Spoc hrSpoc = spocRepository.findById(dto.getHrSpocId())
                    .orElseThrow(() -> new EntityNotFoundException("HR SPOC not found with id: " + dto.getHrSpocId()));
            candidate.setHrSpoc(hrSpoc);
        }
        
        if (dto.getFinanceSpocId() != null) {
            Spoc financeSpoc = spocRepository.findById(dto.getFinanceSpocId())
                    .orElseThrow(() -> new EntityNotFoundException("Finance SPOC not found with id: " + dto.getFinanceSpocId()));
            candidate.setFinanceSpoc(financeSpoc);
        }
    }
}
