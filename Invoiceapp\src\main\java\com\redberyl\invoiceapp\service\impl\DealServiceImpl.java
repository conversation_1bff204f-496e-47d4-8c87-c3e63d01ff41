package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.DealDto;
import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Deal;
import com.redberyl.invoiceapp.entity.Lead;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.DealRepository;
import com.redberyl.invoiceapp.repository.LeadRepository;
import com.redberyl.invoiceapp.service.DealService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DealServiceImpl implements DealService {

    @Autowired
    private DealRepository dealRepository;

    @Autowired
    private LeadRepository leadRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Override
    public List<DealDto> getAllDeals() {
        return dealRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public DealDto getDealById(Long id) {
        Deal deal = dealRepository.findByIdWithLeadAndClient(id)
                .orElseThrow(() -> new EntityNotFoundException("Deal not found with id: " + id));
        return convertToDto(deal);
    }

    @Override
    public List<DealDto> getDealsByLeadId(Long leadId) {
        return dealRepository.findByLeadId(leadId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<DealDto> getDealsByClientId(Long clientId) {
        return dealRepository.findByClientId(clientId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<DealDto> getDealsByStatus(String status) {
        return dealRepository.findByStatus(status).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DealDto createDeal(DealDto dealDto) {
        Deal deal = convertToEntity(dealDto);
        Deal savedDeal = dealRepository.save(deal);
        // Fetch the saved deal with lead and client to ensure they are loaded
        return getDealById(savedDeal.getId());
    }

    @Override
    @Transactional
    public DealDto updateDeal(Long id, DealDto dealDto) {
        Deal existingDeal = dealRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Deal not found with id: " + id));

        updateDealFromDto(existingDeal, dealDto);

        Deal updatedDeal = dealRepository.save(existingDeal);
        // Fetch the updated deal with lead and client to ensure they are loaded
        return getDealById(updatedDeal.getId());
    }

    @Override
    @Transactional
    public void deleteDeal(Long id) {
        if (!dealRepository.existsById(id)) {
            throw new EntityNotFoundException("Deal not found with id: " + id);
        }
        dealRepository.deleteById(id);
    }

    private DealDto convertToDto(Deal deal) {
        // Start building the DTO with common fields
        DealDto.DealDtoBuilder builder = DealDto.builder()
                .id(deal.getId())
                .projectName(deal.getProjectName())
                .valueEstimate(deal.getValueEstimate())
                .expectedClosureDate(deal.getExpectedClosureDate())
                .status(deal.getStatus())
                .notes(deal.getNotes());

        // Set lead ID and lead object if available
        if (deal.getLead() != null) {
            builder.leadId(deal.getLead().getId());

            // Create and set the lead DTO with essential fields only
            LeadDto leadDto = LeadDto.builder()
                    .id(deal.getLead().getId())
                    .name(deal.getLead().getName())
                    .email(deal.getLead().getEmail())
                    .phone(deal.getLead().getPhone())
                    .source(deal.getLead().getSource())
                    .status(deal.getLead().getStatus())
                    .build();

            // Set audit fields for lead
            leadDto.setCreatedAt(deal.getLead().getCreatedAt());
            leadDto.setUpdatedAt(deal.getLead().getModifiedAt());

            builder.lead(leadDto);
        }

        // Set client ID and client object if available
        if (deal.getClient() != null) {
            builder.clientId(deal.getClient().getId());

            // Create and set the client DTO with essential fields only
            ClientDto clientDto = ClientDto.builder()
                    .id(deal.getClient().getId())
                    .name(deal.getClient().getName())
                    .build();

            // Set audit fields for client
            clientDto.setCreatedAt(deal.getClient().getCreatedAt());
            clientDto.setUpdatedAt(deal.getClient().getModifiedAt());

            builder.client(clientDto);
        }

        // Build the DTO
        DealDto dto = builder.build();

        // Set the audit fields from BaseEntity
        dto.setCreatedAt(deal.getCreatedAt());
        dto.setUpdatedAt(deal.getModifiedAt());

        return dto;
    }

    private Deal convertToEntity(DealDto dealDto) {
        Deal deal = new Deal();
        deal.setId(dealDto.getId());

        updateDealFromDto(deal, dealDto);

        return deal;
    }

    private void updateDealFromDto(Deal deal, DealDto dealDto) {
        if (dealDto.getLeadId() != null) {
            Lead lead = leadRepository.findById(dealDto.getLeadId())
                    .orElseThrow(() -> new EntityNotFoundException("Lead not found with id: " + dealDto.getLeadId()));
            deal.setLead(lead);
        }

        if (dealDto.getClientId() != null) {
            Client client = clientRepository.findById(dealDto.getClientId())
                    .orElseThrow(
                            () -> new EntityNotFoundException("Client not found with id: " + dealDto.getClientId()));
            deal.setClient(client);
        }

        deal.setProjectName(dealDto.getProjectName());
        deal.setValueEstimate(dealDto.getValueEstimate());
        deal.setExpectedClosureDate(dealDto.getExpectedClosureDate());
        deal.setStatus(dealDto.getStatus());
        deal.setNotes(dealDto.getNotes());
    }
}

