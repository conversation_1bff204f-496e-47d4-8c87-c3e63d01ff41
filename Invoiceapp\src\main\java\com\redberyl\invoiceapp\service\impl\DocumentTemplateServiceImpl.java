package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;
import com.redberyl.invoiceapp.entity.DocumentTemplate;
import com.redberyl.invoiceapp.repository.DocumentTemplateRepository;
import com.redberyl.invoiceapp.service.DocumentTemplateService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DocumentTemplateServiceImpl implements DocumentTemplateService {

    @Autowired
    private DocumentTemplateRepository documentTemplateRepository;

    @Override
    public List<DocumentTemplateDto> getAllDocumentTemplates() {
        return documentTemplateRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentTemplateDto getDocumentTemplateById(Long id) {
        DocumentTemplate documentTemplate = documentTemplateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Template not found with id: " + id));
        return convertToDto(documentTemplate);
    }

    @Override
    public List<DocumentTemplateDto> getDocumentTemplatesByType(String templateType) {
        return documentTemplateRepository.findByTemplateType(templateType).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DocumentTemplateDto createDocumentTemplate(DocumentTemplateDto documentTemplateDto) {
        DocumentTemplate documentTemplate = convertToEntity(documentTemplateDto);
        DocumentTemplate savedDocumentTemplate = documentTemplateRepository.save(documentTemplate);
        return convertToDto(savedDocumentTemplate);
    }

    @Override
    @Transactional
    public DocumentTemplateDto updateDocumentTemplate(Long id, DocumentTemplateDto documentTemplateDto) {
        DocumentTemplate existingDocumentTemplate = documentTemplateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Template not found with id: " + id));

        existingDocumentTemplate.setName(documentTemplateDto.getName());
        existingDocumentTemplate.setTemplateType(documentTemplateDto.getTemplateType());
        existingDocumentTemplate.setFilePath(documentTemplateDto.getFilePath());

        DocumentTemplate updatedDocumentTemplate = documentTemplateRepository.save(existingDocumentTemplate);
        return convertToDto(updatedDocumentTemplate);
    }

    @Override
    @Transactional
    public void deleteDocumentTemplate(Long id) {
        if (!documentTemplateRepository.existsById(id)) {
            throw new EntityNotFoundException("Document Template not found with id: " + id);
        }
        documentTemplateRepository.deleteById(id);
    }

    private DocumentTemplateDto convertToDto(DocumentTemplate documentTemplate) {
        return DocumentTemplateDto.builder()
                .id(documentTemplate.getId())
                .name(documentTemplate.getName())
                .templateType(documentTemplate.getTemplateType())
                .filePath(documentTemplate.getFilePath())
                .build();
    }

    private DocumentTemplate convertToEntity(DocumentTemplateDto documentTemplateDto) {
        if (documentTemplateDto.getId() != null) {
            // If updating an existing entity, fetch it first to preserve relationships
            return documentTemplateRepository.findById(documentTemplateDto.getId())
                    .map(existingTemplate -> {
                        existingTemplate.setName(documentTemplateDto.getName());
                        existingTemplate.setTemplateType(documentTemplateDto.getTemplateType());
                        existingTemplate.setFilePath(documentTemplateDto.getFilePath());
                        return existingTemplate;
                    })
                    .orElseGet(() -> createNewDocumentTemplate(documentTemplateDto));
        } else {
            return createNewDocumentTemplate(documentTemplateDto);
        }
    }

    private DocumentTemplate createNewDocumentTemplate(DocumentTemplateDto documentTemplateDto) {
        DocumentTemplate template = new DocumentTemplate();
        template.setName(documentTemplateDto.getName());
        template.setTemplateType(documentTemplateDto.getTemplateType());
        template.setFilePath(documentTemplateDto.getFilePath());
        return template;
    }
}
