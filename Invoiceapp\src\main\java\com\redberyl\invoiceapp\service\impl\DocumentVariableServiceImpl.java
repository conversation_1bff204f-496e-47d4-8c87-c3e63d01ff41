package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.DocumentVariableDto;
import com.redberyl.invoiceapp.entity.DocumentTemplateVersion;
import com.redberyl.invoiceapp.entity.DocumentVariable;
import com.redberyl.invoiceapp.repository.DocumentTemplateVersionRepository;
import com.redberyl.invoiceapp.repository.DocumentVariableRepository;
import com.redberyl.invoiceapp.service.DocumentVariableService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DocumentVariableServiceImpl implements DocumentVariableService {

    @Autowired
    private DocumentVariableRepository documentVariableRepository;

    @Autowired
    private DocumentTemplateVersionRepository documentTemplateVersionRepository;

    @Override
    public List<DocumentVariableDto> getAllDocumentVariables() {
        return documentVariableRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentVariableDto getDocumentVariableById(Long id) {
        DocumentVariable documentVariable = documentVariableRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Variable not found with id: " + id));
        return convertToDto(documentVariable);
    }

    @Override
    public List<DocumentVariableDto> getDocumentVariablesByTemplateVersionId(Long templateVersionId) {
        return documentVariableRepository.findByVersionId(templateVersionId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DocumentVariableDto createDocumentVariable(DocumentVariableDto documentVariableDto) {
        DocumentVariable documentVariable = convertToEntity(documentVariableDto);
        DocumentVariable savedDocumentVariable = documentVariableRepository.save(documentVariable);
        return convertToDto(savedDocumentVariable);
    }

    @Override
    @Transactional
    public DocumentVariableDto updateDocumentVariable(Long id, DocumentVariableDto documentVariableDto) {
        DocumentVariable existingDocumentVariable = documentVariableRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Document Variable not found with id: " + id));

        existingDocumentVariable.setVariableName(documentVariableDto.getVariableName());
        existingDocumentVariable.setDescription(documentVariableDto.getDescription());
        existingDocumentVariable.setSampleValue(documentVariableDto.getSampleValue());

        DocumentVariable updatedDocumentVariable = documentVariableRepository.save(existingDocumentVariable);
        return convertToDto(updatedDocumentVariable);
    }

    @Override
    @Transactional
    public void deleteDocumentVariable(Long id) {
        if (!documentVariableRepository.existsById(id)) {
            throw new EntityNotFoundException("Document Variable not found with id: " + id);
        }
        documentVariableRepository.deleteById(id);
    }

    private DocumentVariableDto convertToDto(DocumentVariable documentVariable) {
        return DocumentVariableDto.builder()
                .id(documentVariable.getId())
                .templateVersionId(documentVariable.getVersion().getId())
                .variableName(documentVariable.getVariableName())
                .description(documentVariable.getDescription())
                .sampleValue(documentVariable.getSampleValue())
                .build();
    }

    private DocumentVariable convertToEntity(DocumentVariableDto documentVariableDto) {
        DocumentVariable documentVariable = new DocumentVariable();
        documentVariable.setId(documentVariableDto.getId());

        if (documentVariableDto.getTemplateVersionId() != null) {
            DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository
                    .findById(documentVariableDto.getTemplateVersionId())
                    .orElseThrow(() -> new EntityNotFoundException("Document Template Version not found with id: "
                            + documentVariableDto.getTemplateVersionId()));
            documentVariable.setVersion(documentTemplateVersion);
        }

        documentVariable.setVariableName(documentVariableDto.getVariableName());
        documentVariable.setDescription(documentVariableDto.getDescription());
        documentVariable.setSampleValue(documentVariableDto.getSampleValue());

        return documentVariable;
    }
}
