package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceAuditLogDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.InvoiceAuditLog;
import com.redberyl.invoiceapp.repository.InvoiceAuditLogRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.InvoiceAuditLogService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceAuditLogServiceImpl implements InvoiceAuditLogService {

    @Autowired
    private InvoiceAuditLogRepository invoiceAuditLogRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<InvoiceAuditLogDto> getAllInvoiceAuditLogs() {
        return invoiceAuditLogRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceAuditLogDto getInvoiceAuditLogById(Long id) {
        InvoiceAuditLog invoiceAuditLog = invoiceAuditLogRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Audit Log not found with id: " + id));
        return convertToDto(invoiceAuditLog);
    }

    @Override
    public List<InvoiceAuditLogDto> getInvoiceAuditLogsByInvoiceId(Long invoiceId) {
        return invoiceAuditLogRepository.findByInvoiceId(invoiceId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public InvoiceAuditLogDto createInvoiceAuditLog(InvoiceAuditLogDto invoiceAuditLogDto) {
        InvoiceAuditLog invoiceAuditLog = convertToEntity(invoiceAuditLogDto);
        InvoiceAuditLog savedInvoiceAuditLog = invoiceAuditLogRepository.save(invoiceAuditLog);
        return convertToDto(savedInvoiceAuditLog);
    }

    @Override
    @Transactional
    public void deleteInvoiceAuditLog(Long id) {
        if (!invoiceAuditLogRepository.existsById(id)) {
            throw new EntityNotFoundException("Invoice Audit Log not found with id: " + id);
        }
        invoiceAuditLogRepository.deleteById(id);
    }

    private InvoiceAuditLogDto convertToDto(InvoiceAuditLog invoiceAuditLog) {
        return InvoiceAuditLogDto.builder()
                .id(invoiceAuditLog.getId())
                .invoiceId(invoiceAuditLog.getInvoice() != null ? invoiceAuditLog.getInvoice().getId() : null)
                .action(invoiceAuditLog.getAction())
                .performedBy(invoiceAuditLog.getPerformedBy())
                .build();
    }

    private InvoiceAuditLog convertToEntity(InvoiceAuditLogDto invoiceAuditLogDto) {
        InvoiceAuditLog invoiceAuditLog = new InvoiceAuditLog();
        invoiceAuditLog.setId(invoiceAuditLogDto.getId());
        
        if (invoiceAuditLogDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(invoiceAuditLogDto.getInvoiceId())
                    .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + invoiceAuditLogDto.getInvoiceId()));
            invoiceAuditLog.setInvoice(invoice);
        }
        
        invoiceAuditLog.setAction(invoiceAuditLogDto.getAction());
        invoiceAuditLog.setPerformedBy(invoiceAuditLogDto.getPerformedBy());
        
        return invoiceAuditLog;
    }
}
