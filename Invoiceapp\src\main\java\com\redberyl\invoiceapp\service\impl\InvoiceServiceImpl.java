package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.*;
import com.redberyl.invoiceapp.entity.*;
import com.redberyl.invoiceapp.repository.*;
import com.redberyl.invoiceapp.service.InvoiceService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceServiceImpl implements InvoiceService {

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private InvoiceTypeRepository invoiceTypeRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private StaffingTypeRepository staffingTypeRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private InvoiceAuditLogRepository invoiceAuditLogRepository;

    @Override
    public List<InvoiceDto> getAllInvoices() {
        return invoiceRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceDto getInvoiceById(Long id) {
        Invoice invoice = invoiceRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + id));
        return convertToDto(invoice);
    }

    @Override
    public InvoiceDto getInvoiceByNumber(String invoiceNumber) {
        Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber)
                .orElseThrow(() -> new EntityNotFoundException("Invoice not found with number: " + invoiceNumber));
        return convertToDto(invoice);
    }

    @Override
    public List<InvoiceDto> getInvoicesByClientId(Long clientId) {
        return invoiceRepository.findByClientId(clientId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getInvoicesByProjectId(Long projectId) {
        return invoiceRepository.findByProjectId(projectId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getInvoicesByCandidateId(Long candidateId) {
        return invoiceRepository.findByCandidateId(candidateId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getInvoicesByDateRange(LocalDate startDate, LocalDate endDate) {
        return invoiceRepository.findByInvoiceDateBetween(startDate, endDate).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getOverdueInvoices(LocalDate currentDate) {
        return invoiceRepository.findByDueDateBefore(currentDate).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceDto> getRecurringInvoices(Boolean isRecurring) {
        return invoiceRepository.findByIsRecurring(isRecurring).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public InvoiceDto createInvoice(InvoiceDto invoiceDto) {
        Invoice invoice = convertToEntity(invoiceDto);
        Invoice savedInvoice = invoiceRepository.save(invoice);
        
        // Create audit log entry
        createAuditLog(savedInvoice, "Invoice created", "System");
        
        return convertToDto(savedInvoice);
    }

    @Override
    @Transactional
    public InvoiceDto updateInvoice(Long id, InvoiceDto invoiceDto) {
        Invoice existingInvoice = invoiceRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + id));
        
        updateInvoiceFromDto(existingInvoice, invoiceDto);
        
        Invoice updatedInvoice = invoiceRepository.save(existingInvoice);
        
        // Create audit log entry
        createAuditLog(updatedInvoice, "Invoice updated", "System");
        
        return convertToDto(updatedInvoice);
    }

    @Override
    @Transactional
    public InvoiceDto publishInvoice(Long id) {
        Invoice invoice = invoiceRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + id));
        
        invoice.setPublishedToFinance(true);
        invoice.setPublishedAt(LocalDateTime.now());
        
        Invoice publishedInvoice = invoiceRepository.save(invoice);
        
        // Create audit log entry
        createAuditLog(publishedInvoice, "Invoice published to finance", "System");
        
        return convertToDto(publishedInvoice);
    }

    @Override
    @Transactional
    public void deleteInvoice(Long id) {
        if (!invoiceRepository.existsById(id)) {
            throw new EntityNotFoundException("Invoice not found with id: " + id);
        }
        
        // Create audit log entry before deletion
        Invoice invoice = invoiceRepository.findById(id).get();
        createAuditLog(invoice, "Invoice deleted", "System");
        
        invoiceRepository.deleteById(id);
    }

    private void createAuditLog(Invoice invoice, String action, String actionBy) {
        InvoiceAuditLog auditLog = new InvoiceAuditLog();
        auditLog.setInvoice(invoice);
        auditLog.setAction(action);
        auditLog.setActionBy(actionBy);
        auditLog.setActionDate(LocalDateTime.now());
        
        invoiceAuditLogRepository.save(auditLog);
    }

    private InvoiceDto convertToDto(Invoice invoice) {
        // Start building the DTO with common fields
        InvoiceDto.InvoiceDtoBuilder builder = InvoiceDto.builder()
                .id(invoice.getId())
                .invoiceNumber(invoice.getInvoiceNumber())
                .billingAmount(invoice.getBillingAmount())
                .taxAmount(invoice.getTaxAmount())
                .totalAmount(invoice.getTotalAmount())
                .invoiceDate(invoice.getInvoiceDate())
                .dueDate(invoice.getDueDate())
                .isRecurring(invoice.getIsRecurring())
                .publishedToFinance(invoice.getPublishedToFinance())
                .publishedAt(invoice.getPublishedAt());
        
        // Set client if available
        if (invoice.getClient() != null) {
            builder.clientId(invoice.getClient().getId());
            
            // Create and set the client DTO with essential fields only
            ClientDto clientDto = ClientDto.builder()
                    .id(invoice.getClient().getId())
                    .name(invoice.getClient().getName())
                    .build();
            
            // Set audit fields for client
            clientDto.setCreatedAt(invoice.getClient().getCreatedAt());
            clientDto.setUpdatedAt(invoice.getClient().getModifiedAt());
            
            builder.client(clientDto);
        }
        
        // Set invoice type if available
        if (invoice.getInvoiceType() != null) {
            builder.invoiceTypeId(invoice.getInvoiceType().getId());
            
            // Create and set the invoice type DTO
            InvoiceTypeDto invoiceTypeDto = InvoiceTypeDto.builder()
                    .id(invoice.getInvoiceType().getId())
                    .invoiceType(invoice.getInvoiceType().getInvoiceType())
                    .typeDesc(invoice.getInvoiceType().getTypeDesc())
                    .build();
            
            // Set audit fields for invoice type
            invoiceTypeDto.setCreatedAt(invoice.getInvoiceType().getCreatedAt());
            invoiceTypeDto.setUpdatedAt(invoice.getInvoiceType().getModifiedAt());
            
            builder.invoiceType(invoiceTypeDto);
        }
        
        // Set project if available
        if (invoice.getProject() != null) {
            builder.projectId(invoice.getProject().getId());
            
            // Create and set the project DTO with essential fields only
            ProjectDto projectDto = ProjectDto.builder()
                    .id(invoice.getProject().getId())
                    .name(invoice.getProject().getName())
                    .description(invoice.getProject().getDescription())
                    .build();
            
            // Set audit fields for project
            projectDto.setCreatedAt(invoice.getProject().getCreatedAt());
            projectDto.setUpdatedAt(invoice.getProject().getModifiedAt());
            
            builder.project(projectDto);
        }
        
        // Set candidate if available
        if (invoice.getCandidate() != null) {
            builder.candidateId(invoice.getCandidate().getId());
            
            // Create and set the candidate DTO with essential fields only
            CandidateDto candidateDto = CandidateDto.builder()
                    .id(invoice.getCandidate().getId())
                    .name(invoice.getCandidate().getName())
                    .build();
            
            // Set audit fields for candidate
            candidateDto.setCreatedAt(invoice.getCandidate().getCreatedAt());
            candidateDto.setUpdatedAt(invoice.getCandidate().getModifiedAt());
            
            builder.candidate(candidateDto);
        }
        
        // Set staffing type if available
        if (invoice.getStaffingType() != null) {
            builder.staffingTypeId(invoice.getStaffingType().getId());
            
            // Create and set the staffing type DTO
            StaffingTypeDto staffingTypeDto = StaffingTypeDto.builder()
                    .id(invoice.getStaffingType().getId())
                    .name(invoice.getStaffingType().getName())
                    .build();
            
            // Set audit fields for staffing type
            staffingTypeDto.setCreatedAt(invoice.getStaffingType().getCreatedAt());
            staffingTypeDto.setUpdatedAt(invoice.getStaffingType().getModifiedAt());
            
            builder.staffingType(staffingTypeDto);
        }
        
        // Set HSN code if available
        if (invoice.getHsnCode() != null) {
            builder.hsnId(invoice.getHsnCode().getId());
            
            // Create and set the HSN code DTO
            HsnCodeDto hsnCodeDto = HsnCodeDto.builder()
                    .id(invoice.getHsnCode().getId())
                    .code(invoice.getHsnCode().getCode())
                    .description(invoice.getHsnCode().getDescription())
                    .gstRate(invoice.getHsnCode().getGstRate())
                    .build();
            
            // Set audit fields for HSN code
            hsnCodeDto.setCreatedAt(invoice.getHsnCode().getCreatedAt());
            hsnCodeDto.setUpdatedAt(invoice.getHsnCode().getModifiedAt());
            
            builder.hsnCode(hsnCodeDto);
        }
        
        // Set Redberyl account if available
        if (invoice.getRedberylAccount() != null) {
            builder.redberylAccountId(invoice.getRedberylAccount().getId());
            
            // Create and set the Redberyl account DTO
            RedberylAccountDto redberylAccountDto = RedberylAccountDto.builder()
                    .id(invoice.getRedberylAccount().getId())
                    .accountName(invoice.getRedberylAccount().getAccountName())
                    .accountNo(invoice.getRedberylAccount().getAccountNo())
                    .bankName(invoice.getRedberylAccount().getBankName())
                    .branchName(invoice.getRedberylAccount().getBranchName())
                    .build();
            
            // Set audit fields for Redberyl account
            redberylAccountDto.setCreatedAt(invoice.getRedberylAccount().getCreatedAt());
            redberylAccountDto.setUpdatedAt(invoice.getRedberylAccount().getModifiedAt());
            
            builder.redberylAccount(redberylAccountDto);
        }
        
        // Build the DTO
        InvoiceDto dto = builder.build();
        
        // Set the audit fields
        dto.setCreatedAt(invoice.getCreatedAt());
        dto.setUpdatedAt(invoice.getModifiedAt());
        
        return dto;
    }

    private Invoice convertToEntity(InvoiceDto dto) {
        Invoice invoice = new Invoice();
        invoice.setId(dto.getId());
        invoice.setInvoiceNumber(dto.getInvoiceNumber());
        
        if (dto.getClientId() != null) {
            Client client = clientRepository.findById(dto.getClientId())
                    .orElseThrow(() -> new EntityNotFoundException("Client not found with id: " + dto.getClientId()));
            invoice.setClient(client);
        }
        
        if (dto.getInvoiceTypeId() != null) {
            InvoiceType invoiceType = invoiceTypeRepository.findById(dto.getInvoiceTypeId())
                    .orElseThrow(() -> new EntityNotFoundException("Invoice Type not found with id: " + dto.getInvoiceTypeId()));
            invoice.setInvoiceType(invoiceType);
        }
        
        updateInvoiceFromDto(invoice, dto);
        
        return invoice;
    }
    
    private void updateInvoiceFromDto(Invoice invoice, InvoiceDto dto) {
        if (dto.getProjectId() != null) {
            Project project = projectRepository.findById(dto.getProjectId())
                    .orElseThrow(() -> new EntityNotFoundException("Project not found with id: " + dto.getProjectId()));
            invoice.setProject(project);
        }
        
        if (dto.getCandidateId() != null) {
            Candidate candidate = candidateRepository.findById(dto.getCandidateId())
                    .orElseThrow(() -> new EntityNotFoundException("Candidate not found with id: " + dto.getCandidateId()));
            invoice.setCandidate(candidate);
        }
        
        if (dto.getStaffingTypeId() != null) {
            StaffingType staffingType = staffingTypeRepository.findById(dto.getStaffingTypeId())
                    .orElseThrow(() -> new EntityNotFoundException("Staffing Type not found with id: " + dto.getStaffingTypeId()));
            invoice.setStaffingType(staffingType);
        }
        
        invoice.setBillingAmount(dto.getBillingAmount());
        invoice.setTaxAmount(dto.getTaxAmount());
        invoice.setTotalAmount(dto.getTotalAmount());
        invoice.setInvoiceDate(dto.getInvoiceDate());
        invoice.setDueDate(dto.getDueDate());
        invoice.setIsRecurring(dto.getIsRecurring());
        invoice.setPublishedToFinance(dto.getPublishedToFinance());
        invoice.setPublishedAt(dto.getPublishedAt());
        
        if (dto.getHsnId() != null) {
            HsnCode hsnCode = hsnCodeRepository.findById(dto.getHsnId())
                    .orElseThrow(() -> new EntityNotFoundException("HSN Code not found with id: " + dto.getHsnId()));
            invoice.setHsnCode(hsnCode);
        }
        
        if (dto.getRedberylAccountId() != null) {
            RedberylAccount redberylAccount = redberylAccountRepository.findById(dto.getRedberylAccountId())
                    .orElseThrow(() -> new EntityNotFoundException("Redberyl Account not found with id: " + dto.getRedberylAccountId()));
            invoice.setRedberylAccount(redberylAccount);
        }
    }
}

