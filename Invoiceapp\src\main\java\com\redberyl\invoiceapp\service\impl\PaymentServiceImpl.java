package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.dto.PaymentDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.Payment;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.PaymentRepository;
import com.redberyl.invoiceapp.service.PaymentService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PaymentServiceImpl implements PaymentService {

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<PaymentDto> getAllPayments() {
        List<Payment> payments = paymentRepository.findAll();
        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public PaymentDto getPaymentById(Long id) {
        Payment payment = paymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Payment", "id", id));
        return convertToDto(payment);
    }

    @Override
    public List<PaymentDto> getPaymentsByInvoiceId(Long invoiceId) {
        List<Payment> payments = paymentRepository.findByInvoiceId(invoiceId);
        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentDto> getPaymentsByDateRange(LocalDate startDate, LocalDate endDate) {
        List<Payment> payments = paymentRepository.findByReceivedOnBetween(startDate, endDate);
        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentDto> getPaymentsByPaymentMode(String paymentMode) {
        List<Payment> payments = paymentRepository.findByPaymentMode(paymentMode);
        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public PaymentDto createPayment(PaymentDto paymentDto) {
        Payment payment = convertToEntity(paymentDto);
        Payment savedPayment = paymentRepository.save(payment);
        return convertToDto(savedPayment);
    }

    @Override
    @Transactional
    public PaymentDto updatePayment(Long id, PaymentDto paymentDto) {
        Payment existingPayment = paymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Payment", "id", id));

        if (paymentDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(paymentDto.getInvoiceId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Invoice not found with id: " + paymentDto.getInvoiceId()));
            existingPayment.setInvoice(invoice);
        }

        existingPayment.setAmountReceived(paymentDto.getAmountReceived());
        existingPayment.setReceivedOn(paymentDto.getReceivedOn());
        existingPayment.setPaymentMode(paymentDto.getPaymentMode());
        existingPayment.setReferenceNumber(paymentDto.getReferenceNumber());

        Payment updatedPayment = paymentRepository.save(existingPayment);
        return convertToDto(updatedPayment);
    }

    @Override
    @Transactional
    public void deletePayment(Long id) {
        if (!paymentRepository.existsById(id)) {
            throw new ResourceNotFoundException("Payment", "id", id);
        }
        paymentRepository.deleteById(id);
    }

    private PaymentDto convertToDto(Payment payment) {
        // Start building the DTO with common fields
        PaymentDto.PaymentDtoBuilder builder = PaymentDto.builder()
                .id(payment.getId())
                .amountReceived(payment.getAmountReceived())
                .receivedOn(payment.getReceivedOn())
                .paymentMode(payment.getPaymentMode())
                .referenceNumber(payment.getReferenceNumber());

        // Set invoice if available
        if (payment.getInvoice() != null) {
            builder.invoiceId(payment.getInvoice().getId());

            // Create and set the invoice DTO with essential fields only
            InvoiceDto invoiceDto = InvoiceDto.builder()
                    .id(payment.getInvoice().getId())
                    .invoiceNumber(payment.getInvoice().getInvoiceNumber())
                    .billingAmount(payment.getInvoice().getBillingAmount())
                    .taxAmount(payment.getInvoice().getTaxAmount())
                    .totalAmount(payment.getInvoice().getTotalAmount())
                    .invoiceDate(payment.getInvoice().getInvoiceDate())
                    .dueDate(payment.getInvoice().getDueDate())
                    .build();

            // Set audit fields for invoice
            invoiceDto.setCreatedAt(payment.getInvoice().getCreatedAt());
            invoiceDto.setUpdatedAt(payment.getInvoice().getModifiedAt());

            // Commented out to avoid circular reference
            // builder.invoice(invoiceDto);
        }

        // Build the DTO
        PaymentDto dto = builder.build();

        // Set the audit fields
        dto.setCreatedAt(payment.getCreatedAt());
        dto.setUpdatedAt(payment.getModifiedAt());

        return dto;
    }

    private Payment convertToEntity(PaymentDto dto) {
        Payment payment = new Payment();
        payment.setId(dto.getId());

        if (dto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(dto.getInvoiceId())
                    .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + dto.getInvoiceId()));
            payment.setInvoice(invoice);
        }

        payment.setAmountReceived(dto.getAmountReceived());
        payment.setReceivedOn(dto.getReceivedOn());
        payment.setPaymentMode(dto.getPaymentMode());
        payment.setReferenceNumber(dto.getReferenceNumber());

        return payment;
    }
}

