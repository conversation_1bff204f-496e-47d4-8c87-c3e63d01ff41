package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.RedberylAccountDto;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.service.RedberylAccountService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class RedberylAccountServiceImpl implements RedberylAccountService {

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Override
    public List<RedberylAccountDto> getAllRedberylAccounts() {
        return redberylAccountRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public RedberylAccountDto getRedberylAccountById(Long id) {
        RedberylAccount redberylAccount = redberylAccountRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Redberyl Account not found with id: " + id));
        return convertToDto(redberylAccount);
    }

    @Override
    public RedberylAccountDto getRedberylAccountByAccountNo(String accountNo) {
        RedberylAccount redberylAccount = redberylAccountRepository.findByAccountNo(accountNo)
                .orElseThrow(() -> new EntityNotFoundException("Redberyl Account not found with account number: " + accountNo));
        return convertToDto(redberylAccount);
    }

    @Override
    public RedberylAccountDto getRedberylAccountByGstn(String gstn) {
        RedberylAccount redberylAccount = redberylAccountRepository.findByGstn(gstn)
                .orElseThrow(() -> new EntityNotFoundException("Redberyl Account not found with GSTN: " + gstn));
        return convertToDto(redberylAccount);
    }

    @Override
    public RedberylAccountDto getRedberylAccountByPanNo(String panNo) {
        RedberylAccount redberylAccount = redberylAccountRepository.findByPanNo(panNo)
                .orElseThrow(() -> new EntityNotFoundException("Redberyl Account not found with PAN: " + panNo));
        return convertToDto(redberylAccount);
    }

    @Override
    @Transactional
    public RedberylAccountDto createRedberylAccount(RedberylAccountDto redberylAccountDto) {
        RedberylAccount redberylAccount = convertToEntity(redberylAccountDto);
        RedberylAccount savedRedberylAccount = redberylAccountRepository.save(redberylAccount);
        return convertToDto(savedRedberylAccount);
    }

    @Override
    @Transactional
    public RedberylAccountDto updateRedberylAccount(Long id, RedberylAccountDto redberylAccountDto) {
        RedberylAccount existingRedberylAccount = redberylAccountRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Redberyl Account not found with id: " + id));
        
        updateRedberylAccountFromDto(existingRedberylAccount, redberylAccountDto);
        
        RedberylAccount updatedRedberylAccount = redberylAccountRepository.save(existingRedberylAccount);
        return convertToDto(updatedRedberylAccount);
    }

    @Override
    @Transactional
    public void deleteRedberylAccount(Long id) {
        if (!redberylAccountRepository.existsById(id)) {
            throw new EntityNotFoundException("Redberyl Account not found with id: " + id);
        }
        redberylAccountRepository.deleteById(id);
    }

    private RedberylAccountDto convertToDto(RedberylAccount redberylAccount) {
        RedberylAccountDto dto = new RedberylAccountDto();
        dto.setId(redberylAccount.getId());
        dto.setGlCode(redberylAccount.getGlCode());
        dto.setCostCenter(redberylAccount.getCostCenter());
        dto.setAccountingNotes(redberylAccount.getAccountingNotes());
        dto.setBankName(redberylAccount.getBankName());
        dto.setBranchName(redberylAccount.getBranchName());
        dto.setAccountName(redberylAccount.getAccountName());
        dto.setAccountNo(redberylAccount.getAccountNo());
        dto.setIfscCode(redberylAccount.getIfscCode());
        dto.setAccountType(redberylAccount.getAccountType());
        dto.setGstn(redberylAccount.getGstn());
        dto.setCin(redberylAccount.getCin());
        dto.setPanNo(redberylAccount.getPanNo());
        return dto;
    }

    private RedberylAccount convertToEntity(RedberylAccountDto dto) {
        RedberylAccount redberylAccount = new RedberylAccount();
        redberylAccount.setId(dto.getId());
        updateRedberylAccountFromDto(redberylAccount, dto);
        return redberylAccount;
    }
    
    private void updateRedberylAccountFromDto(RedberylAccount redberylAccount, RedberylAccountDto dto) {
        redberylAccount.setGlCode(dto.getGlCode());
        redberylAccount.setCostCenter(dto.getCostCenter());
        redberylAccount.setAccountingNotes(dto.getAccountingNotes());
        redberylAccount.setBankName(dto.getBankName());
        redberylAccount.setBranchName(dto.getBranchName());
        redberylAccount.setAccountName(dto.getAccountName());
        redberylAccount.setAccountNo(dto.getAccountNo());
        redberylAccount.setIfscCode(dto.getIfscCode());
        redberylAccount.setAccountType(dto.getAccountType());
        redberylAccount.setGstn(dto.getGstn());
        redberylAccount.setCin(dto.getCin());
        redberylAccount.setPanNo(dto.getPanNo());
    }
}
