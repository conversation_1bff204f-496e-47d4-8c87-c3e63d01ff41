package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ReminderDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.Reminder;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.ReminderRepository;
import com.redberyl.invoiceapp.service.ReminderService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ReminderServiceImpl implements ReminderService {

    @Autowired
    private ReminderRepository reminderRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<ReminderDto> getAllReminders() {
        return reminderRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ReminderDto getReminderById(Long id) {
        Reminder reminder = reminderRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Reminder not found with id: " + id));
        return convertToDto(reminder);
    }

    @Override
    public List<ReminderDto> getRemindersByInvoiceId(Long invoiceId) {
        return reminderRepository.findByInvoiceId(invoiceId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ReminderDto> getRemindersByMethod(String method) {
        return reminderRepository.findByMethod(method).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ReminderDto> getRemindersByStatus(String status) {
        return reminderRepository.findByStatus(status).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public ReminderDto createReminder(ReminderDto reminderDto) {
        Reminder reminder = convertToEntity(reminderDto);
        Reminder savedReminder = reminderRepository.save(reminder);
        return convertToDto(savedReminder);
    }

    @Override
    @Transactional
    public ReminderDto updateReminder(Long id, ReminderDto reminderDto) {
        Reminder existingReminder = reminderRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Reminder not found with id: " + id));
        
        existingReminder.setMethod(reminderDto.getMethod());
        existingReminder.setStatus(reminderDto.getStatus());
        existingReminder.setNote(reminderDto.getNote());
        
        Reminder updatedReminder = reminderRepository.save(existingReminder);
        return convertToDto(updatedReminder);
    }

    @Override
    @Transactional
    public void deleteReminder(Long id) {
        if (!reminderRepository.existsById(id)) {
            throw new EntityNotFoundException("Reminder not found with id: " + id);
        }
        reminderRepository.deleteById(id);
    }

    private ReminderDto convertToDto(Reminder reminder) {
        return ReminderDto.builder()
                .id(reminder.getId())
                .invoiceId(reminder.getInvoice().getId())
                .method(reminder.getMethod())
                .status(reminder.getStatus())
                .note(reminder.getNote())
                .build();
    }

    private Reminder convertToEntity(ReminderDto reminderDto) {
        Reminder reminder = new Reminder();
        reminder.setId(reminderDto.getId());
        
        if (reminderDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(reminderDto.getInvoiceId())
                    .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + reminderDto.getInvoiceId()));
            reminder.setInvoice(invoice);
        }
        
        reminder.setMethod(reminderDto.getMethod());
        reminder.setStatus(reminderDto.getStatus());
        reminder.setNote(reminderDto.getNote());
        
        return reminder;
    }
}
