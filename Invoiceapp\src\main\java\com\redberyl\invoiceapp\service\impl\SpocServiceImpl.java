package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.SpocDto;
import com.redberyl.invoiceapp.entity.Spoc;
import com.redberyl.invoiceapp.repository.SpocRepository;
import com.redberyl.invoiceapp.service.SpocService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class SpocServiceImpl implements SpocService {

    @Autowired
    private SpocRepository spocRepository;

    @Override
    public List<SpocDto> getAllSpocs() {
        return spocRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public SpocDto getSpocById(Long id) {
        Spoc spoc = spocRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("SPOC not found with id: " + id));
        return convertToDto(spoc);
    }

    @Override
    public SpocDto getSpocByEmailId(String emailId) {
        Spoc spoc = spocRepository.findByEmailId(emailId)
                .orElseThrow(() -> new EntityNotFoundException("SPOC not found with email: " + emailId));
        return convertToDto(spoc);
    }

    @Override
    @Transactional
    public SpocDto createSpoc(SpocDto spocDto) {
        Spoc spoc = convertToEntity(spocDto);
        Spoc savedSpoc = spocRepository.save(spoc);
        return convertToDto(savedSpoc);
    }

    @Override
    @Transactional
    public SpocDto updateSpoc(Long id, SpocDto spocDto) {
        Spoc existingSpoc = spocRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("SPOC not found with id: " + id));
        
        existingSpoc.setName(spocDto.getName());
        existingSpoc.setEmailId(spocDto.getEmailId());
        existingSpoc.setContactNo(spocDto.getContactNo());
        
        Spoc updatedSpoc = spocRepository.save(existingSpoc);
        return convertToDto(updatedSpoc);
    }

    @Override
    @Transactional
    public void deleteSpoc(Long id) {
        if (!spocRepository.existsById(id)) {
            throw new EntityNotFoundException("SPOC not found with id: " + id);
        }
        spocRepository.deleteById(id);
    }

    private SpocDto convertToDto(Spoc spoc) {
        return SpocDto.builder()
                .id(spoc.getId())
                .name(spoc.getName())
                .emailId(spoc.getEmailId())
                .contactNo(spoc.getContactNo())
                .build();
    }

    private Spoc convertToEntity(SpocDto spocDto) {
        return Spoc.builder()
                .id(spocDto.getId())
                .name(spocDto.getName())
                .emailId(spocDto.getEmailId())
                .contactNo(spocDto.getContactNo())
                .build();
    }
}
