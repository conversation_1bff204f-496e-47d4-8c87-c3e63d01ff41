package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.entity.TaxRate;
import com.redberyl.invoiceapp.entity.TaxType;
import com.redberyl.invoiceapp.repository.TaxRateRepository;
import com.redberyl.invoiceapp.repository.TaxTypeRepository;
import com.redberyl.invoiceapp.service.TaxRateService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TaxRateServiceImpl implements TaxRateService {

    @Autowired
    private TaxRateRepository taxRateRepository;

    @Autowired
    private TaxTypeRepository taxTypeRepository;

    @Override
    public List<TaxRateDto> getAllTaxRates() {
        return taxRateRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public TaxRateDto getTaxRateById(Long id) {
        TaxRate taxRate = taxRateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Tax Rate not found with id: " + id));
        return convertToDto(taxRate);
    }

    @Override
    public List<TaxRateDto> getTaxRatesByTaxTypeId(Long taxTypeId) {
        return taxRateRepository.findByTaxTypeId(taxTypeId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<TaxRateDto> getTaxRatesEffectiveOnDate(LocalDate date) {
        return taxRateRepository.findByEffectiveFromLessThanEqualAndEffectiveToGreaterThanEqual(date, date).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TaxRateDto createTaxRate(TaxRateDto taxRateDto) {
        TaxRate taxRate = convertToEntity(taxRateDto);
        TaxRate savedTaxRate = taxRateRepository.save(taxRate);
        return convertToDto(savedTaxRate);
    }

    @Override
    @Transactional
    public TaxRateDto updateTaxRate(Long id, TaxRateDto taxRateDto) {
        TaxRate existingTaxRate = taxRateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Tax Rate not found with id: " + id));

        // Handle tax type change if needed
        if (taxRateDto.getTaxTypeId() != null &&
                (existingTaxRate.getTaxType() == null ||
                        !existingTaxRate.getTaxType().getId().equals(taxRateDto.getTaxTypeId()))) {

            // Remove from old tax type if exists
            if (existingTaxRate.getTaxType() != null) {
                existingTaxRate.getTaxType().removeTaxRate(existingTaxRate);
            }

            // Add to new tax type
            TaxType newTaxType = taxTypeRepository.findById(taxRateDto.getTaxTypeId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Tax Type not found with id: " + taxRateDto.getTaxTypeId()));
            newTaxType.addTaxRate(existingTaxRate);
        }

        existingTaxRate.setRate(taxRateDto.getRate());
        existingTaxRate.setEffectiveFrom(taxRateDto.getEffectiveFrom());
        existingTaxRate.setEffectiveTo(taxRateDto.getEffectiveTo());

        TaxRate updatedTaxRate = taxRateRepository.save(existingTaxRate);
        return convertToDto(updatedTaxRate);
    }

    @Override
    @Transactional
    public void deleteTaxRate(Long id) {
        TaxRate taxRate = taxRateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Tax Rate not found with id: " + id));

        // Properly remove from the parent entity to maintain the relationship
        if (taxRate.getTaxType() != null) {
            taxRate.getTaxType().removeTaxRate(taxRate);
        }

        taxRateRepository.delete(taxRate);
    }

    private TaxRateDto convertToDto(TaxRate taxRate) {
        // Start building the DTO with common fields
        TaxRateDto.TaxRateDtoBuilder builder = TaxRateDto.builder()
                .id(taxRate.getId())
                .rate(taxRate.getRate())
                .effectiveFrom(taxRate.getEffectiveFrom())
                .effectiveTo(taxRate.getEffectiveTo());

        // Set tax type if available
        if (taxRate.getTaxType() != null) {
            builder.taxTypeId(taxRate.getTaxType().getId());

            // Create and set the tax type DTO
            TaxTypeDto taxTypeDto = TaxTypeDto.builder()
                    .id(taxRate.getTaxType().getId())
                    .taxType(taxRate.getTaxType().getTaxType())
                    .taxTypeDescription(taxRate.getTaxType().getTaxTypeDescription())
                    .build();

            // Set audit fields for tax type
            taxTypeDto.setCreatedAt(taxRate.getTaxType().getCreatedAt());
            taxTypeDto.setUpdatedAt(taxRate.getTaxType().getModifiedAt());

            builder.taxType(taxTypeDto);
        }

        // Build the DTO
        TaxRateDto dto = builder.build();

        // Set the audit fields
        dto.setCreatedAt(taxRate.getCreatedAt());
        dto.setUpdatedAt(taxRate.getModifiedAt());

        return dto;
    }

    private TaxRate convertToEntity(TaxRateDto taxRateDto) {
        TaxRate taxRate = new TaxRate();
        taxRate.setId(taxRateDto.getId());

        if (taxRateDto.getTaxTypeId() != null) {
            TaxType taxType = taxTypeRepository.findById(taxRateDto.getTaxTypeId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Tax Type not found with id: " + taxRateDto.getTaxTypeId()));

            // Use the helper method to maintain the bidirectional relationship
            taxType.addTaxRate(taxRate);
        }

        taxRate.setRate(taxRateDto.getRate());
        taxRate.setEffectiveFrom(taxRateDto.getEffectiveFrom());
        taxRate.setEffectiveTo(taxRateDto.getEffectiveTo());

        return taxRate;
    }
}

