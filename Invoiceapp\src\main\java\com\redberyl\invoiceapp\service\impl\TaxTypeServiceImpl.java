package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.entity.TaxType;
import com.redberyl.invoiceapp.repository.TaxTypeRepository;
import com.redberyl.invoiceapp.service.TaxTypeService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TaxTypeServiceImpl implements TaxTypeService {

    @Autowired
    private TaxTypeRepository taxTypeRepository;

    @Override
    public List<TaxTypeDto> getAllTaxTypes() {
        return taxTypeRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public TaxTypeDto getTaxTypeById(Long id) {
        TaxType taxType = taxTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Tax Type not found with id: " + id));
        return convertToDto(taxType);
    }

    @Override
    public TaxTypeDto getTaxTypeByType(String taxType) {
        TaxType type = taxTypeRepository.findByTaxType(taxType)
                .orElseThrow(() -> new EntityNotFoundException("Tax Type not found with type: " + taxType));
        return convertToDto(type);
    }

    @Override
    @Transactional
    public TaxTypeDto createTaxType(TaxTypeDto taxTypeDto) {
        TaxType taxType = convertToEntity(taxTypeDto);
        TaxType savedTaxType = taxTypeRepository.save(taxType);
        return convertToDto(savedTaxType);
    }

    @Override
    @Transactional
    public TaxTypeDto updateTaxType(Long id, TaxTypeDto taxTypeDto) {
        TaxType existingTaxType = taxTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Tax Type not found with id: " + id));

        existingTaxType.setTaxType(taxTypeDto.getTaxType());
        existingTaxType.setTaxTypeDescription(taxTypeDto.getTaxTypeDescription());

        TaxType updatedTaxType = taxTypeRepository.save(existingTaxType);
        return convertToDto(updatedTaxType);
    }

    @Override
    @Transactional
    public void deleteTaxType(Long id) {
        TaxType taxType = taxTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Tax Type not found with id: " + id));

        // Clear the tax rates collection to avoid orphan issues
        taxType.getTaxRates().clear();

        taxTypeRepository.delete(taxType);
    }

    private TaxTypeDto convertToDto(TaxType taxType) {
        TaxTypeDto dto = TaxTypeDto.builder()
                .id(taxType.getId())
                .taxType(taxType.getTaxType())
                .taxTypeDescription(taxType.getTaxTypeDescription())
                .build();

        // Set the audit fields
        dto.setCreatedAt(taxType.getCreatedAt());
        dto.setUpdatedAt(taxType.getModifiedAt());

        return dto;
    }

    private TaxType convertToEntity(TaxTypeDto taxTypeDto) {
        // If updating an existing tax type, fetch it first to preserve relationships
        if (taxTypeDto.getId() != null) {
            TaxType existingTaxType = taxTypeRepository.findById(taxTypeDto.getId())
                    .orElse(new TaxType());

            existingTaxType.setTaxType(taxTypeDto.getTaxType());
            existingTaxType.setTaxTypeDescription(taxTypeDto.getTaxTypeDescription());

            return existingTaxType;
        } else {
            // For new tax types
            return TaxType.builder()
                    .id(taxTypeDto.getId())
                    .taxType(taxTypeDto.getTaxType())
                    .taxTypeDescription(taxTypeDto.getTaxTypeDescription())
                    .taxRates(new java.util.HashSet<>())
                    .build();
        }
    }
}

