-- Core Entities
CREATE TABLE clients (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE hsn_codes (
    id BIGSERIAL PRIMARY KEY,
    code VA<PERSON>HA<PERSON>(20) UNIQUE NOT NULL,
    description TEXT,
    gst_rate NUMERIC(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE bdms (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    gst_number VARCHAR(50),
    billing_address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE spocs (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email_id VARCHAR(255) NOT NULL,
    contact_no VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE staffing_types (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE projects (
    id BIGSERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(id),
    name VARCHAR(255) NOT NULL,
    hsn_code_id INT REFERENCES hsn_codes(id),
    description TEXT,
    email VARCHAR(255),
    phone VARCHAR(20),
    gst_number VARCHAR(50),
    billing_address TEXT,
    shipping_address TEXT,
    engagement_code VARCHAR(100),
    client_partner_name VARCHAR(255),
    client_partner_email VARCHAR(255),
    client_partner_phone VARCHAR(20),
    bdm_id INT REFERENCES bdms(id),
    commission_percentage NUMERIC(5,2),
    commission_amount NUMERIC(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE candidates (
    id BIGSERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(id),
    project_id INT REFERENCES projects(id),
    name VARCHAR(255) NOT NULL,
    joining_date TIMESTAMP,
    billing_rate NUMERIC(10,2),
    designation VARCHAR(50),
    pan_no VARCHAR(10),
    aadhar_no VARCHAR(16),
    uan_no VARCHAR(25),
    experience_in_yrs NUMERIC(10,2),
    bank_account_no VARCHAR(25),
    branch_name VARCHAR(50),
    ifsc_code VARCHAR(10),
    address TEXT,
    salary_offered NUMERIC(10,2),
    manager_spoc_id INT REFERENCES spocs(id),
    account_head_spoc_id INT REFERENCES spocs(id),
    business_head_spoc_id INT REFERENCES spocs(id),
    hr_spoc_id INT REFERENCES spocs(id),
    finance_spoc_id INT REFERENCES spocs(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Invoice & Payment Tracking
CREATE TABLE invoice_types (
    id BIGSERIAL PRIMARY KEY,
    invoice_type VARCHAR(50) UNIQUE NOT NULL,
    type_desc VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE redberyl_accounts (
    id BIGSERIAL PRIMARY KEY,
    gl_code VARCHAR(50),
    cost_center VARCHAR(50),
    accounting_notes TEXT,
    bank_name VARCHAR(50),
    branch_name VARCHAR(50),
    account_name VARCHAR(50),
    account_no VARCHAR(50),
    ifsc_code VARCHAR(50),
    account_type VARCHAR(50),
    gstn VARCHAR(50),
    cin VARCHAR(50),
    pan_no VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE invoices (
    id BIGSERIAL PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    client_id INT REFERENCES clients(id),
    invoice_type_id INT REFERENCES invoice_types(id),
    project_id INT REFERENCES projects(id),
    candidate_id INT REFERENCES candidates(id),
    staffing_type_id INT REFERENCES staffing_types(id),
    billing_amount NUMERIC(12,2) NOT NULL,
    tax_amount NUMERIC(12,2) NOT NULL,
    total_amount NUMERIC(12,2) NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    is_recurring BOOLEAN DEFAULT FALSE,
    published_to_finance BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP,
    hsn_id INT REFERENCES hsn_codes(id),
    redberyl_account_id INT REFERENCES redberyl_accounts(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE invoice_milestones (
    id BIGSERIAL PRIMARY KEY,
    invoice_id INT REFERENCES invoices(id) ON DELETE CASCADE,
    description TEXT,
    amount NUMERIC(12,2),
    milestone_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE payments (
    id BIGSERIAL PRIMARY KEY,
    invoice_id INT REFERENCES invoices(id) ON DELETE CASCADE,
    amount_received NUMERIC(12,2),
    received_on DATE,
    payment_mode VARCHAR(50),
    reference_number VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE bdm_payments (
    id BIGSERIAL PRIMARY KEY,
    invoice_id INT REFERENCES invoices(id) ON DELETE CASCADE,
    bdm_id INT REFERENCES bdms(id),
    amount NUMERIC(10,2),
    paid_on DATE,
    is_paid BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE reminders (
    id BIGSERIAL PRIMARY KEY,
    invoice_id INT REFERENCES invoices(id) ON DELETE CASCADE,
    method VARCHAR(50),
    status VARCHAR(50) DEFAULT 'sent',
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE invoice_audit_log (
    id BIGSERIAL PRIMARY KEY,
    invoice_id INT REFERENCES invoices(id),
    action VARCHAR(255),
    performed_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Tax & Accounting Structure
CREATE TABLE tax_types (
    id BIGSERIAL PRIMARY KEY,
    tax_type VARCHAR(50),
    tax_type_description VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE tax_rates (
    id BIGSERIAL PRIMARY KEY,
    tax_type_id INT REFERENCES tax_types(id),
    rate NUMERIC(5,2),
    effective_from DATE,
    effective_to DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE invoice_taxes (
    id BIGSERIAL PRIMARY KEY,
    invoice_id INT REFERENCES invoices(id) ON DELETE CASCADE,
    tax_rate_id INT REFERENCES tax_rates(id),
    amount NUMERIC(12,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- CRM Module
CREATE TABLE leads (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    source VARCHAR(100),
    status VARCHAR(50) DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE deals (
    id BIGSERIAL PRIMARY KEY,
    lead_id INT REFERENCES leads(id) ON DELETE CASCADE,
    client_id INT REFERENCES clients(id),
    project_name VARCHAR(255),
    value_estimate NUMERIC(12,2),
    expected_closure_date DATE,
    status VARCHAR(50) DEFAULT 'open',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE communications (
    id BIGSERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(id),
    lead_id INT REFERENCES leads(id),
    deal_id INT REFERENCES deals(id),
    subject VARCHAR(255),
    method VARCHAR(50),
    content TEXT,
    follow_up_date DATE,
    logged_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Document Management
CREATE TABLE document_templates (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100),
    template_type VARCHAR(50),
    file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE document_template_versions (
    id BIGSERIAL PRIMARY KEY,
    template_id INT REFERENCES document_templates(id) ON DELETE CASCADE,
    version_number INT NOT NULL,
    content TEXT NOT NULL,
    created_by VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE document_variables (
    id BIGSERIAL PRIMARY KEY,
    template_version_id INT REFERENCES document_template_versions(id) ON DELETE CASCADE,
    variable_name VARCHAR(100),
    description TEXT,
    sample_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE generated_documents (
    id BIGSERIAL PRIMARY KEY,
    template_id INT REFERENCES document_templates(id),
    version_id INT REFERENCES document_template_versions(id),
    client_id INT REFERENCES clients(id),
    deal_id INT REFERENCES deals(id),
    file_path TEXT,
    filled_content TEXT,
    status VARCHAR(50) DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);
