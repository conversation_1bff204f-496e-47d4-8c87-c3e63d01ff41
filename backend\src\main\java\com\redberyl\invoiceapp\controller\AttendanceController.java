package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.AttendanceDto;
import com.redberyl.invoiceapp.service.AttendanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/attendance")
@Tag(name = "Attendance", description = "Candidate attendance management API")
public class AttendanceController {

    @Autowired
    private AttendanceService attendanceService;

    @PostMapping
    @Operation(summary = "Create or update attendance", description = "Create or update attendance record for a candidate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attendance record created/updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "404", description = "Candidate not found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<AttendanceDto> createOrUpdateAttendance(@RequestBody AttendanceDto attendanceDto) {
        AttendanceDto savedAttendance = attendanceService.createOrUpdateAttendance(attendanceDto);
        return new ResponseEntity<>(savedAttendance, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get attendance by ID", description = "Get attendance record by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attendance record found"),
            @ApiResponse(responseCode = "404", description = "Attendance record not found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<AttendanceDto> getAttendanceById(@PathVariable Long id) {
        AttendanceDto attendance = attendanceService.getAttendanceById(id);
        return new ResponseEntity<>(attendance, HttpStatus.OK);
    }

    @GetMapping("/candidate/{candidateId}")
    @Operation(summary = "Get attendance by candidate", description = "Get all attendance records for a candidate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attendance records found"),
            @ApiResponse(responseCode = "404", description = "Candidate not found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceDto>> getAttendanceByCandidate(@PathVariable Long candidateId) {
        List<AttendanceDto> attendanceList = attendanceService.getAttendanceByCandidate(candidateId);
        return new ResponseEntity<>(attendanceList, HttpStatus.OK);
    }

    @GetMapping("/candidate/{candidateId}/month/{month}/year/{year}")
    @Operation(summary = "Get attendance by candidate, month and year", description = "Get specific attendance record")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attendance record found"),
            @ApiResponse(responseCode = "404", description = "Attendance record not found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<AttendanceDto> getAttendanceByCandidateMonthYear(
            @PathVariable Long candidateId,
            @PathVariable Integer month,
            @PathVariable Integer year) {
        AttendanceDto attendance = attendanceService.getAttendanceByCandidateMonthYear(candidateId, month, year);
        return new ResponseEntity<>(attendance, HttpStatus.OK);
    }

    @GetMapping("/month/{month}/year/{year}")
    @Operation(summary = "Get attendance by month and year", description = "Get all attendance records for a specific month and year")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attendance records found"),
            @ApiResponse(responseCode = "204", description = "No attendance records found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceDto>> getAttendanceByMonthYear(
            @PathVariable Integer month,
            @PathVariable Integer year) {
        List<AttendanceDto> attendanceList = attendanceService.getAttendanceByMonthYear(month, year);
        return new ResponseEntity<>(attendanceList, HttpStatus.OK);
    }

    @GetMapping("/candidate/{candidateId}/year/{year}")
    @Operation(summary = "Get attendance by candidate and year", description = "Get attendance records for a candidate in a specific year")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attendance records found"),
            @ApiResponse(responseCode = "404", description = "Candidate not found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceDto>> getAttendanceByCandidateYear(
            @PathVariable Long candidateId,
            @PathVariable Integer year) {
        List<AttendanceDto> attendanceList = attendanceService.getAttendanceByCandidateYear(candidateId, year);
        return new ResponseEntity<>(attendanceList, HttpStatus.OK);
    }

    @PostMapping("/calculate")
    @Operation(summary = "Calculate salary", description = "Calculate salary based on attendance days")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Salary calculated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "404", description = "Candidate not found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<AttendanceDto> calculateSalary(
            @RequestParam Long candidateId,
            @RequestParam Integer month,
            @RequestParam Integer year,
            @RequestParam Integer daysWorked) {
        AttendanceDto attendance = attendanceService.calculateSalary(candidateId, month, year, daysWorked);
        return new ResponseEntity<>(attendance, HttpStatus.OK);
    }

    @GetMapping("/candidate/{candidateId}/latest")
    @Operation(summary = "Get latest attendance", description = "Get latest attendance record for a candidate")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Latest attendance record found"),
            @ApiResponse(responseCode = "404", description = "No attendance records found for candidate")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<AttendanceDto> getLatestAttendanceByCandidate(@PathVariable Long candidateId) {
        AttendanceDto attendance = attendanceService.getLatestAttendanceByCandidate(candidateId);
        if (attendance != null) {
            return new ResponseEntity<>(attendance, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping
    @Operation(summary = "Get all attendance records", description = "Get all attendance records")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Attendance records found"),
            @ApiResponse(responseCode = "204", description = "No attendance records found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceDto>> getAllAttendance() {
        List<AttendanceDto> attendanceList = attendanceService.getAllAttendance();
        return new ResponseEntity<>(attendanceList, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete attendance", description = "Delete attendance record by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Attendance record deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Attendance record not found")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<Void> deleteAttendance(@PathVariable Long id) {
        attendanceService.deleteAttendance(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
