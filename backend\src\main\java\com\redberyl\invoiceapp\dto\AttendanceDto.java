package com.redberyl.invoiceapp.dto;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendanceDto {
    
    private Long id;
    private Long candidateId;
    private String candidateName;
    private Integer month;
    private Integer year;
    private Integer daysWorked;
    private BigDecimal dailyRate;
    private BigDecimal calculatedSalary;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Additional fields for display purposes
    private String monthName;
    private String clientName;
    private String projectName;
}
