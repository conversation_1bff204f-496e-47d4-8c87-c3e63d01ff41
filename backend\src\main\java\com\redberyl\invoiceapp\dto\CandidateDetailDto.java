package com.redberyl.invoiceapp.dto;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CandidateDetailDto {
    
    private Long id;
    private String name;
    private String designation;
    private String panNo;
    private String aadharNo;
    private String uanNo;
    private BigDecimal experienceInYrs;
    private String bankAccountNo;
    private String branchName;
    private String ifscCode;
    private String address;
    private BigDecimal salaryOffered;
    private BigDecimal billingRate;
    private LocalDateTime joiningDate;
    
    // Client information
    private Long clientId;
    private String clientName;
    
    // Project information
    private Long projectId;
    private String projectName;
    
    // SPOC information
    private Long managerSpocId;
    private String managerSpocName;
    private Long hrSpocId;
    private String hrSpocName;
    private Long financeSpocId;
    private String financeSpocName;
    
    // Calculated daily rate (salary / 30 days)
    private BigDecimal dailyRate;
    
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updatedAt;
}
