package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.CandidateAttendance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CandidateAttendanceRepository extends JpaRepository<CandidateAttendance, Long> {
    
    /**
     * Find attendance record by candidate, month and year
     */
    Optional<CandidateAttendance> findByCandidateIdAndMonthAndYear(Long candidateId, Integer month, Integer year);
    
    /**
     * Find all attendance records for a candidate
     */
    List<CandidateAttendance> findByCandidateIdOrderByYearDescMonthDesc(Long candidateId);
    
    /**
     * Find all attendance records for a specific month and year
     */
    List<CandidateAttendance> findByMonthAndYearOrderByCandidateId(Integer month, Integer year);
    
    /**
     * Find attendance records for a candidate in a specific year
     */
    List<CandidateAttendance> findByCandidateIdAndYearOrderByMonthDesc(Long candidateId, Integer year);
    
    /**
     * Check if attendance record exists for candidate in specific month/year
     */
    boolean existsByCandidateIdAndMonthAndYear(Long candidateId, Integer month, Integer year);
    
    /**
     * Get attendance records with candidate details
     */
    @Query("SELECT ca FROM CandidateAttendance ca " +
           "JOIN FETCH ca.candidate c " +
           "LEFT JOIN FETCH c.client " +
           "LEFT JOIN FETCH c.project " +
           "WHERE ca.month = :month AND ca.year = :year " +
           "ORDER BY c.name")
    List<CandidateAttendance> findAttendanceWithCandidateDetails(@Param("month") Integer month, @Param("year") Integer year);
    
    /**
     * Get latest attendance record for a candidate
     */
    @Query("SELECT ca FROM CandidateAttendance ca " +
           "WHERE ca.candidate.id = :candidateId " +
           "ORDER BY ca.year DESC, ca.month DESC " +
           "LIMIT 1")
    Optional<CandidateAttendance> findLatestAttendanceByCandidateId(@Param("candidateId") Long candidateId);
}
