package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.AttendanceDto;
import java.util.List;

public interface AttendanceService {
    
    /**
     * Create or update attendance record for a candidate
     */
    AttendanceDto createOrUpdateAttendance(AttendanceDto attendanceDto);
    
    /**
     * Get attendance record by ID
     */
    AttendanceDto getAttendanceById(Long id);
    
    /**
     * Get attendance record by candidate, month and year
     */
    AttendanceDto getAttendanceByCandidateMonthYear(Long candidateId, Integer month, Integer year);
    
    /**
     * Get all attendance records for a candidate
     */
    List<AttendanceDto> getAttendanceByCandidate(Long candidateId);
    
    /**
     * Get all attendance records for a specific month and year
     */
    List<AttendanceDto> getAttendanceByMonthYear(Integer month, Integer year);
    
    /**
     * Get attendance records for a candidate in a specific year
     */
    List<AttendanceDto> getAttendanceByCandidateYear(Long candidateId, Integer year);
    
    /**
     * Delete attendance record
     */
    void deleteAttendance(Long id);
    
    /**
     * Calculate salary based on attendance days and daily rate
     */
    AttendanceDto calculateSalary(Long candidateId, Integer month, Integer year, Integer daysWorked);
    
    /**
     * Get latest attendance record for a candidate (for auto-population)
     */
    AttendanceDto getLatestAttendanceByCandidate(Long candidateId);
    
    /**
     * Get all attendance records with pagination
     */
    List<AttendanceDto> getAllAttendance();
}
