package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.AttendanceDto;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.entity.CandidateAttendance;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.CandidateAttendanceRepository;
import com.redberyl.invoiceapp.repository.CandidateRepository;
import com.redberyl.invoiceapp.service.AttendanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Month;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class AttendanceServiceImpl implements AttendanceService {

    @Autowired
    private CandidateAttendanceRepository attendanceRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @Override
    public AttendanceDto createOrUpdateAttendance(AttendanceDto attendanceDto) {
        // Validate candidate exists
        Candidate candidate = candidateRepository.findById(attendanceDto.getCandidateId())
                .orElseThrow(() -> new ResourceNotFoundException("Candidate not found with id: " + attendanceDto.getCandidateId()));

        // Check if attendance record already exists
        Optional<CandidateAttendance> existingAttendance = attendanceRepository
                .findByCandidateIdAndMonthAndYear(attendanceDto.getCandidateId(), attendanceDto.getMonth(), attendanceDto.getYear());

        CandidateAttendance attendance;
        if (existingAttendance.isPresent()) {
            // Update existing record
            attendance = existingAttendance.get();
            attendance.setDaysWorked(attendanceDto.getDaysWorked());
            attendance.setDailyRate(attendanceDto.getDailyRate());
            attendance.setNotes(attendanceDto.getNotes());
        } else {
            // Create new record
            attendance = CandidateAttendance.builder()
                    .candidate(candidate)
                    .month(attendanceDto.getMonth())
                    .year(attendanceDto.getYear())
                    .daysWorked(attendanceDto.getDaysWorked())
                    .dailyRate(attendanceDto.getDailyRate())
                    .notes(attendanceDto.getNotes())
                    .build();
        }

        CandidateAttendance savedAttendance = attendanceRepository.save(attendance);
        return convertToDto(savedAttendance);
    }

    @Override
    public AttendanceDto getAttendanceById(Long id) {
        CandidateAttendance attendance = attendanceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Attendance record not found with id: " + id));
        return convertToDto(attendance);
    }

    @Override
    public AttendanceDto getAttendanceByCandidateMonthYear(Long candidateId, Integer month, Integer year) {
        CandidateAttendance attendance = attendanceRepository
                .findByCandidateIdAndMonthAndYear(candidateId, month, year)
                .orElseThrow(() -> new ResourceNotFoundException(
                        String.format("Attendance record not found for candidate %d in %d/%d", candidateId, month, year)));
        return convertToDto(attendance);
    }

    @Override
    public List<AttendanceDto> getAttendanceByCandidate(Long candidateId) {
        List<CandidateAttendance> attendanceList = attendanceRepository.findByCandidateIdOrderByYearDescMonthDesc(candidateId);
        return attendanceList.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    public List<AttendanceDto> getAttendanceByMonthYear(Integer month, Integer year) {
        List<CandidateAttendance> attendanceList = attendanceRepository.findAttendanceWithCandidateDetails(month, year);
        return attendanceList.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    public List<AttendanceDto> getAttendanceByCandidateYear(Long candidateId, Integer year) {
        List<CandidateAttendance> attendanceList = attendanceRepository.findByCandidateIdAndYearOrderByMonthDesc(candidateId, year);
        return attendanceList.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    public void deleteAttendance(Long id) {
        if (!attendanceRepository.existsById(id)) {
            throw new ResourceNotFoundException("Attendance record not found with id: " + id);
        }
        attendanceRepository.deleteById(id);
    }

    @Override
    public AttendanceDto calculateSalary(Long candidateId, Integer month, Integer year, Integer daysWorked) {
        Candidate candidate = candidateRepository.findById(candidateId)
                .orElseThrow(() -> new ResourceNotFoundException("Candidate not found with id: " + candidateId));

        // Calculate daily rate from monthly salary (assuming 30 days per month)
        BigDecimal dailyRate = candidate.getSalaryOffered() != null ? 
                candidate.getSalaryOffered().divide(BigDecimal.valueOf(30), 2, BigDecimal.ROUND_HALF_UP) : 
                BigDecimal.ZERO;

        AttendanceDto attendanceDto = AttendanceDto.builder()
                .candidateId(candidateId)
                .month(month)
                .year(year)
                .daysWorked(daysWorked)
                .dailyRate(dailyRate)
                .calculatedSalary(dailyRate.multiply(BigDecimal.valueOf(daysWorked)))
                .build();

        return createOrUpdateAttendance(attendanceDto);
    }

    @Override
    public AttendanceDto getLatestAttendanceByCandidate(Long candidateId) {
        Optional<CandidateAttendance> latestAttendance = attendanceRepository.findLatestAttendanceByCandidateId(candidateId);
        return latestAttendance.map(this::convertToDto).orElse(null);
    }

    @Override
    public List<AttendanceDto> getAllAttendance() {
        List<CandidateAttendance> attendanceList = attendanceRepository.findAll();
        return attendanceList.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    private AttendanceDto convertToDto(CandidateAttendance attendance) {
        return AttendanceDto.builder()
                .id(attendance.getId())
                .candidateId(attendance.getCandidate().getId())
                .candidateName(attendance.getCandidate().getName())
                .month(attendance.getMonth())
                .year(attendance.getYear())
                .daysWorked(attendance.getDaysWorked())
                .dailyRate(attendance.getDailyRate())
                .calculatedSalary(attendance.getCalculatedSalary())
                .notes(attendance.getNotes())
                .monthName(Month.of(attendance.getMonth()).name())
                .clientName(attendance.getCandidate().getClient() != null ? attendance.getCandidate().getClient().getName() : null)
                .projectName(attendance.getCandidate().getProject() != null ? attendance.getCandidate().getProject().getName() : null)
                .createdAt(attendance.getCreatedAt())
                .updatedAt(attendance.getUpdatedAt())
                .build();
    }
}
