-- Attendance Management Schema
CREATE TABLE candidate_attendance (
    id BIGSERIAL PRIMARY KEY,
    candidate_id BIGINT NOT NULL REFERENCES candidates(id) ON DELETE CASCADE,
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    year INTEGER NOT NULL CHECK (year >= 2020),
    days_worked INTEGER NOT NULL DEFAULT 0 CHECK (days_worked >= 0 AND days_worked <= 31),
    daily_rate NUMERIC(10,2) NOT NULL DEFAULT 0.00,
    calculated_salary NUMERIC(12,2) NOT NULL DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(candidate_id, month, year)
);

-- Add attendance_days field to invoices table for tracking attendance-based billing
ALTER TABLE invoices ADD COLUMN attendance_days INTEGER DEFAULT NULL;
ALTER TABLE invoices ADD COLUMN attendance_id BIGINT REFERENCES candidate_attendance(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX idx_candidate_attendance_candidate_id ON candidate_attendance(candidate_id);
CREATE INDEX idx_candidate_attendance_month_year ON candidate_attendance(month, year);
CREATE INDEX idx_invoices_attendance_id ON invoices(attendance_id);

-- Add trigger to automatically update calculated_salary when days_worked or daily_rate changes
CREATE OR REPLACE FUNCTION update_calculated_salary()
RETURNS TRIGGER AS $$
BEGIN
    NEW.calculated_salary = NEW.days_worked * NEW.daily_rate;
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_calculated_salary
    BEFORE INSERT OR UPDATE ON candidate_attendance
    FOR EACH ROW
    EXECUTE FUNCTION update_calculated_salary();
