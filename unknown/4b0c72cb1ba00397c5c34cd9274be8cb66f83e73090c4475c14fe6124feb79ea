package com.redberyl.invoiceapp.service.impl;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.dto.RedberylAccountDto;
import com.redberyl.invoiceapp.dto.SpocDto;
import com.redberyl.invoiceapp.entity.Bdm;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.Project;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.entity.Spoc;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.InvoiceGenerationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * Implementation of the InvoiceGenerationService
 */
@Service
public class InvoiceGenerationServiceImpl implements InvoiceGenerationService {

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private InvoiceRepository invoiceRepository;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy");
    private static final NumberFormat CURRENCY_FORMATTER = NumberFormat.getCurrencyInstance(Locale.US);

    @Override
    public Resource generatePdfInvoice(Invoice invoice) {
        try {
            String htmlContent = generateHtmlInvoice(invoice);
            return convertHtmlToPdfResource(htmlContent);
        } catch (Exception e) {
            throw new CustomException("Failed to generate PDF invoice", e);
        }
    }

    @Override
    public Resource generatePdfInvoiceFromDto(InvoiceDto invoiceDto) {
        try {
            String htmlContent = generateHtmlInvoiceFromDto(invoiceDto);
            return convertHtmlToPdfResource(htmlContent);
        } catch (Exception e) {
            throw new CustomException("Failed to generate PDF invoice from DTO", e);
        }
    }

    @Override
    public String generateHtmlInvoice(Invoice invoice) {
        Context context = new Context();
        populateContextFromInvoice(context, invoice);
        return templateEngine.process("invoice-template", context);
    }

    @Override
    public String generateHtmlInvoiceFromDto(InvoiceDto invoiceDto) {
        Context context = new Context();
        populateContextFromDto(context, invoiceDto);
        return templateEngine.process("invoice-template", context);
    }

    /**
     * Convert HTML content to a PDF Resource
     */
    private Resource convertHtmlToPdfResource(String htmlContent) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdf = new PdfDocument(writer);
        ConverterProperties properties = new ConverterProperties();

        HtmlConverter.convertToPdf(htmlContent, pdf, properties);

        byte[] bytes = outputStream.toByteArray();
        return new ByteArrayResource(bytes);
    }

    /**
     * Populate the Thymeleaf context with data from an Invoice entity
     */
    private void populateContextFromInvoice(Context context, Invoice invoice) {
        // Company information
        context.setVariable("companyName", "RedBeryl Tech Solutions");
        context.setVariable("companyAddress", "507-B Amanora Chambers");
        context.setVariable("companyCity", "Amanora Mall, Hadapsar, Pune 411028");
        context.setVariable("companyPhone", "+91 **********");

        // Invoice details
        context.setVariable("invoiceNumber", invoice.getInvoiceNumber());
        context.setVariable("invoiceDate", invoice.getInvoiceDate().format(DATE_FORMATTER));

        // Due date
        if (invoice.getDueDate() != null) {
            context.setVariable("dueDate", invoice.getDueDate().format(DATE_FORMATTER));
        }

        // Client information - include all client data
        if (invoice.getClient() != null) {
            Client client = invoice.getClient();
            // Set the entire client object
            context.setVariable("client", client);

            // Set individual client fields for direct access in template
            context.setVariable("clientName", client.getName());
            context.setVariable("clientId", client.getId());

            // Add all client fields that might be available
            try {
                // Use reflection to get all fields from Client
                java.lang.reflect.Field[] fields = Client.class.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    // Skip collections to avoid circular references
                    if (fieldName.equals("projects") || fieldName.equals("invoices") || fieldName.equals("deals")) {
                        continue;
                    }
                    Object value = field.get(client);
                    if (value != null) {
                        context.setVariable("client" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                    }
                }
                System.out.println("Added all client fields to template context");
            } catch (Exception e) {
                System.err.println("Error adding client fields: " + e.getMessage());
            }
        }

        // Project information - include all project data
        if (invoice.getProject() != null) {
            Project project = invoice.getProject();
            // Set the entire project object
            context.setVariable("project", project);

            // Set basic project fields
            context.setVariable("projectName", project.getName());
            context.setVariable("projectId", project.getId());
            context.setVariable("projectDescription", project.getDescription());
            context.setVariable("projectEmail", project.getEmail());
            context.setVariable("projectPhone", project.getPhone());
            context.setVariable("projectGstNumber", project.getGstNumber());
            context.setVariable("projectBillingAddress", project.getBillingAddress());
            context.setVariable("projectShippingAddress", project.getShippingAddress());
            context.setVariable("projectEngagementCode", project.getEngagementCode());
            context.setVariable("projectClientPartnerName", project.getClientPartnerName());
            context.setVariable("projectClientPartnerEmail", project.getClientPartnerEmail());
            context.setVariable("projectClientPartnerPhone", project.getClientPartnerPhone());

            // Format date fields
            context.setVariable("projectStartDate", project.getStartDate() != null ? project.getStartDate().format(DATE_FORMATTER) : "");
            context.setVariable("projectEndDate", project.getEndDate() != null ? project.getEndDate().format(DATE_FORMATTER) : "");

            // Other fields
            context.setVariable("projectStatus", project.getStatus());
            context.setVariable("projectValue", project.getValue() != null ? CURRENCY_FORMATTER.format(project.getValue()) : "");

            // HSN Code information
            if (project.getHsnCode() != null) {
                context.setVariable("hsnCode", project.getHsnCode().getCode());
                context.setVariable("hsnDescription", project.getHsnCode().getDescription());
                // Add the full HSN code object
                context.setVariable("hsnCodeObject", project.getHsnCode());
            }

            // BDM information
            if (project.getBdm() != null) {
                Bdm bdm = project.getBdm();
                context.setVariable("bdmName", bdm.getName());
                context.setVariable("bdmEmail", bdm.getEmail());
                context.setVariable("bdmPhone", bdm.getPhone());
                // Add the full BDM object
                context.setVariable("bdm", bdm);

                // Add all BDM fields
                try {
                    java.lang.reflect.Field[] fields = Bdm.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object value = field.get(bdm);
                        if (value != null) {
                            context.setVariable("bdm" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error adding BDM fields: " + e.getMessage());
                }
            }

            // Add all remaining project fields using reflection
            try {
                java.lang.reflect.Field[] fields = Project.class.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    // Skip collections and fields we've already handled explicitly
                    if (fieldName.equals("candidates") || fieldName.equals("invoices") ||
                        fieldName.equals("id") || fieldName.equals("name") ||
                        fieldName.equals("description") || fieldName.equals("email") ||
                        fieldName.equals("phone") || fieldName.equals("gstNumber") ||
                        fieldName.equals("billingAddress") || fieldName.equals("shippingAddress") ||
                        fieldName.equals("engagementCode") || fieldName.equals("startDate") ||
                        fieldName.equals("endDate") || fieldName.equals("status") ||
                        fieldName.equals("value") || fieldName.equals("hsnCode") ||
                        fieldName.equals("bdm") || fieldName.equals("client") ||
                        fieldName.equals("clientPartnerName") || fieldName.equals("clientPartnerEmail") ||
                        fieldName.equals("clientPartnerPhone")) {
                        continue;
                    }

                    Object value = field.get(project);
                    if (value != null) {
                        context.setVariable("project" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                    }
                }
                System.out.println("Added all project fields to template context");
            } catch (Exception e) {
                System.err.println("Error adding project fields: " + e.getMessage());
            }

            // SPOC information - include full objects
            if (project.getManagerSpoc() != null) {
                Spoc managerSpoc = project.getManagerSpoc();
                context.setVariable("managerSpoc", managerSpoc);
                context.setVariable("managerSpocName", managerSpoc.getName());
                context.setVariable("managerSpocEmail", managerSpoc.getEmailId());
                context.setVariable("managerSpocPhone", managerSpoc.getContactNo());

                // Add all fields from the SPOC object
                try {
                    java.lang.reflect.Field[] fields = Spoc.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object value = field.get(managerSpoc);
                        if (value != null) {
                            context.setVariable("managerSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error adding manager SPOC fields: " + e.getMessage());
                }
            }

            if (project.getAccountHeadSpoc() != null) {
                Spoc accountHeadSpoc = project.getAccountHeadSpoc();
                context.setVariable("accountHeadSpoc", accountHeadSpoc);
                context.setVariable("accountHeadSpocName", accountHeadSpoc.getName());
                context.setVariable("accountHeadSpocEmail", accountHeadSpoc.getEmailId());
                context.setVariable("accountHeadSpocPhone", accountHeadSpoc.getContactNo());

                // Add all fields from the SPOC object
                try {
                    java.lang.reflect.Field[] fields = Spoc.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object value = field.get(accountHeadSpoc);
                        if (value != null) {
                            context.setVariable("accountHeadSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error adding account head SPOC fields: " + e.getMessage());
                }
            }

            if (project.getBusinessHeadSpoc() != null) {
                Spoc businessHeadSpoc = project.getBusinessHeadSpoc();
                context.setVariable("businessHeadSpoc", businessHeadSpoc);
                context.setVariable("businessHeadSpocName", businessHeadSpoc.getName());
                context.setVariable("businessHeadSpocEmail", businessHeadSpoc.getEmailId());
                context.setVariable("businessHeadSpocPhone", businessHeadSpoc.getContactNo());

                // Add all fields from the SPOC object
                try {
                    java.lang.reflect.Field[] fields = Spoc.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object value = field.get(businessHeadSpoc);
                        if (value != null) {
                            context.setVariable("businessHeadSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error adding business head SPOC fields: " + e.getMessage());
                }
            }

            if (project.getHrSpoc() != null) {
                Spoc hrSpoc = project.getHrSpoc();
                context.setVariable("hrSpoc", hrSpoc);
                context.setVariable("hrSpocName", hrSpoc.getName());
                context.setVariable("hrSpocEmail", hrSpoc.getEmailId());
                context.setVariable("hrSpocPhone", hrSpoc.getContactNo());

                // Add all fields from the SPOC object
                try {
                    java.lang.reflect.Field[] fields = Spoc.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object value = field.get(hrSpoc);
                        if (value != null) {
                            context.setVariable("hrSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error adding HR SPOC fields: " + e.getMessage());
                }
            }

            if (project.getFinanceSpoc() != null) {
                Spoc financeSpoc = project.getFinanceSpoc();
                context.setVariable("financeSpoc", financeSpoc);
                context.setVariable("financeSpocName", financeSpoc.getName());
                context.setVariable("financeSpocEmail", financeSpoc.getEmailId());
                context.setVariable("financeSpocPhone", financeSpoc.getContactNo());

                // Add all fields from the SPOC object
                try {
                    java.lang.reflect.Field[] fields = Spoc.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object value = field.get(financeSpoc);
                        if (value != null) {
                            context.setVariable("financeSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error adding finance SPOC fields: " + e.getMessage());
                }
            }
        }

        // Candidate information - include all fields
        if (invoice.getCandidate() != null) {
            Candidate candidate = invoice.getCandidate();
            // Set the entire candidate object
            context.setVariable("candidate", candidate);

            // Set basic candidate fields
            context.setVariable("candidateName", candidate.getName());
            context.setVariable("candidateId", candidate.getId());

            // Format date fields
            context.setVariable("candidateJoiningDate", candidate.getJoiningDate() != null ? candidate.getJoiningDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")) : "");

            // Format currency fields
            context.setVariable("candidateBillingRate", candidate.getBillingRate() != null ? CURRENCY_FORMATTER.format(candidate.getBillingRate()) : "");
            context.setVariable("candidateSalaryOffered", candidate.getSalaryOffered() != null ? CURRENCY_FORMATTER.format(candidate.getSalaryOffered()) : "");

            // Set standard fields
            context.setVariable("candidateDesignation", candidate.getDesignation());
            context.setVariable("candidatePanNo", candidate.getPanNo());
            context.setVariable("candidateAadharNo", candidate.getAadharNo());
            context.setVariable("candidateUanNo", candidate.getUanNo());
            context.setVariable("candidateExperienceInYrs", candidate.getExperienceInYrs());
            context.setVariable("candidateBankAccountNo", candidate.getBankAccountNo());
            context.setVariable("candidateBranchName", candidate.getBranchName());
            context.setVariable("candidateIfscCode", candidate.getIfscCode());
            context.setVariable("candidateAddress", candidate.getAddress());

            // Add all remaining candidate fields using reflection
            try {
                java.lang.reflect.Field[] fields = Candidate.class.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    // Skip fields we've already handled explicitly and collections
                    if (fieldName.equals("id") || fieldName.equals("name") ||
                        fieldName.equals("joiningDate") || fieldName.equals("billingRate") ||
                        fieldName.equals("designation") || fieldName.equals("panNo") ||
                        fieldName.equals("aadharNo") || fieldName.equals("uanNo") ||
                        fieldName.equals("experienceInYrs") || fieldName.equals("bankAccountNo") ||
                        fieldName.equals("branchName") || fieldName.equals("ifscCode") ||
                        fieldName.equals("address") || fieldName.equals("salaryOffered") ||
                        fieldName.equals("project") || fieldName.equals("invoices")) {
                        continue;
                    }

                    Object value = field.get(candidate);
                    if (value != null) {
                        context.setVariable("candidate" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                    }
                }
                System.out.println("Added all candidate fields to template context");
            } catch (Exception e) {
                System.err.println("Error adding candidate fields: " + e.getMessage());
            }
        }

        // Financial details
        context.setVariable("billingAmount", CURRENCY_FORMATTER.format(invoice.getBillingAmount()));
        context.setVariable("taxAmount", CURRENCY_FORMATTER.format(invoice.getTaxAmount()));
        context.setVariable("totalAmount", CURRENCY_FORMATTER.format(invoice.getTotalAmount()));

        // No payments/credits field as requested

        // Additional information
        if (invoice.getInvoiceType() != null) {
            context.setVariable("invoiceType", invoice.getInvoiceType().getInvoiceType());
        }

        if (invoice.getStaffingType() != null) {
            context.setVariable("staffingType", invoice.getStaffingType().getName());
        }

        // HSN Code information
        if (invoice.getHsnCode() != null) {
            context.setVariable("hsnCode", invoice.getHsnCode().getCode());
            context.setVariable("hsnDescription", invoice.getHsnCode().getDescription());
        }

        // Redberyl Account information
        if (invoice.getRedberylAccount() != null) {
            RedberylAccount redberylAccount = invoice.getRedberylAccount();
            // Set the entire redberyl account object
            context.setVariable("redberylAccount", redberylAccount);

            // Set standard fields
            context.setVariable("redberylAccountName", redberylAccount.getAccountName());
            context.setVariable("redberylAccountBankName", redberylAccount.getBankName());
            context.setVariable("redberylAccountNumber", redberylAccount.getAccountNo());
            context.setVariable("redberylAccountIfscCode", redberylAccount.getIfscCode());
            context.setVariable("redberylAccountBranchName", redberylAccount.getBranchName());
            context.setVariable("redberylAccountGstn", redberylAccount.getGstn());
            context.setVariable("redberylAccountCin", redberylAccount.getCin());
            context.setVariable("redberylAccountPanNo", redberylAccount.getPanNo());

            // Add all remaining redberyl account fields using reflection
            try {
                java.lang.reflect.Field[] fields = RedberylAccount.class.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    // Skip fields we've already handled explicitly and collections
                    if (fieldName.equals("accountName") || fieldName.equals("bankName") ||
                        fieldName.equals("accountNo") || fieldName.equals("ifscCode") ||
                        fieldName.equals("branchName") || fieldName.equals("gstn") ||
                        fieldName.equals("cin") || fieldName.equals("panNo") ||
                        fieldName.equals("invoices")) {
                        continue;
                    }

                    Object value = field.get(redberylAccount);
                    if (value != null) {
                        context.setVariable("redberylAccount" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                    }
                }
                System.out.println("Added all redberyl account fields to template context");
            } catch (Exception e) {
                System.err.println("Error adding redberyl account fields: " + e.getMessage());
            }
        }

        // Payment terms removed as requested
    }

    /**
     * Populate the Thymeleaf context with data from an InvoiceDto
     */
    private void populateContextFromDto(Context context, InvoiceDto invoiceDto) {
        try {
            // Company information
            context.setVariable("companyName", "RedBeryl Tech Solutions");
            context.setVariable("companyAddress", "507-B Amanora Chambers");
            context.setVariable("companyCity", "Amanora Mall, Hadapsar, Pune 411028");
            context.setVariable("companyPhone", "+91 **********");

            // Invoice details
            context.setVariable("invoiceNumber", invoiceDto.getInvoiceNumber());

            // Handle invoice date safely
            try {
                String formattedDate;
                Object dateObj = invoiceDto.getInvoiceDate();

                if (dateObj == null) {
                    // If date is null, use current date
                    formattedDate = LocalDate.now().format(DATE_FORMATTER);
                } else if (dateObj instanceof LocalDate) {
                    // If it's already a LocalDate, format it
                    formattedDate = ((LocalDate) dateObj).format(DATE_FORMATTER);
                } else if (dateObj instanceof String) {
                    // If it's a string, try to parse it as a date
                    try {
                        LocalDate parsedDate = LocalDate.parse((String) dateObj);
                        formattedDate = parsedDate.format(DATE_FORMATTER);
                    } catch (Exception e) {
                        System.err.println("Failed to parse date string: " + dateObj);
                        formattedDate = LocalDate.now().format(DATE_FORMATTER);
                    }
                } else {
                    // For any other type, use current date
                    System.err.println("Unexpected date type: " + dateObj.getClass().getName());
                    formattedDate = LocalDate.now().format(DATE_FORMATTER);
                }

                context.setVariable("invoiceDate", formattedDate);
            } catch (Exception e) {
                System.err.println("Error handling invoice date: " + e.getMessage());
                context.setVariable("invoiceDate", LocalDate.now().format(DATE_FORMATTER));
            }

            // Handle due date safely
            try {
                if (invoiceDto.getDueDate() != null) {
                    Object dueDateObj = invoiceDto.getDueDate();
                    String formattedDueDate;

                    if (dueDateObj instanceof LocalDate) {
                        formattedDueDate = ((LocalDate) dueDateObj).format(DATE_FORMATTER);
                    } else if (dueDateObj instanceof String) {
                        try {
                            LocalDate parsedDate = LocalDate.parse((String) dueDateObj);
                            formattedDueDate = parsedDate.format(DATE_FORMATTER);
                        } catch (Exception e) {
                            System.err.println("Failed to parse due date string: " + dueDateObj);
                            formattedDueDate = null;
                        }
                    } else {
                        formattedDueDate = null;
                    }

                    if (formattedDueDate != null) {
                        context.setVariable("dueDate", formattedDueDate);
                    }
                }
            } catch (Exception e) {
                System.err.println("Error handling due date: " + e.getMessage());
            }

            // We don't have description field in InvoiceDto, so we'll use project name as description
            if (invoiceDto.getProject() != null && invoiceDto.getProject().getName() != null) {
                context.setVariable("description", invoiceDto.getProject().getName());
            }

            // Client information - include all client data
            if (invoiceDto.getClient() != null) {
                ClientDto client = invoiceDto.getClient();
                // Set the entire client object
                context.setVariable("client", client);

                // Set individual client fields for direct access in template
                context.setVariable("clientName", client.getName());
                context.setVariable("clientId", client.getId());

                // Add all client fields that might be available
                try {
                    // Use reflection to get all fields from ClientDto
                    java.lang.reflect.Field[] fields = ClientDto.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object value = field.get(client);
                        if (value != null) {
                            context.setVariable("client" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                    System.out.println("Added all client fields to template context");
                } catch (Exception e) {
                    System.err.println("Error adding client fields: " + e.getMessage());
                }
            }

            // Project information - include all project data
            if (invoiceDto.getProject() != null) {
                ProjectDto project = invoiceDto.getProject();
                // Set the entire project object
                context.setVariable("project", project);

                // Set basic project fields
                context.setVariable("projectName", project.getName());
                context.setVariable("projectId", project.getId());

                // Add all standard project fields
                if (project.getDescription() != null) context.setVariable("projectDescription", project.getDescription());
                if (project.getEmail() != null) context.setVariable("projectEmail", project.getEmail());
                if (project.getPhone() != null) context.setVariable("projectPhone", project.getPhone());
                if (project.getGstNumber() != null) context.setVariable("projectGstNumber", project.getGstNumber());
                if (project.getBillingAddress() != null) context.setVariable("projectBillingAddress", project.getBillingAddress());
                if (project.getShippingAddress() != null) context.setVariable("projectShippingAddress", project.getShippingAddress());
                if (project.getEngagementCode() != null) context.setVariable("projectEngagementCode", project.getEngagementCode());
                if (project.getClientPartnerName() != null) context.setVariable("projectClientPartnerName", project.getClientPartnerName());
                if (project.getClientPartnerEmail() != null) context.setVariable("projectClientPartnerEmail", project.getClientPartnerEmail());
                if (project.getClientPartnerPhone() != null) context.setVariable("projectClientPartnerPhone", project.getClientPartnerPhone());

                // Format date fields
                if (project.getStartDate() != null) {
                    context.setVariable("projectStartDate", project.getStartDate().format(DATE_FORMATTER));
                }

                if (project.getEndDate() != null) {
                    context.setVariable("projectEndDate", project.getEndDate().format(DATE_FORMATTER));
                }

                if (project.getStatus() != null) context.setVariable("projectStatus", project.getStatus());

                if (project.getValue() != null) {
                    context.setVariable("projectValue", CURRENCY_FORMATTER.format(project.getValue()));
                }

                // HSN Code information
                if (project.getHsnCode() != null) {
                    context.setVariable("hsnCode", project.getHsnCode().getCode());
                    context.setVariable("hsnDescription", project.getHsnCode().getDescription());
                    // Add the full HSN code object
                    context.setVariable("hsnCodeObject", project.getHsnCode());
                }

                // BDM information
                if (project.getBdm() != null) {
                    BdmDto bdm = project.getBdm();
                    context.setVariable("bdmName", bdm.getName());
                    context.setVariable("bdmEmail", bdm.getEmail());
                    context.setVariable("bdmPhone", bdm.getPhone());
                    // Add the full BDM object
                    context.setVariable("bdm", bdm);

                    // Add all BDM fields
                    try {
                        java.lang.reflect.Field[] fields = BdmDto.class.getDeclaredFields();
                        for (java.lang.reflect.Field field : fields) {
                            field.setAccessible(true);
                            String fieldName = field.getName();
                            Object value = field.get(bdm);
                            if (value != null) {
                                context.setVariable("bdm" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error adding BDM fields: " + e.getMessage());
                    }
                }

                // SPOC information - include full objects
                if (project.getManagerSpoc() != null) {
                    SpocDto managerSpoc = project.getManagerSpoc();
                    context.setVariable("managerSpoc", managerSpoc);
                    context.setVariable("managerSpocName", managerSpoc.getName());
                    context.setVariable("managerSpocEmail", managerSpoc.getEmailId());
                    context.setVariable("managerSpocPhone", managerSpoc.getContactNo());

                    // Add all fields from the SPOC object
                    try {
                        java.lang.reflect.Field[] fields = SpocDto.class.getDeclaredFields();
                        for (java.lang.reflect.Field field : fields) {
                            field.setAccessible(true);
                            String fieldName = field.getName();
                            Object value = field.get(managerSpoc);
                            if (value != null) {
                                context.setVariable("managerSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error adding manager SPOC fields: " + e.getMessage());
                    }
                }

                if (project.getAccountHeadSpoc() != null) {
                    SpocDto accountHeadSpoc = project.getAccountHeadSpoc();
                    context.setVariable("accountHeadSpoc", accountHeadSpoc);
                    context.setVariable("accountHeadSpocName", accountHeadSpoc.getName());
                    context.setVariable("accountHeadSpocEmail", accountHeadSpoc.getEmailId());
                    context.setVariable("accountHeadSpocPhone", accountHeadSpoc.getContactNo());

                    // Add all fields from the SPOC object
                    try {
                        java.lang.reflect.Field[] fields = SpocDto.class.getDeclaredFields();
                        for (java.lang.reflect.Field field : fields) {
                            field.setAccessible(true);
                            String fieldName = field.getName();
                            Object value = field.get(accountHeadSpoc);
                            if (value != null) {
                                context.setVariable("accountHeadSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error adding account head SPOC fields: " + e.getMessage());
                    }
                }

                if (project.getBusinessHeadSpoc() != null) {
                    SpocDto businessHeadSpoc = project.getBusinessHeadSpoc();
                    context.setVariable("businessHeadSpoc", businessHeadSpoc);
                    context.setVariable("businessHeadSpocName", businessHeadSpoc.getName());
                    context.setVariable("businessHeadSpocEmail", businessHeadSpoc.getEmailId());
                    context.setVariable("businessHeadSpocPhone", businessHeadSpoc.getContactNo());

                    // Add all fields from the SPOC object
                    try {
                        java.lang.reflect.Field[] fields = SpocDto.class.getDeclaredFields();
                        for (java.lang.reflect.Field field : fields) {
                            field.setAccessible(true);
                            String fieldName = field.getName();
                            Object value = field.get(businessHeadSpoc);
                            if (value != null) {
                                context.setVariable("businessHeadSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error adding business head SPOC fields: " + e.getMessage());
                    }
                }

                if (project.getHrSpoc() != null) {
                    SpocDto hrSpoc = project.getHrSpoc();
                    context.setVariable("hrSpoc", hrSpoc);
                    context.setVariable("hrSpocName", hrSpoc.getName());
                    context.setVariable("hrSpocEmail", hrSpoc.getEmailId());
                    context.setVariable("hrSpocPhone", hrSpoc.getContactNo());

                    // Add all fields from the SPOC object
                    try {
                        java.lang.reflect.Field[] fields = SpocDto.class.getDeclaredFields();
                        for (java.lang.reflect.Field field : fields) {
                            field.setAccessible(true);
                            String fieldName = field.getName();
                            Object value = field.get(hrSpoc);
                            if (value != null) {
                                context.setVariable("hrSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error adding HR SPOC fields: " + e.getMessage());
                    }
                }

                if (project.getFinanceSpoc() != null) {
                    SpocDto financeSpoc = project.getFinanceSpoc();
                    context.setVariable("financeSpoc", financeSpoc);
                    context.setVariable("financeSpocName", financeSpoc.getName());
                    context.setVariable("financeSpocEmail", financeSpoc.getEmailId());
                    context.setVariable("financeSpocPhone", financeSpoc.getContactNo());

                    // Add all fields from the SPOC object
                    try {
                        java.lang.reflect.Field[] fields = SpocDto.class.getDeclaredFields();
                        for (java.lang.reflect.Field field : fields) {
                            field.setAccessible(true);
                            String fieldName = field.getName();
                            Object value = field.get(financeSpoc);
                            if (value != null) {
                                context.setVariable("financeSpoc" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error adding finance SPOC fields: " + e.getMessage());
                    }
                }

                // Add any remaining project fields using reflection
                try {
                    java.lang.reflect.Field[] fields = ProjectDto.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        // Skip fields we've already handled explicitly
                        if (fieldName.equals("id") || fieldName.equals("name") ||
                            fieldName.equals("description") || fieldName.equals("email") ||
                            fieldName.equals("phone") || fieldName.equals("gstNumber") ||
                            fieldName.equals("billingAddress") || fieldName.equals("shippingAddress") ||
                            fieldName.equals("engagementCode") || fieldName.equals("startDate") ||
                            fieldName.equals("endDate") || fieldName.equals("status") ||
                            fieldName.equals("value") || fieldName.equals("hsnCode") ||
                            fieldName.equals("bdm") || fieldName.equals("managerSpoc") ||
                            fieldName.equals("accountHeadSpoc") || fieldName.equals("businessHeadSpoc") ||
                            fieldName.equals("hrSpoc") || fieldName.equals("financeSpoc") ||
                            fieldName.equals("clientPartnerName") || fieldName.equals("clientPartnerEmail") ||
                            fieldName.equals("clientPartnerPhone")) {
                            continue;
                        }

                        Object value = field.get(project);
                        if (value != null) {
                            context.setVariable("project" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                    System.out.println("Added all project fields to template context");
                } catch (Exception e) {
                    System.err.println("Error adding project fields: " + e.getMessage());
                }
            }

            // Candidate information - include all fields
            if (invoiceDto.getCandidate() != null) {
                CandidateDto candidate = invoiceDto.getCandidate();
                // Set the entire candidate object
                context.setVariable("candidate", candidate);

                // Set basic candidate fields
                context.setVariable("candidateName", candidate.getName());
                context.setVariable("candidateId", candidate.getId());

                // Format date fields
                if (candidate.getJoiningDate() != null) {
                    context.setVariable("candidateJoiningDate", candidate.getJoiningDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")));
                }

                // Format currency fields
                if (candidate.getBillingRate() != null) {
                    context.setVariable("candidateBillingRate", CURRENCY_FORMATTER.format(candidate.getBillingRate()));
                }

                if (candidate.getSalaryOffered() != null) {
                    context.setVariable("candidateSalaryOffered", CURRENCY_FORMATTER.format(candidate.getSalaryOffered()));
                }

                // Set standard fields
                if (candidate.getDesignation() != null) context.setVariable("candidateDesignation", candidate.getDesignation());
                if (candidate.getPanNo() != null) context.setVariable("candidatePanNo", candidate.getPanNo());
                if (candidate.getAadharNo() != null) context.setVariable("candidateAadharNo", candidate.getAadharNo());
                if (candidate.getUanNo() != null) context.setVariable("candidateUanNo", candidate.getUanNo());
                if (candidate.getExperienceInYrs() != null) context.setVariable("candidateExperienceInYrs", candidate.getExperienceInYrs());
                if (candidate.getBankAccountNo() != null) context.setVariable("candidateBankAccountNo", candidate.getBankAccountNo());
                if (candidate.getBranchName() != null) context.setVariable("candidateBranchName", candidate.getBranchName());
                if (candidate.getIfscCode() != null) context.setVariable("candidateIfscCode", candidate.getIfscCode());
                if (candidate.getAddress() != null) context.setVariable("candidateAddress", candidate.getAddress());

                // Add all remaining candidate fields using reflection
                try {
                    java.lang.reflect.Field[] fields = CandidateDto.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        // Skip fields we've already handled explicitly
                        if (fieldName.equals("id") || fieldName.equals("name") ||
                            fieldName.equals("joiningDate") || fieldName.equals("billingRate") ||
                            fieldName.equals("designation") || fieldName.equals("panNo") ||
                            fieldName.equals("aadharNo") || fieldName.equals("uanNo") ||
                            fieldName.equals("experienceInYrs") || fieldName.equals("bankAccountNo") ||
                            fieldName.equals("branchName") || fieldName.equals("ifscCode") ||
                            fieldName.equals("address") || fieldName.equals("salaryOffered")) {
                            continue;
                        }

                        Object value = field.get(candidate);
                        if (value != null) {
                            context.setVariable("candidate" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                    System.out.println("Added all candidate fields to template context");
                } catch (Exception e) {
                    System.err.println("Error adding candidate fields: " + e.getMessage());
                }
            }

            // Financial details - handle different types safely
            try {
                BigDecimal billingAmount;
                Object billingAmountObj = invoiceDto.getBillingAmount();

                if (billingAmountObj instanceof BigDecimal) {
                    billingAmount = (BigDecimal) billingAmountObj;
                } else if (billingAmountObj instanceof Number) {
                    billingAmount = BigDecimal.valueOf(((Number) billingAmountObj).doubleValue());
                } else if (billingAmountObj instanceof String) {
                    try {
                        billingAmount = new BigDecimal((String) billingAmountObj);
                    } catch (NumberFormatException e) {
                        billingAmount = BigDecimal.ZERO;
                    }
                } else {
                    billingAmount = BigDecimal.ZERO;
                }
                context.setVariable("billingAmount", CURRENCY_FORMATTER.format(billingAmount));
            } catch (Exception e) {
                System.err.println("Error formatting billing amount: " + e.getMessage());
                context.setVariable("billingAmount", CURRENCY_FORMATTER.format(0));
            }

            try {
                BigDecimal taxAmount;
                Object taxAmountObj = invoiceDto.getTaxAmount();

                if (taxAmountObj instanceof BigDecimal) {
                    taxAmount = (BigDecimal) taxAmountObj;
                } else if (taxAmountObj instanceof Number) {
                    taxAmount = BigDecimal.valueOf(((Number) taxAmountObj).doubleValue());
                } else if (taxAmountObj instanceof String) {
                    try {
                        taxAmount = new BigDecimal((String) taxAmountObj);
                    } catch (NumberFormatException e) {
                        taxAmount = BigDecimal.ZERO;
                    }
                } else {
                    taxAmount = BigDecimal.ZERO;
                }
                context.setVariable("taxAmount", CURRENCY_FORMATTER.format(taxAmount));
            } catch (Exception e) {
                System.err.println("Error formatting tax amount: " + e.getMessage());
                context.setVariable("taxAmount", CURRENCY_FORMATTER.format(0));
            }

            try {
                BigDecimal totalAmount;
                Object totalAmountObj = invoiceDto.getTotalAmount();

                if (totalAmountObj instanceof BigDecimal) {
                    totalAmount = (BigDecimal) totalAmountObj;
                } else if (totalAmountObj instanceof Number) {
                    totalAmount = BigDecimal.valueOf(((Number) totalAmountObj).doubleValue());
                } else if (totalAmountObj instanceof String) {
                    try {
                        totalAmount = new BigDecimal((String) totalAmountObj);
                    } catch (NumberFormatException e) {
                        totalAmount = BigDecimal.ZERO;
                    }
                } else {
                    totalAmount = BigDecimal.ZERO;
                }
                context.setVariable("totalAmount", CURRENCY_FORMATTER.format(totalAmount));
            } catch (Exception e) {
                System.err.println("Error formatting total amount: " + e.getMessage());
                context.setVariable("totalAmount", CURRENCY_FORMATTER.format(0));
            }

            // Additional information
            if (invoiceDto.getInvoiceType() != null && invoiceDto.getInvoiceType().getInvoiceType() != null) {
                context.setVariable("invoiceType", invoiceDto.getInvoiceType().getInvoiceType());
            }

            if (invoiceDto.getStaffingType() != null && invoiceDto.getStaffingType().getName() != null) {
                context.setVariable("staffingType", invoiceDto.getStaffingType().getName());
            }

            // HSN Code information
            if (invoiceDto.getHsnCode() != null) {
                context.setVariable("hsnCode", invoiceDto.getHsnCode().getCode());
                context.setVariable("hsnDescription", invoiceDto.getHsnCode().getDescription());
            }

            // Redberyl Account information - include all fields
            if (invoiceDto.getRedberylAccount() != null) {
                RedberylAccountDto redberylAccount = invoiceDto.getRedberylAccount();
                // Set the entire redberyl account object
                context.setVariable("redberylAccount", redberylAccount);

                // Set standard fields
                context.setVariable("redberylAccountName", redberylAccount.getAccountName());
                context.setVariable("redberylAccountBankName", redberylAccount.getBankName());
                context.setVariable("redberylAccountNumber", redberylAccount.getAccountNo());
                context.setVariable("redberylAccountIfscCode", redberylAccount.getIfscCode());
                context.setVariable("redberylAccountBranchName", redberylAccount.getBranchName());
                context.setVariable("redberylAccountGstn", redberylAccount.getGstn());
                context.setVariable("redberylAccountCin", redberylAccount.getCin());
                context.setVariable("redberylAccountPanNo", redberylAccount.getPanNo());

                // Add all remaining redberyl account fields using reflection
                try {
                    java.lang.reflect.Field[] fields = RedberylAccountDto.class.getDeclaredFields();
                    for (java.lang.reflect.Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        // Skip fields we've already handled explicitly
                        if (fieldName.equals("accountName") || fieldName.equals("bankName") ||
                            fieldName.equals("accountNo") || fieldName.equals("ifscCode") ||
                            fieldName.equals("branchName") || fieldName.equals("gstn") ||
                            fieldName.equals("cin") || fieldName.equals("panNo")) {
                            continue;
                        }

                        Object value = field.get(redberylAccount);
                        if (value != null) {
                            context.setVariable("redberylAccount" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), value);
                        }
                    }
                    System.out.println("Added all redberyl account fields to template context");
                } catch (Exception e) {
                    System.err.println("Error adding redberyl account fields: " + e.getMessage());
                }
            }

            // Payment terms removed as requested
        } catch (Exception e) {
            // Log the error but continue with default values
            System.err.println("Error populating context from DTO: " + e.getMessage());
            e.printStackTrace();

            // Set default values for critical fields
            context.setVariable("invoiceNumber", invoiceDto.getInvoiceNumber() != null ? invoiceDto.getInvoiceNumber() : "N/A");
            context.setVariable("invoiceDate", LocalDate.now().format(DATE_FORMATTER));
            context.setVariable("clientName", "Client");
            context.setVariable("billingAmount", CURRENCY_FORMATTER.format(0));
            context.setVariable("taxAmount", CURRENCY_FORMATTER.format(0));
            context.setVariable("totalAmount", CURRENCY_FORMATTER.format(0));
        }
    }
}
